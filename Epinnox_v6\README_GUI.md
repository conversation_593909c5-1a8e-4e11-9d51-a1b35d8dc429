# Epinnox v6 Integrated Trading System GUI

## 🚀 Overview

The Epinnox v6 Integrated Trading System GUI (`epinnox_int.py`) is a comprehensive PyQt5-based interface that provides real-time access to all the optimized features of the Epinnox v6 trading system. This professional Matrix-themed interface integrates dynamic leverage management, ML model predictions, signal hierarchy resolution, and smart position sizing into a unified trading platform.

## ✨ Key Features

### 🎨 Matrix Theme Design
- **Professional Terminal Appearance**: Green-on-black color scheme with Courier New monospace font
- **Color-Coded Decisions**: Green (LONG), Red (SHORT), Yellow (WAIT)
- **Responsive Design**: Proper scaling and layout management
- **Trading Platform Aesthetics**: Similar to professional trading terminals

### 🔧 Core Integration
- **Real-Time Trading System**: Direct integration with `main.py` trading system
- **Dynamic Leverage Management**: Live leverage fetching from HTX exchange
- **ML Model Integration**: Real-time predictions from SVM, Random Forest, and LSTM models
- **Signal Hierarchy**: Intelligent conflict resolution between different analysis sources
- **Smart Position Sizing**: Risk-aware position calculations with leverage consideration

### 📊 Seven Main Tabs

#### 1. 📈 Live Trading Tab
- **Symbol Selection**: Popular crypto pairs (DOGE/USDT, BTC/USDT, ETH/USDT, etc.)
- **Real-Time Analysis**: Execute trading system with live data
- **Decision Display**: Current trading decision with confidence levels
- **ML Model Status**: Individual model predictions and confidence
- **Leverage Analysis**: Max/Recommended/Effective leverage display
- **Signal Hierarchy**: Breakdown of all analysis sources
- **Market Analysis**: Regime, trend strength, volatility metrics
- **Risk Warnings**: Real-time alerts and position sizing
- **Analysis Log**: Timestamped activity log

#### 2. 🔍 Scalping Scanner Tab
- **Multi-Symbol Scanning**: Analyze multiple symbols simultaneously
- **Opportunity Detection**: Identify trading opportunities across markets
- **Results Table**: Comprehensive view of all scanned symbols
- **Real-Time Updates**: Continuous monitoring capabilities

#### 3. 🎯 Manual Trader Tab
- **Order Entry**: Direct order placement interface
- **Position Management**: View and manage open positions
- **Order Management**: Track and cancel pending orders
- **Risk Controls**: Leverage and position size controls

#### 4. 🤖 Auto Trader Tab
- **Automated Trading**: Strategy queue management
- **Execution Control**: Start/stop automated trading
- **Strategy Monitoring**: Track automated strategy performance
- **Activity Log**: Detailed automation logs

#### 5. 📊 Microstructure Tab
- **Order Book Display**: Real-time bid/ask data
- **Market Depth Analysis**: Liquidity and spread metrics
- **Trade Flow**: Recent trades and market activity
- **Liquidity Scoring**: Market impact assessment

#### 6. 📈 Performance Dashboard Tab
- **PnL Tracking**: Total and daily performance metrics
- **Trade Statistics**: Win rate, average trade, drawdown
- **ML Model Performance**: Accuracy and training metrics
- **Trade History**: Detailed transaction records

#### 7. ⚙️ Settings Tab
- **Timeframe Weights**: Configurable 1m, 5m, 15m weights
- **Leverage Management**: Base balance, max risk, conservative mode
- **ML Model Settings**: Confidence thresholds, retrain intervals
- **Signal Hierarchy Weights**: Customizable source weights
- **Settings Persistence**: Save/load/reset functionality
- **System Information**: Real-time system status

## 🚀 Quick Start

### Prerequisites
```bash
pip install PyQt5
```

### Launch the GUI
```bash
python epinnox_int.py
```

### Basic Usage
1. **Select Symbol**: Choose from dropdown (start with DOGE/USDT)
2. **Configure Settings**: Adjust parameters in Settings tab
3. **Analyze Symbol**: Click "ANALYZE SYMBOL" button
4. **Monitor Results**: View real-time analysis across all panels
5. **Enable Auto-Refresh**: For continuous monitoring

## 🔧 Technical Architecture

### Threading Model
- **Non-Blocking Analysis**: QThread-based worker for trading system execution
- **Real-Time Updates**: Timer-based refresh mechanisms
- **UI Responsiveness**: Proper separation of computation and interface

### Data Integration
- **Exchange Connectivity**: Direct integration with HTX exchange data
- **ML Model Pipeline**: Real-time training and prediction display
- **Signal Processing**: Live signal hierarchy resolution
- **Risk Management**: Dynamic leverage and position sizing

### Settings Management
- **Persistent Configuration**: JSON-based settings storage
- **Dynamic Updates**: Real-time parameter application
- **Default Restoration**: Reset to optimal defaults

## 📊 Real-Time Features

### Live Analysis Display
```
Decision: WAIT
Confidence: 75.8%
Max Available Leverage: 3.0x
Recommended Leverage: 2.0x
Effective Leverage: 0.8x
Position Size: 0.00 units ($0.00)
Risk per Trade: $0.00
```

### ML Model Status
| Model | Decision | Confidence |
|-------|----------|------------|
| SVM | WAIT | 54.6% |
| Random Forest | WAIT | 62.1% |
| Ensemble | WAIT | 58.3% |

### Signal Hierarchy
| Source | Decision | Confidence | Weight |
|--------|----------|------------|--------|
| ML Ensemble | WAIT | 58.3% | 0.35 |
| Technical Signals | WAIT | 62.8% | 0.25 |
| Multi-Timeframe | WAIT | 52.0% | 0.20 |
| Market Regime | LONG | 70.0% | 0.20 |

## ⚙️ Configuration Options

### Timeframe Weights
- **1m Weight**: 0.2 (Short-term signals)
- **5m Weight**: 0.3 (Medium-term signals)  
- **15m Weight**: 0.5 (Long-term signals)

### Leverage Management
- **Base Balance**: $50.00 (Account balance)
- **Max Risk per Trade**: 5.0% (Risk limit)
- **Conservative Mode**: Toggle for reduced risk

### ML Model Settings
- **Confidence Threshold**: 0.6 (Minimum confidence)
- **Retrain Interval**: 24 hours (Model refresh)

### Signal Hierarchy Weights
- **ML Ensemble**: 0.35 (Machine learning weight)
- **Technical Signals**: 0.25 (Technical analysis weight)
- **Multi-Timeframe**: 0.20 (Timeframe analysis weight)

## 🎯 Advanced Features

### Auto-Refresh System
- **Configurable Intervals**: 30-second default refresh
- **Background Processing**: Non-blocking continuous analysis
- **Status Monitoring**: Real-time system health

### Error Handling
- **Graceful Degradation**: Continues operation during errors
- **User Notifications**: Clear error messages and warnings
- **Recovery Mechanisms**: Automatic retry and fallback

### Performance Optimization
- **Efficient Updates**: Only refresh changed components
- **Memory Management**: Proper cleanup and resource handling
- **Responsive UI**: Maintains interface responsiveness

## 🔍 Troubleshooting

### Common Issues
1. **PyQt5 Not Found**: Install with `pip install PyQt5`
2. **Module Import Errors**: Ensure running from Epinnox_v6 directory
3. **Exchange Connection**: Check internet connectivity
4. **Settings Not Saving**: Verify write permissions

### Performance Tips
- Start with DOGE/USDT for testing (good liquidity)
- Enable auto-refresh for continuous monitoring
- Monitor Analysis Log for detailed information
- Check Risk Warnings for important alerts

## 📈 Integration Benefits

### Unified Interface
- **Single Point of Control**: All trading functions in one interface
- **Real-Time Monitoring**: Live system status and performance
- **Comprehensive Analysis**: All analysis sources in one view

### Professional Features
- **Risk Management**: Integrated leverage and position sizing
- **Decision Support**: Clear signal hierarchy and reasoning
- **Performance Tracking**: Built-in metrics and logging

### Scalability
- **Multi-Symbol Support**: Scanner for multiple markets
- **Automated Trading**: Queue-based strategy execution
- **Extensible Design**: Easy addition of new features

## 🎉 Conclusion

The Epinnox v6 Integrated Trading System GUI represents a comprehensive solution for professional cryptocurrency trading. With its Matrix-themed design, real-time integration, and advanced features, it provides traders with the tools needed for sophisticated market analysis and risk management.

**Ready to trade smarter with Epinnox v6!** 🚀
