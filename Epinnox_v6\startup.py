"""
Startup Script for Epinnox Trading System
This script initializes the GPU environment and loads the necessary components
"""
import os
import sys
import logging
from pathlib import Path

# Get logger (don't configure here - let main.py handle it)
logger = logging.getLogger(__name__)

def setup_environment():
    """
    Set up the environment for the Epinnox Trading System
    """
    logger.info("Setting up environment for Epinnox Trading System")
    
    # Add the current directory to the Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Set up GPU environment
    try:
        from gpu_integration import setup_gpu_environment, optimize_model_loading
        
        # Set up GPU environment
        gpu_config = setup_gpu_environment()
        
        # Optimize model loading
        optimize_model_loading()
        
        # Print GPU configuration
        if gpu_config['gpu_enabled']:
            logger.info("GPU acceleration enabled")
            logger.info(f"Device: {gpu_config['device']}")
            logger.info(f"Data Type: {gpu_config['dtype']}")
            logger.info(f"Device Name: {gpu_config['device_info']['device_name']}")
            logger.info(f"CUDA Version: {gpu_config['device_info']['cuda_version']}")
        else:
            logger.info("GPU acceleration not available. Using CPU only.")
        
        return gpu_config
    except Exception as e:
        logger.error(f"Error setting up GPU environment: {e}")
        return {
            'gpu_enabled': False,
            'device': 'cpu',
            'dtype': None,
            'device_info': {}
        }

def main():
    """
    Main function to run the startup script
    """
    logger.info("Starting Epinnox Trading System")
    
    # Set up environment
    gpu_config = setup_environment()
    
    # Return GPU configuration
    return gpu_config

if __name__ == "__main__":
    main()
