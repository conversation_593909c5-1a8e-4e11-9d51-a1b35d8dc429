#!/usr/bin/env python3
"""
Test script for the reorganized Epinnox v6 UI
Demonstrates the new UX strategy with role-based organization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from gui.main_window import TradingSystemGUI
import random

def simulate_market_data():
    """Generate simulated market data for testing"""
    return {
        'current_price': 0.3245 + random.uniform(-0.01, 0.01),
        'spread': random.uniform(0.0001, 0.0005),
        'volume_24h': f"{random.uniform(1.0, 2.5):.1f}M",
        'orderbook_pressure': random.uniform(-0.5, 0.5),
        'trend_direction': random.choice(['STRONG BEARISH', 'BEARISH', 'NEUTRAL', 'BULLISH', 'STRONG BULLISH']),
        'trend_strength': random.uniform(-1.0, 1.0),
        'momentum': random.uniform(-1.0, 1.0),
        'confidence': random.randint(30, 90)
    }

def test_reorganized_ui():
    """Test the reorganized UI with simulated data"""
    app = QApplication(sys.argv)
    
    # Create main window
    window = TradingSystemGUI()
    window.show()
    
    # Set up data simulation timer
    def update_data():
        data = simulate_market_data()
        
        # Update market info panel
        if hasattr(window, 'market_info_panel'):
            window.market_info_panel.update_data(data)
        
        # Update dock panels
        for dock_name, dock_widget in window.dock_widgets.items():
            panel = dock_widget.widget()
            if hasattr(panel, 'update_data'):
                panel.update_data(data)
    
    # Update every 2 seconds for testing
    timer = QTimer()
    timer.timeout.connect(update_data)
    timer.start(2000)
    
    # Initial data update
    update_data()
    
    print("🎉 Reorganized UI Test Started!")
    print("📊 Features being tested:")
    print("  ✅ Top Navigation Bar with critical controls")
    print("  ✅ Main Chart with indicators row and compact market info")
    print("  ✅ Left Column: Trend System + Signal Scoring")
    print("  ✅ Right Column: Market Intelligence")
    print("  ✅ Bottom: Risk & Execution + Logs & Analytics")
    print("  ✅ Real-time data updates every 2 seconds")
    print("  ✅ Matrix theme with professional styling")
    print("\n🔧 UX Improvements:")
    print("  ✅ Role-based panel organization")
    print("  ✅ Compact information display")
    print("  ✅ Hover tooltips and collapsible sections")
    print("  ✅ Visual indicators and progress bars")
    print("  ✅ Consistent Matrix theme styling")
    print("\n🎯 Test the following features:")
    print("  🔄 Symbol selector dropdown")
    print("  🔄 Live data toggle")
    print("  🔄 Timeframe selector")
    print("  🔄 Theme switcher")
    print("  🔄 Panel dragging and resizing")
    print("  🔄 Real-time data updates")
    print("  🔄 Collapsible sections")
    print("\n⚡ Press Ctrl+C to exit")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_reorganized_ui()
