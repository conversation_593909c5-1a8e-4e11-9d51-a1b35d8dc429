"""
GUI Data Manager
Manages data flow between the trading system and GUI components
"""
import json
import logging
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class GUIDataManager:
    """
    Manages data collection and distribution for the GUI
    Interfaces with the trading system to get real-time data
    """
    
    def __init__(self):
        self.data_cache = {}
        self.data_lock = threading.RLock()
        self.last_update = None
        
        # File paths for data exchange
        self.data_file = Path("gui_data.json")
        self.status_file = Path("system_status.json")
        
        # Initialize data structure
        self.init_data_structure()
        
        logger.info("GUI Data Manager initialized")
    
    def init_data_structure(self):
        """Initialize the data structure with default values"""
        self.data_cache = {
            'timestamp': datetime.now().isoformat(),
            'market_data': {
                'symbol': '--',
                'price': 0.0,
                'volume': 0.0,
                'change_24h': 0.0,
                'change_percent': 0.0,
                'high_24h': 0.0,
                'low_24h': 0.0,
                'order_book': {
                    'bids': [],
                    'asks': [],
                    'spread': 0.0,
                    'imbalance': 0.0
                },
                'recent_trades': []
            },
            'timeframe_analysis': {
                'timeframes': ['1m', '5m', '15m'],
                'trends': {
                    '1m': {'direction': 'neutral', 'strength': 0.0},
                    '5m': {'direction': 'neutral', 'strength': 0.0},
                    '15m': {'direction': 'neutral', 'strength': 0.0}
                },
                'alignment_percentage': 0.0,
                'overall_trend': 'neutral',
                'trend_strength': 0.0
            },
            'signal_scoring': {
                'individual_scores': {
                    'macd': 0.0,
                    'order_book': 0.0,
                    'volume': 0.0,
                    'price_action': 0.0,
                    'trend': 0.0
                },
                'total_score': 0.0,
                'confidence': 0.0,
                'alignment_percentage': 0.0,
                'signal_strength': 'weak'
            },
            'market_regime': {
                'current_regime': 'unknown',
                'volatility': 0.0,
                'trend_strength': 0.0,
                'adjustments': {
                    'leverage_factor': 1.0,
                    'position_size_factor': 1.0,
                    'stop_loss_factor': 1.0,
                    'take_profit_factor': 1.0,
                    'entry_confidence': 0.5
                }
            },
            'ai_analysis': {
                'decision': 'WAIT',
                'confidence': 0.0,
                'reasoning': 'No analysis available',
                'take_profit': 0.0,
                'stop_loss': 0.0,
                'model_info': 'Mock Model',
                'analysis_timestamp': datetime.now().isoformat()
            },
            'risk_management': {
                'adaptive_stop_loss': 0.0,
                'adaptive_take_profit': 0.0,
                'position_size': 0.0,
                'atr_volatility': 0.0,
                'risk_score': 0.0,
                'max_position_size': 100.0
            },
            'system_status': {
                'connected': False,
                'running': False,
                'last_analysis': '--',
                'next_analysis': '--',
                'uptime': '00:00:00',
                'errors': 0,
                'warnings': 0
            },
            'performance': {
                'total_signals': 0,
                'successful_signals': 0,
                'success_rate': 0.0,
                'avg_confidence': 0.0,
                'total_runtime': '00:00:00',
                'memory_usage': 0.0,
                'cpu_usage': 0.0
            }
        }
    
    def get_latest_data(self) -> Dict[str, Any]:
        """
        Get the latest data for the GUI
        
        Returns:
            Dict containing all current data
        """
        with self.data_lock:
            # Try to read from data files if they exist
            self.read_data_files()
            
            # Update timestamp
            self.data_cache['timestamp'] = datetime.now().isoformat()
            
            return self.data_cache.copy()
    
    def read_data_files(self):
        """Read data from files if they exist"""
        try:
            # Read main data file
            if self.data_file.exists():
                with open(self.data_file, 'r') as f:
                    file_data = json.load(f)
                    self.merge_data(file_data)
            
            # Read status file
            if self.status_file.exists():
                with open(self.status_file, 'r') as f:
                    status_data = json.load(f)
                    if 'system_status' in status_data:
                        self.data_cache['system_status'].update(status_data['system_status'])
                        
        except Exception as e:
            logger.warning(f"Error reading data files: {e}")
    
    def merge_data(self, new_data: Dict[str, Any]):
        """
        Merge new data with existing cache
        
        Args:
            new_data: New data to merge
        """
        for key, value in new_data.items():
            if key in self.data_cache:
                if isinstance(value, dict) and isinstance(self.data_cache[key], dict):
                    self.data_cache[key].update(value)
                else:
                    self.data_cache[key] = value
    
    def update_market_data(self, symbol: str, price: float, volume: float, 
                          change_24h: float, order_book: Dict, trades: list):
        """Update market data"""
        with self.data_lock:
            self.data_cache['market_data'].update({
                'symbol': symbol,
                'price': price,
                'volume': volume,
                'change_24h': change_24h,
                'change_percent': (change_24h / (price - change_24h)) * 100 if price > change_24h else 0,
                'order_book': order_book,
                'recent_trades': trades[-10:] if trades else []  # Keep last 10 trades
            })
    
    def update_timeframe_analysis(self, trends: Dict, alignment: float, overall_trend: str):
        """Update timeframe analysis data"""
        with self.data_lock:
            self.data_cache['timeframe_analysis'].update({
                'trends': trends,
                'alignment_percentage': alignment,
                'overall_trend': overall_trend,
                'trend_strength': sum(abs(t.get('strength', 0)) for t in trends.values()) / len(trends)
            })
    
    def update_signal_scoring(self, scores: Dict, total_score: float, confidence: float):
        """Update signal scoring data"""
        with self.data_lock:
            self.data_cache['signal_scoring'].update({
                'individual_scores': scores,
                'total_score': total_score,
                'confidence': confidence,
                'signal_strength': self.get_signal_strength(confidence)
            })
    
    def update_market_regime(self, regime: str, volatility: float, adjustments: Dict):
        """Update market regime data"""
        with self.data_lock:
            self.data_cache['market_regime'].update({
                'current_regime': regime,
                'volatility': volatility,
                'adjustments': adjustments
            })
    
    def update_ai_analysis(self, decision: str, confidence: float, reasoning: str,
                          take_profit: float, stop_loss: float, model_info: str):
        """Update AI analysis data"""
        with self.data_lock:
            self.data_cache['ai_analysis'].update({
                'decision': decision,
                'confidence': confidence,
                'reasoning': reasoning,
                'take_profit': take_profit,
                'stop_loss': stop_loss,
                'model_info': model_info,
                'analysis_timestamp': datetime.now().isoformat()
            })
    
    def update_risk_management(self, stop_loss: float, take_profit: float,
                              position_size: float, atr: float, risk_score: float):
        """Update risk management data"""
        with self.data_lock:
            self.data_cache['risk_management'].update({
                'adaptive_stop_loss': stop_loss,
                'adaptive_take_profit': take_profit,
                'position_size': position_size,
                'atr_volatility': atr,
                'risk_score': risk_score
            })
    
    def update_system_status(self, connected: bool, running: bool, uptime: str,
                           errors: int, warnings: int):
        """Update system status"""
        with self.data_lock:
            self.data_cache['system_status'].update({
                'connected': connected,
                'running': running,
                'uptime': uptime,
                'errors': errors,
                'warnings': warnings,
                'last_analysis': datetime.now().strftime('%H:%M:%S')
            })
    
    def update_performance(self, total_signals: int, successful_signals: int,
                          runtime: str, memory_usage: float, cpu_usage: float):
        """Update performance metrics"""
        with self.data_lock:
            success_rate = (successful_signals / total_signals * 100) if total_signals > 0 else 0
            
            self.data_cache['performance'].update({
                'total_signals': total_signals,
                'successful_signals': successful_signals,
                'success_rate': success_rate,
                'total_runtime': runtime,
                'memory_usage': memory_usage,
                'cpu_usage': cpu_usage
            })
    
    def get_signal_strength(self, confidence: float) -> str:
        """Get signal strength based on confidence"""
        if confidence >= 80:
            return 'very_strong'
        elif confidence >= 65:
            return 'strong'
        elif confidence >= 50:
            return 'moderate'
        elif confidence >= 35:
            return 'weak'
        else:
            return 'very_weak'
    
    def save_data_to_file(self, filename: str = None):
        """Save current data to file"""
        try:
            filename = filename or self.data_file
            with self.data_lock:
                with open(filename, 'w') as f:
                    json.dump(self.data_cache, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving data to file: {e}")
    
    def load_data_from_file(self, filename: str = None):
        """Load data from file"""
        try:
            filename = filename or self.data_file
            if Path(filename).exists():
                with open(filename, 'r') as f:
                    data = json.load(f)
                    self.merge_data(data)
        except Exception as e:
            logger.error(f"Error loading data from file: {e}")
