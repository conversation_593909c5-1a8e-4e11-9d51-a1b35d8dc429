"""
Live OHLCV Chart Widget
Real-time candlestick chart with Matrix theme support
"""
import sys
import json
from datetime import datetime, timedelta
import numpy as np
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QSpinBox, QCheckBox,
    QFrame, QGroupBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor
import pyqtgraph as pg
from pyqtgraph import PlotWidget, mkPen, mkBrush

class LiveChartWidget(QWidget):
    """Live OHLCV chart with Matrix theme support"""
    
    def __init__(self):
        super().__init__()
        # FIX: Set expanding size policy for proper scaling
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.current_theme = "matrix"
        self.chart_data = []
        self.volume_data = []
        self.init_ui()
        self.setup_chart()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_chart_data)
        self.update_timer.start(5000)  # Update every 5 seconds
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Chart controls
        controls_frame = self.create_controls()
        layout.addWidget(controls_frame)
        
        # Main chart area with proper size policy
        self.chart_widget = PlotWidget()
        self.chart_widget.setMinimumHeight(400)
        self.chart_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # FIX: Chart scaling
        layout.addWidget(self.chart_widget, stretch=1)

        # Volume chart with fixed height
        self.volume_widget = PlotWidget()
        self.volume_widget.setMaximumHeight(120)
        self.volume_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)  # FIX: Volume chart scaling
        layout.addWidget(self.volume_widget)
        
        # Chart info panel
        info_frame = self.create_info_panel()
        layout.addWidget(info_frame)
    
    def create_controls(self):
        """Create chart control panel"""
        controls_frame = QFrame()
        controls_frame.setMaximumHeight(60)
        controls_layout = QHBoxLayout(controls_frame)
        
        # Symbol info
        self.symbol_label = QLabel("DOGE/USDT")
        self.symbol_label.setFont(QFont("Courier New", 12, QFont.Bold))
        controls_layout.addWidget(self.symbol_label)
        
        # Price info
        self.price_label = QLabel("$0.176234")
        self.price_label.setFont(QFont("Courier New", 14, QFont.Bold))
        controls_layout.addWidget(self.price_label)
        
        # Change info
        self.change_label = QLabel("+1.24%")
        self.change_label.setFont(QFont("Courier New", 10, QFont.Bold))
        controls_layout.addWidget(self.change_label)
        
        controls_layout.addStretch()
        
        # Timeframe selector
        controls_layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText("5m")
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        controls_layout.addWidget(self.timeframe_combo)
        
        # Candle count
        controls_layout.addWidget(QLabel("Candles:"))
        self.candle_count = QSpinBox()
        self.candle_count.setRange(50, 500)
        self.candle_count.setValue(100)
        self.candle_count.valueChanged.connect(self.on_candle_count_changed)
        controls_layout.addWidget(self.candle_count)
        
        # Auto-scroll
        self.auto_scroll = QCheckBox("Auto-scroll")
        self.auto_scroll.setChecked(True)
        controls_layout.addWidget(self.auto_scroll)
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_chart)
        controls_layout.addWidget(refresh_btn)
        
        return controls_frame
    
    def create_info_panel(self):
        """Create chart information panel"""
        info_frame = QFrame()
        info_frame.setMaximumHeight(80)
        info_layout = QGridLayout(info_frame)
        
        # OHLCV info
        self.open_label = QLabel("O: --")
        self.high_label = QLabel("H: --")
        self.low_label = QLabel("L: --")
        self.close_label = QLabel("C: --")
        self.volume_label = QLabel("V: --")
        
        info_layout.addWidget(self.open_label, 0, 0)
        info_layout.addWidget(self.high_label, 0, 1)
        info_layout.addWidget(self.low_label, 0, 2)
        info_layout.addWidget(self.close_label, 0, 3)
        info_layout.addWidget(self.volume_label, 0, 4)
        
        # Technical indicators
        self.rsi_label = QLabel("RSI: --")
        self.macd_label = QLabel("MACD: --")
        self.bb_label = QLabel("BB: --")
        self.ema_label = QLabel("EMA: --")
        
        info_layout.addWidget(self.rsi_label, 1, 0)
        info_layout.addWidget(self.macd_label, 1, 1)
        info_layout.addWidget(self.bb_label, 1, 2)
        info_layout.addWidget(self.ema_label, 1, 3)
        
        return info_frame
    
    def setup_chart(self):
        """Setup the chart with Matrix theme"""
        # Configure main chart
        self.chart_widget.setBackground('#000000')
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # Configure volume chart
        self.volume_widget.setBackground('#000000')
        self.volume_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # Apply Matrix theme
        self.apply_matrix_chart_theme()
        
        # Generate initial mock data
        self.generate_mock_data()
        self.plot_chart()
    
    def apply_matrix_chart_theme(self):
        """Apply Matrix theme to charts"""
        # Chart styling
        axis_color = '#00AA00'
        grid_color = '#003300'
        
        # Main chart
        self.chart_widget.getAxis('left').setPen(axis_color)
        self.chart_widget.getAxis('bottom').setPen(axis_color)
        self.chart_widget.getAxis('left').setTextPen(axis_color)
        self.chart_widget.getAxis('bottom').setTextPen(axis_color)
        
        # Volume chart
        self.volume_widget.getAxis('left').setPen(axis_color)
        self.volume_widget.getAxis('bottom').setPen(axis_color)
        self.volume_widget.getAxis('left').setTextPen(axis_color)
        self.volume_widget.getAxis('bottom').setTextPen(axis_color)
    
    def generate_mock_data(self):
        """Generate realistic mock OHLCV data"""
        base_price = 0.176234
        num_candles = self.candle_count.value()
        
        self.chart_data = []
        self.volume_data = []
        
        current_time = datetime.now()
        
        for i in range(num_candles):
            # Generate realistic price movement
            volatility = 0.002
            price_change = np.random.normal(0, volatility)
            
            if i == 0:
                open_price = base_price
            else:
                open_price = self.chart_data[-1]['close']
            
            high_price = open_price * (1 + abs(np.random.normal(0, volatility/2)))
            low_price = open_price * (1 - abs(np.random.normal(0, volatility/2)))
            close_price = open_price * (1 + price_change)
            
            # Ensure high >= max(open, close) and low <= min(open, close)
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            volume = np.random.uniform(50000, 200000)
            
            timestamp = current_time - timedelta(minutes=(num_candles - i) * 5)
            
            candle = {
                'timestamp': timestamp.timestamp(),
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            }
            
            self.chart_data.append(candle)
            self.volume_data.append(volume)
    
    def plot_chart(self):
        """Plot the candlestick chart"""
        if not self.chart_data:
            return
        
        # Clear previous plots
        self.chart_widget.clear()
        self.volume_widget.clear()
        
        # Prepare data for plotting
        timestamps = [candle['timestamp'] for candle in self.chart_data]
        opens = [candle['open'] for candle in self.chart_data]
        highs = [candle['high'] for candle in self.chart_data]
        lows = [candle['low'] for candle in self.chart_data]
        closes = [candle['close'] for candle in self.chart_data]
        volumes = [candle['volume'] for candle in self.chart_data]
        
        # Plot candlesticks (simplified as line chart for now)
        green_pen = mkPen(color='#00FF00', width=2)
        red_pen = mkPen(color='#FF0000', width=2)
        
        # Plot price line
        self.chart_widget.plot(timestamps, closes, pen=green_pen, name="Price")
        
        # Plot volume bars
        volume_brush = mkBrush(color='#00AA00')
        self.volume_widget.plot(timestamps, volumes, pen=None, brush=volume_brush, fillLevel=0)
        
        # Update info labels
        if self.chart_data:
            latest = self.chart_data[-1]
            self.open_label.setText(f"O: {latest['open']:.6f}")
            self.high_label.setText(f"H: {latest['high']:.6f}")
            self.low_label.setText(f"L: {latest['low']:.6f}")
            self.close_label.setText(f"C: {latest['close']:.6f}")
            self.volume_label.setText(f"V: {latest['volume']:,.0f}")
            
            # Update price display
            self.price_label.setText(f"${latest['close']:.6f}")
            
            # Calculate change
            if len(self.chart_data) > 1:
                prev_close = self.chart_data[-2]['close']
                change_pct = ((latest['close'] - prev_close) / prev_close) * 100
                self.change_label.setText(f"{change_pct:+.2f}%")
                
                # Color code change
                if change_pct > 0:
                    self.change_label.setStyleSheet("color: #00FF00;")
                else:
                    self.change_label.setStyleSheet("color: #FF0000;")
    
    def update_chart_data(self):
        """Update chart with new data"""
        # Add new candle
        if self.chart_data:
            last_candle = self.chart_data[-1]
            new_price = last_candle['close'] * (1 + np.random.normal(0, 0.001))
            new_volume = np.random.uniform(50000, 200000)
            
            new_candle = {
                'timestamp': datetime.now().timestamp(),
                'open': last_candle['close'],
                'high': max(last_candle['close'], new_price),
                'low': min(last_candle['close'], new_price),
                'close': new_price,
                'volume': new_volume
            }
            
            self.chart_data.append(new_candle)
            self.volume_data.append(new_volume)
            
            # Keep only recent candles
            max_candles = self.candle_count.value()
            if len(self.chart_data) > max_candles:
                self.chart_data = self.chart_data[-max_candles:]
                self.volume_data = self.volume_data[-max_candles:]
            
            # Replot
            self.plot_chart()
            
            # Auto-scroll if enabled
            if self.auto_scroll.isChecked():
                self.chart_widget.getViewBox().autoRange()
                self.volume_widget.getViewBox().autoRange()
    
    def on_timeframe_changed(self, timeframe):
        """Handle timeframe change"""
        print(f"Timeframe changed to: {timeframe}")
        self.generate_mock_data()
        self.plot_chart()
    
    def on_candle_count_changed(self, count):
        """Handle candle count change"""
        self.generate_mock_data()
        self.plot_chart()
    
    def refresh_chart(self):
        """Refresh chart data"""
        self.generate_mock_data()
        self.plot_chart()
    
    def apply_theme(self, theme_name):
        """Apply theme to the chart"""
        self.current_theme = theme_name
        if theme_name == "matrix":
            self.apply_matrix_theme()
        # Add other themes as needed
    
    def apply_matrix_theme(self):
        """Apply Matrix theme to all components"""
        matrix_style = """
            QWidget {
                background-color: #000000;
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }
            QLabel {
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }
            QPushButton {
                background-color: #002200;
                border: 1px solid #00AA00;
                color: #00FF00;
                padding: 4px 8px;
                border-radius: 3px;
                font-family: 'Courier New', monospace;
            }
            QPushButton:hover {
                background-color: #003300;
            }
            QComboBox {
                background-color: #002200;
                border: 1px solid #00AA00;
                color: #00FF00;
                padding: 2px 4px;
                font-family: 'Courier New', monospace;
            }
            QSpinBox {
                background-color: #002200;
                border: 1px solid #00AA00;
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }
            QCheckBox {
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }
            QFrame {
                background-color: #001100;
                border: 1px solid #00AA00;
            }
        """
        self.setStyleSheet(matrix_style)
        self.apply_matrix_chart_theme()
