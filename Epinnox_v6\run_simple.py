#!/usr/bin/env python3
"""
Epinnox v6 Simple Optimized Launcher

Direct launcher that bypasses dependency checks and runs the optimized system.
"""

import sys
import os
import argparse
from datetime import datetime

def print_banner():
    """Print the optimized system banner"""
    print("=" * 70)
    print("🚀 EPINNOX v6 OPTIMIZED TRADING SYSTEM")
    print("=" * 70)
    print("✅ Intelligent Signal Hierarchy")
    print("✅ Enhanced ML Models (SVM, Random Forest)")
    print("✅ Smart Position Sizing")
    print("✅ Improved Confidence Calculation")
    print("✅ Multi-timeframe Analysis")
    print("✅ Market Regime Detection")
    print("✅ Unicode-safe Logging")
    print("=" * 70)
    print()

def main():
    """Main launcher function"""
    print_banner()
    
    # Parse arguments
    parser = argparse.ArgumentParser(
        description='Launch Epinnox v6 Optimized Trading System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_simple.py --symbol DOGE/USDT
  python run_simple.py --symbol BTC/USDT --continuous --delay 60
  python run_simple.py --symbol ETH/USDT --live
        """
    )
    
    parser.add_argument('--symbol', type=str, default='DOGE/USDT', 
                       help='Trading symbol (default: DOGE/USDT)')
    parser.add_argument('--continuous', action='store_true', 
                       help='Run continuously')
    parser.add_argument('--delay', type=int, default=30, 
                       help='Delay between runs in seconds (default: 30)')
    parser.add_argument('--live', action='store_true', 
                       help='Use live trades data instead of historical OHLCV')
    parser.add_argument('--conservative-sizing', action='store_true',
                       help='Use conservative position sizing mode')
    
    args = parser.parse_args()
    
    # Show configuration
    print(f"📊 CONFIGURATION:")
    print(f"   Symbol: {args.symbol}")
    print(f"   Mode: {'Continuous' if args.continuous else 'Single run'}")
    if args.continuous:
        print(f"   Delay: {args.delay} seconds")
    print(f"   Data: {'Live trades' if args.live else 'Historical OHLCV'}")
    print(f"   Position Sizing: {'Conservative' if args.conservative_sizing else 'Moderate'}")
    print()
    
    # Import and run main system
    try:
        print(f"🚀 Starting optimized trading system at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Set environment variable for position sizing mode
        if args.conservative_sizing:
            os.environ['EPINNOX_CONSERVATIVE_SIZING'] = '1'
        
        # Import main system
        from main import main as run_main
        
        # Override sys.argv to pass arguments to main
        original_argv = sys.argv.copy()
        sys.argv = ['main.py', '--symbol', args.symbol]
        
        if args.continuous:
            sys.argv.extend(['--continuous', '--delay', str(args.delay)])
        
        if args.live:
            sys.argv.append('--live')
        
        # Run the main system
        run_main()
        
        # Restore original argv
        sys.argv = original_argv
        
    except KeyboardInterrupt:
        print("\n🛑 Trading system stopped by user")
    except Exception as e:
        print(f"\n❌ Error running trading system: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
