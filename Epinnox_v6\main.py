"""
Main Application for Trading System
This module connects all components and runs the trading system.
"""
import logging
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import textwrap
from pathlib import Path
import yaml
import os
import sys

# Initialize GPU environment at startup
try:
    from startup import setup_environment
    gpu_config = setup_environment()
    if gpu_config['gpu_enabled']:
        print(f"GPU acceleration enabled: {gpu_config['device_info']['device_name']}\n")
    else:
        print("GPU acceleration not available. Using CPU only.\n")
except Exception as e:
    print(f"Error setting up GPU environment: {e}\n")

from config.config import config
from config import DEFAULT_SYMBOL, DEFAULT_DELAY
from trading.trading_system_interface import TradingSystemInterface
from trading.simulation_interface import SimulationInterface
from core.multi_timeframe import MultiTimeframeAnalyzer
from core.market_regime import MarketRegimeDetector
from core.adaptive_risk import AdaptiveRiskManager
from core.signal_scoring import SignalScorer
from core.prompt import build_prompt, parse_llm_response
from llama.runner import <PERSON>lamaRunner
from ml.models import MLModelManager
from ml.position_sizing import SmartPositionSizer
from data.exchange import ExchangeDataFetcher
from core.features import extract_features, analyze_tick_data, combine_features

# Configure logging
class WrapperFormatter(logging.Formatter):
    def __init__(self, fmt=None, datefmt=None, style='%', width=100):
        super().__init__(fmt, datefmt, style)
        self.width = width

    def format(self, record):
        # Format the message using the parent formatter
        formatted = super().format(record)

        # Wrap the message
        lines = formatted.split('\n')
        wrapped_lines = []

        for line in lines:
            # Find the position of the message part (after the timestamp, level, etc.)
            parts = line.split(' - ', 3)
            if len(parts) >= 4:
                prefix = ' - '.join(parts[:3]) + ' - '
                message = parts[3]

                # Wrap the message part
                wrapped_message = textwrap.fill(message,
                                              width=self.width,
                                              initial_indent='',
                                              subsequent_indent=' ' * len(prefix))

                # Reconstruct the line
                wrapped_lines.append(prefix + wrapped_message)
            else:
                wrapped_lines.append(line)

        return '\n'.join(wrapped_lines)

# Create formatter
formatter = WrapperFormatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    width=100
)

# Configure logging - prevent duplicate handlers
root_logger = logging.getLogger()
root_logger.setLevel(getattr(logging, config.system.log_level))

# Clear any existing handlers to prevent duplicates
if root_logger.handlers:
    root_logger.handlers.clear()

# Add file handler
file_handler = logging.FileHandler(config.system.log_file)
file_handler.setFormatter(formatter)
root_logger.addHandler(file_handler)

# Add console handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
root_logger.addHandler(console_handler)

# Get logger for this module
logger = logging.getLogger(__name__)

# Removed fetch_market_data function - we use ccxt exclusively now

def simulate_tick_data(ohlcv_df, num_ticks=100):
    """
    Simulate tick data based on OHLCV data.
    In a real system, this would be replaced with actual tick data.

    Args:
        ohlcv_df: OHLCV DataFrame
        num_ticks: Number of ticks to simulate

    Returns:
        DataFrame: Simulated tick data
    """
    if ohlcv_df is None or ohlcv_df.empty:
        return pd.DataFrame()

    # Get the last candle
    last_candle = ohlcv_df.iloc[-1]

    # Create a range of prices between the high and low
    price_range = pd.Series(
        [last_candle['low'] + (last_candle['high'] - last_candle['low']) * i / num_ticks
         for i in range(num_ticks)]
    )

    # Add some randomness to the prices
    price_range = price_range + (price_range * 0.001 * (pd.Series(np.random.randn(num_ticks))))

    # Create a DataFrame with the simulated ticks
    ticks = pd.DataFrame({
        'timestamp': [last_candle['datetime'] + timedelta(seconds=i) for i in range(num_ticks)],
        'price': price_range,
        'volume': [last_candle['volume'] / num_ticks * (1 + 0.5 * np.random.randn()) for _ in range(num_ticks)]
    })

    return ticks

def fetch_exchange_data(symbol, use_exchange=False, use_live_data=False, timeframes=None):
    """
    Fetch data from the exchange.

    Args:
        symbol: Trading symbol (e.g., 'DOGE/USDT')
        use_exchange: Whether to use the exchange data fetcher
        use_live_data: Whether to use live trades data instead of historical OHLCV
        timeframes: List of timeframes to fetch (e.g., ['1m', '5m', '15m'])

    Returns:
        tuple: (ohlcv_df, exchange_features, multi_timeframe_data)
    """
    if not use_exchange:
        return None, {}, {}

    try:
        # Initialize exchange data fetcher
        fetcher = ExchangeDataFetcher(exchange_id='htx')

        # Determine spot and futures symbols
        if '/' in symbol:
            # Check if it's already a futures symbol
            if ':' in symbol:
                # It's a futures symbol, extract the spot symbol
                spot_symbol = symbol.split(':')[0]
                futures_symbol = symbol
                logger.info(f"Using futures symbol: {futures_symbol} and spot symbol: {spot_symbol}")
            else:
                # It's a spot symbol, construct the futures symbol
                spot_symbol = symbol
                base, quote = symbol.split('/')
                futures_symbol = f"{base}/{quote}:{quote}"
                logger.info(f"Using spot symbol: {spot_symbol} and futures symbol: {futures_symbol}")
        else:
            # Default to DOGE/USDT if symbol format is not recognized
            spot_symbol = 'DOGE/USDT'
            futures_symbol = 'DOGE/USDT:USDT'
            logger.warning(f"Symbol format not recognized: {symbol}. Using default: {spot_symbol}")

        # Ensure we don't have duplicate quote in futures symbol
        if futures_symbol.endswith(':USDT:USDT'):
            futures_symbol = futures_symbol.replace(':USDT:USDT', ':USDT')
            logger.info(f"Corrected futures symbol format to: {futures_symbol}")

        # Fetch combined data
        data_type = "live trades" if use_live_data else "historical OHLCV"
        logger.info(f"Fetching {data_type} data for {spot_symbol} and {futures_symbol}")

        # SMART ENHANCEMENT: Increase historical data depth for better analysis
        # Use 500 candles for more reliable indicators and pattern recognition
        data_limit = 500  # Increased from 100 for better signal quality

        if use_live_data:
            combined_data = fetcher.fetch_combined_data(
                spot_symbol,
                futures_symbol,
                timeframe='1m',
                limit=data_limit,
                ob_limit=5,
                trades_limit=100,
                include_trades=True
            )
        else:
            combined_data = fetcher.fetch_combined_data(
                spot_symbol,
                futures_symbol,
                timeframe='1m',
                limit=data_limit,
                ob_limit=5
            )

        # Extract features
        exchange_features = fetcher.extract_features_from_combined_data(combined_data)

        # Create OHLCV DataFrame from spot data
        ohlcv_df = None
        if combined_data['spot_ohlcv']:
            ohlcv_df = pd.DataFrame(combined_data['spot_ohlcv'])
            logger.info(f"Fetched {len(ohlcv_df)} rows of exchange data for {spot_symbol}")
        else:
            logger.warning(f"No OHLCV data fetched from exchange for {spot_symbol}")

        # SMART ENHANCEMENT: Fetch multi-timeframe data with optimized limits
        multi_timeframe_data = {}
        if timeframes:
            # Initialize multi-timeframe analyzer
            mt_analyzer = MultiTimeframeAnalyzer(timeframes=timeframes)

            # Use different limits for different timeframes for optimal analysis
            timeframe_limits = {
                '1m': 500,   # 8+ hours of 1-minute data for short-term patterns
                '5m': 300,   # 25+ hours of 5-minute data for intraday trends
                '15m': 200,  # 50+ hours of 15-minute data for swing analysis
                '1h': 168,   # 1 week of hourly data for trend confirmation
                '4h': 84,    # 2 weeks of 4-hour data for major trends
                '1d': 30     # 1 month of daily data for long-term context
            }

            # Fetch enhanced multi-timeframe data
            multi_timeframe_data = {}
            for tf in timeframes:
                limit = timeframe_limits.get(tf, 300)  # Default to 300 if timeframe not specified
                try:
                    tf_data = fetcher.fetch_ohlcv(spot_symbol, timeframe=tf, limit=limit, market_type='spot')
                    if tf_data is not None and not tf_data.empty:
                        multi_timeframe_data[tf] = tf_data
                        logger.info(f"Enhanced fetch: {len(tf_data)} {tf} candles for improved analysis")
                    else:
                        logger.warning(f"No data received for {tf} timeframe")
                except Exception as e:
                    logger.error(f"Error fetching enhanced {tf} data: {e}")
                    # Fallback to original method
                    try:
                        fallback_data = mt_analyzer.fetch_multi_timeframe_data(fetcher, spot_symbol)
                        if tf in fallback_data:
                            multi_timeframe_data[tf] = fallback_data[tf]
                            logger.info(f"Fallback: Using original method for {tf}")
                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed for {tf}: {fallback_error}")

            logger.info(f"Enhanced multi-timeframe data: {len(multi_timeframe_data)} timeframes with improved depth: {list(multi_timeframe_data.keys())}")

        return ohlcv_df, exchange_features, multi_timeframe_data

    except Exception as e:
        logger.exception(f"Error fetching exchange data: {e}")
        return None, {}, {}

def calculate_ml_consensus_boost(ml_predictions: dict, signal_decision: str) -> float:
    """
    Calculate confidence boost based on ML model consensus

    Args:
        ml_predictions: Dictionary of ML model predictions
        signal_decision: Decision from signal scoring

    Returns:
        float: Confidence boost percentage (0-15%)
    """
    try:
        if not ml_predictions or not isinstance(ml_predictions, dict):
            return 0.0

        # Count models that agree with signal decision
        agreeing_models = 0
        total_models = 0
        total_confidence = 0

        for model_name, prediction in ml_predictions.items():
            if model_name == 'ensemble' or not isinstance(prediction, dict):
                continue

            model_direction = prediction.get('direction', 'WAIT')
            model_confidence = prediction.get('direction_confidence', 0)

            total_models += 1
            total_confidence += model_confidence

            if model_direction == signal_decision:
                agreeing_models += 1

        if total_models == 0:
            return 0.0

        # Calculate consensus ratio
        consensus_ratio = agreeing_models / total_models
        avg_confidence = total_confidence / total_models

        # Calculate boost based on consensus and average confidence
        # Maximum boost: 15% for 100% consensus with high confidence
        base_boost = consensus_ratio * 10  # Up to 10% for consensus
        confidence_boost = avg_confidence * 5  # Up to 5% for high confidence

        total_boost = base_boost + confidence_boost

        logger.info(f"ML consensus: {agreeing_models}/{total_models} models agree, "
                   f"avg confidence: {avg_confidence:.3f}, boost: {total_boost:.1f}%")

        return min(15.0, total_boost)  # Cap at 15%

    except Exception as e:
        logger.error(f"Error calculating ML consensus boost: {e}")
        return 0.0

def build_enhanced_prompt_with_ml(features, signal_scores=None, market_regime=None,
                                 multi_timeframe_analysis=None, ml_predictions=None,
                                 position_sizing=None):
    """
    Build enhanced prompt including ML predictions and position sizing
    """
    try:
        # Start with base prompt
        base_prompt = build_prompt(features, signal_scores=signal_scores,
                                 market_regime=market_regime,
                                 multi_timeframe_analysis=multi_timeframe_analysis)

        # Add ML predictions section
        ml_section = ""
        if ml_predictions:
            ml_section = "\n🤖 MACHINE LEARNING PREDICTIONS:\n"

            for model_name, prediction in ml_predictions.items():
                if isinstance(prediction, dict):
                    direction = prediction.get('direction', 'WAIT')
                    confidence = prediction.get('direction_confidence', 0)
                    magnitude = prediction.get('magnitude', 0)

                    if model_name == 'ensemble':
                        ml_section += f"🎯 ENSEMBLE PREDICTION: {direction} (confidence: {confidence:.3f}, magnitude: {magnitude:.4f})\n"
                        if 'model_votes' in prediction:
                            votes = prediction['model_votes']
                            ml_section += f"   Model votes: {votes}\n"
                    else:
                        model_emoji = {"svm": "🔬", "random_forest": "🌲", "lstm": "🧠"}.get(model_name, "🤖")
                        ml_section += f"{model_emoji} {model_name.upper()}: {direction} (conf: {confidence:.3f}, mag: {magnitude:.4f})\n"

        # Add position sizing section
        sizing_section = ""
        if position_sizing:
            sizing_section = "\n💰 SMART POSITION SIZING:\n"
            sizing_section += f"- Optimal size: {position_sizing.get('optimal_position_size', 0):.2f} units\n"
            sizing_section += f"- Position value: ${position_sizing.get('position_usd', 0):.2f}\n"
            sizing_section += f"- Liquidity score: {position_sizing.get('liquidity_score', 0):.3f}\n"
            sizing_section += f"- Market impact: {position_sizing.get('market_impact_estimate', 0):.3f}%\n"

            execution = position_sizing.get('execution_strategy', {})
            if execution.get('type') == 'split_order':
                sizing_section += f"- Execution: Split into {execution.get('splits', 1)} orders\n"

            recommendations = position_sizing.get('recommendations', [])
            if recommendations:
                sizing_section += "- Recommendations:\n"
                for rec in recommendations:
                    sizing_section += f"  {rec}\n"

        # Combine all sections
        enhanced_prompt = base_prompt + ml_section + sizing_section

        # Add enhanced decision guidance
        enhanced_prompt += """
🎯 ENHANCED DECISION FRAMEWORK:
Consider the following in your analysis:
1. ML Model Consensus: Do multiple models agree on direction?
2. Confidence Alignment: Are signal scores and ML predictions aligned?
3. Position Sizing Constraints: Are there liquidity or market impact concerns?
4. Risk-Reward Optimization: Does the setup justify the position size?

Make your decision based on the convergence of traditional indicators, ML predictions, and market microstructure analysis.
"""

        return enhanced_prompt

    except Exception as e:
        logger.error(f"Error building enhanced prompt: {e}")
        # Fallback to basic prompt
        return build_prompt(features, signal_scores=signal_scores,
                          market_regime=market_regime,
                          multi_timeframe_analysis=multi_timeframe_analysis)

def run_trading_system(symbol, use_live_data=False):
    """
    Run the trading system for a given symbol using ccxt exchange data.

    Args:
        symbol: Trading symbol (e.g., 'DOGE/USDT')
        use_live_data: Whether to use live trades data instead of historical OHLCV

    Returns:
        tuple: (decision, explanation, parsed_response)
        parsed_response is a dictionary containing:
            - decision: LONG, SHORT, or WAIT
            - explanation: Explanation for the decision
            - confidence: Confidence level (0-100%)
            - take_profit: Take profit percentage (optional)
            - stop_loss: Stop loss percentage (optional)
            - market_regime: Detected market regime
            - multi_timeframe_analysis: Analysis across multiple timeframes
            - position_scaling: Position scaling strategy
    """
    # Define timeframes for multi-timeframe analysis
    timeframes = ['1m', '5m', '15m']

    # 1. Fetch data from the exchange (ccxt only - no yfinance fallback)
    exchange_df, exchange_features, multi_timeframe_data = fetch_exchange_data(
        symbol, use_exchange=True, use_live_data=use_live_data, timeframes=timeframes
    )

    # 2. Use exchange data exclusively
    if exchange_df is None:
        logger.error(f"Failed to fetch exchange data for {symbol}")
        return "ERROR", "Failed to fetch exchange data", {"decision": "ERROR", "explanation": "Failed to fetch exchange data"}

    ohlcv_df = exchange_df
    logger.info(f"Processing {symbol} data: {len(ohlcv_df)} OHLCV rows, {len(exchange_features)} exchange features")

    # 3. Simulate tick data (in a real system, this would be actual tick data)
    tick_df = simulate_tick_data(ohlcv_df)

    # 4. Extract features from OHLCV data
    ohlcv_features = extract_features(ohlcv_df)

    # 5. Extract features from tick data
    tick_features = analyze_tick_data(tick_df)

    # 6. Combine features
    combined_features = combine_features(ohlcv_features, tick_features)

    # 7. Add exchange features if available
    if exchange_features:
        # Add exchange features that don't conflict with existing features
        for k, v in exchange_features.items():
            if k not in combined_features:
                combined_features[k] = v

    # 8. Perform multi-timeframe analysis
    multi_timeframe_analysis = {}
    trend_strength = 0
    trend_direction = "neutral"
    trend_alignment = 0
    volatility = 1.0

    if multi_timeframe_data and len(multi_timeframe_data) > 0:
        try:
            # Initialize multi-timeframe analyzer
            mt_analyzer = MultiTimeframeAnalyzer(timeframes=timeframes)

            # Calculate indicators for each timeframe
            indicators = mt_analyzer.calculate_indicators(multi_timeframe_data)

            # Analyze trend for each timeframe
            trend_analysis = mt_analyzer.analyze_trend(indicators)

            # Calculate overall trend strength
            trend_metrics = mt_analyzer.calculate_trend_strength(trend_analysis)

            # Store results
            multi_timeframe_analysis = {
                'trend_metrics': trend_metrics,
                'timeframe_analysis': trend_analysis
            }

            # Extract key metrics for market regime detection
            trend_strength = trend_metrics['trend_strength']
            trend_direction = trend_metrics['trend_direction']
            trend_alignment = trend_metrics['trend_alignment']
            volatility = trend_metrics['volatility']

            logger.info(f"Multi-timeframe analysis: {trend_direction} trend, strength: {trend_strength:.2f}, alignment: {trend_alignment:.2f}")
        except Exception as e:
            logger.warning(f"Error in multi-timeframe analysis: {e}. Proceeding with single timeframe.")

    # 9. Detect market regime
    market_regime = {}
    try:
        # Initialize market regime detector
        regime_detector = MarketRegimeDetector()

        # Detect market regime
        regime_result = regime_detector.detect_regime(
            ohlcv_df, trend_strength=trend_strength,
            volatility=volatility, trend_alignment=trend_alignment
        )

        # Store results
        market_regime = regime_result

        logger.info(f"Market regime: {regime_result['primary_regime']} (leverage: {regime_result['adjustments']['leverage_factor']:.2f})")
    except Exception as e:
        logger.warning(f"Error detecting market regime: {e}. Proceeding without regime detection.")

    # 10. MACHINE LEARNING PREDICTIONS
    ml_predictions = {}
    try:
        # Initialize ML model manager
        ml_manager = MLModelManager()

        # Try to load existing models
        models_loaded = ml_manager.load_models()

        # If no models exist or insufficient data, train new models
        if not models_loaded or len(ohlcv_df) >= 200:
            logger.info("Training ML models with current data...")
            training_scores = ml_manager.train_models(ohlcv_df)
            logger.info(f"ML model training scores: {training_scores}")

        # Make predictions with all models
        ml_predictions = ml_manager.predict(ohlcv_df)

        if ml_predictions:
            logger.info("ML Predictions:")
            for model_name, prediction in ml_predictions.items():
                if isinstance(prediction, dict):
                    direction = prediction.get('direction', 'WAIT')
                    confidence = prediction.get('direction_confidence', 0)
                    logger.info(f"  {model_name}: {direction} (confidence: {confidence:.3f})")

    except Exception as e:
        logger.warning(f"ML prediction error: {e}. Continuing without ML predictions.")

    # 11. Calculate signal scores with longer timeframe confirmation
    signal_scores = None
    try:
        # Initialize signal scorer with smoothing and longer timeframe confirmation
        scorer = SignalScorer(smoothing_periods=3, use_longer_timeframe=True)

        # Calculate scores with longer timeframe data if available
        signal_scores = scorer.calculate_scores(ohlcv_df, None)  # We're using our own multi-timeframe analysis now

        # SMART ENHANCEMENT: Enhanced decision criteria with confidence boosting
        decision_from_score, confidence_from_score, alignment = scorer.get_decision(signal_scores)

        # Calculate enhanced confidence based on multiple factors
        enhanced_confidence = confidence_from_score
        confidence_factors = []

        # Factor 1: Multi-timeframe alignment boost
        if multi_timeframe_analysis and trend_alignment > 0.6:
            alignment_boost = min(20, trend_alignment * 30)  # Up to 20% boost for strong alignment
            enhanced_confidence += alignment_boost
            confidence_factors.append(f"MTF alignment: +{alignment_boost:.1f}%")

        # Factor 2: Trend strength boost
        if trend_strength > 0.7:
            strength_boost = min(15, (trend_strength - 0.7) * 50)  # Up to 15% boost for strong trends
            enhanced_confidence += strength_boost
            confidence_factors.append(f"Trend strength: +{strength_boost:.1f}%")

        # Factor 3: Signal score magnitude boost
        total_score = abs(signal_scores.get('total_score', 0))
        if total_score > 0.05:
            score_boost = min(10, (total_score - 0.05) * 100)  # Up to 10% boost for strong signals
            enhanced_confidence += score_boost
            confidence_factors.append(f"Signal magnitude: +{score_boost:.1f}%")

        # Factor 4: Market regime consistency boost
        if market_regime and market_regime.get('primary_regime') in ['trending', 'high_volatility']:
            regime_boost = 8  # 8% boost for favorable market conditions
            enhanced_confidence += regime_boost
            confidence_factors.append(f"Market regime: +{regime_boost}%")

        # Factor 5: ML Model Consensus Boost
        if ml_predictions:
            ml_boost = calculate_ml_consensus_boost(ml_predictions, decision_from_score)
            if ml_boost > 0:
                enhanced_confidence += ml_boost
                confidence_factors.append(f"ML consensus: +{ml_boost:.1f}%")

        # Cap enhanced confidence at 95% to maintain realism
        enhanced_confidence = min(95, enhanced_confidence)

        # Update signal scores with enhanced metrics
        signal_scores['original_confidence'] = confidence_from_score
        signal_scores['enhanced_confidence'] = enhanced_confidence
        signal_scores['confidence_factors'] = confidence_factors

        # Enhance signal scores with multi-timeframe analysis
        if multi_timeframe_analysis:
            signal_scores['trend_strength'] = trend_strength
            signal_scores['trend_direction'] = trend_direction
            signal_scores['trend_alignment'] = trend_alignment
            signal_scores['multi_timeframe_confidence'] = (trend_alignment + 0.5) * 100  # Convert to percentage

        # Smart decision override based on enhanced confidence
        if enhanced_confidence >= 70 and decision_from_score == 'WAIT':
            # Override WAIT decision if we have high confidence from other factors
            if signal_scores.get('total_score', 0) > 0.02:
                decision_from_score = 'LONG'
                logger.info(f"Smart override: WAIT -> LONG due to enhanced confidence ({enhanced_confidence:.1f}%)")
            elif signal_scores.get('total_score', 0) < -0.02:
                decision_from_score = 'SHORT'
                logger.info(f"Smart override: WAIT -> SHORT due to enhanced confidence ({enhanced_confidence:.1f}%)")

        logger.info(f"Enhanced signal analysis: {decision_from_score} (confidence: {confidence_from_score:.1f}% -> {enhanced_confidence:.1f}%, total score: {signal_scores.get('total_score', 0):.3f})")
        if confidence_factors:
            logger.info(f"Confidence boosts: {', '.join(confidence_factors)}")
    except ImportError:
        logger.warning("Signal scoring module not available. Proceeding without signal scores.")
    except Exception as e:
        logger.warning(f"Error calculating signal scores: {e}. Proceeding without signal scores.")

    # 16. Build enhanced prompt for LLaMA with ML predictions
    prompt = build_enhanced_prompt_with_ml(
        combined_features,
        signal_scores=signal_scores,
        market_regime=market_regime.get('primary_regime', 'unknown'),
        multi_timeframe_analysis=multi_timeframe_analysis,
        ml_predictions=ml_predictions,
        position_sizing=position_sizing_result if 'position_sizing_result' in locals() else {}
    )

    # 12. Run LLaMA inference
    llama = LlamaRunner()
    response = llama.run_inference(prompt)

    # 13. Parse the response
    parsed_response = parse_llm_response(response)
    decision = parsed_response['decision']
    explanation = parsed_response['explanation']
    confidence = parsed_response['confidence']
    take_profit = parsed_response['take_profit']
    stop_loss = parsed_response['stop_loss']

    # 14. SMART POSITION SIZING
    position_sizing_result = {}
    try:
        # Initialize smart position sizer
        position_sizer = SmartPositionSizer()

        # Prepare market data for position sizing
        market_data_for_sizing = {
            'order_book': exchange_features.get('order_book', {}),
            'volume_24h': exchange_features.get('volume_24h', 0),
            'current_price': ohlcv_df['close'].iloc[-1] if not ohlcv_df.empty else 0,
            'recent_trades': exchange_features.get('recent_trades', [])
        }

        # Calculate optimal position size
        base_position_size = 100.0  # Base position in units
        account_balance = 500.0     # Account balance in USD
        final_confidence = enhanced_confidence / 100 if 'enhanced_confidence' in locals() else 0.7

        position_sizing_result = position_sizer.calculate_optimal_position_size(
            base_position_size=base_position_size,
            market_data=market_data_for_sizing,
            confidence=final_confidence,
            account_balance=account_balance
        )

        logger.info(f"Smart position sizing: {position_sizing_result['optimal_position_size']:.2f} units "
                   f"(${position_sizing_result['position_usd']:.2f}), "
                   f"liquidity score: {position_sizing_result['liquidity_score']:.3f}")

        # Log recommendations
        for recommendation in position_sizing_result.get('recommendations', []):
            logger.info(f"Position sizing: {recommendation}")

    except Exception as e:
        logger.warning(f"Error calculating smart position sizing: {e}. Using default sizing.")

    # 15. Calculate adaptive risk parameters
    try:
        # Initialize adaptive risk manager
        risk_manager = AdaptiveRiskManager()

        # Get current price
        current_price = ohlcv_df['close'].iloc[-1] if not ohlcv_df.empty else 0

        # Calculate volatility-based stops
        if decision in ['LONG', 'SHORT']:
            adaptive_stops = risk_manager.calculate_volatility_based_stops(
                ohlcv_df, current_price, decision,
                confidence=confidence/100 if confidence else 0.7
            )

            # Override take profit and stop loss if not provided by LLM
            if not take_profit:
                take_profit = adaptive_stops['take_profit_pct']
                parsed_response['take_profit'] = take_profit

            if not stop_loss:
                stop_loss = adaptive_stops['stop_loss_pct']
                parsed_response['stop_loss'] = stop_loss

            # Calculate position scaling strategy
            position_scaling = risk_manager.calculate_position_scaling(
                1.0,  # Initial position size (normalized)
                100.0,  # Available balance (placeholder)
                confidence/100 if confidence else 0.7
            )

            parsed_response['position_scaling'] = position_scaling

            # Add adaptive risk parameters to response
            parsed_response['adaptive_stops'] = adaptive_stops

            logger.info(f"Risk management: TP: {take_profit:.2f}%, SL: {stop_loss:.2f}%, Scaling: {position_scaling['scaling_stages']} stages")
    except Exception as e:
        logger.warning(f"Error calculating adaptive risk parameters: {e}. Using standard parameters.")

    # 15. Log the final decision (avoid emoji for Windows compatibility)
    logger.info(f"FINAL DECISION for {symbol}: {decision} (confidence: {confidence}%)")
    logger.info(f"Reasoning: {explanation[:100]}..." if len(explanation) > 100 else f"Reasoning: {explanation}")

    # 17. Add additional data to the parsed response
    if signal_scores:
        parsed_response['signal_scores'] = signal_scores

    parsed_response['market_regime'] = market_regime.get('primary_regime', 'unknown')
    parsed_response['multi_timeframe_analysis'] = multi_timeframe_analysis

    # Add ML predictions and position sizing to response
    if ml_predictions:
        parsed_response['ml_predictions'] = ml_predictions

        # Add ensemble prediction summary
        if 'ensemble' in ml_predictions:
            ensemble = ml_predictions['ensemble']
            parsed_response['ml_ensemble'] = {
                'direction': ensemble.get('direction', 'WAIT'),
                'confidence': ensemble.get('confidence', 0.33),
                'model_count': ensemble.get('individual_predictions', 0)
            }

    if 'position_sizing_result' in locals() and position_sizing_result:
        parsed_response['position_sizing'] = position_sizing_result

    return decision, explanation, parsed_response

def main():
    """
    Main function to run the trading system.
    """
    parser = argparse.ArgumentParser(description='Run the trading system with ccxt exchange data')
    parser.add_argument('--symbol', type=str, default=DEFAULT_SYMBOL, help='Trading symbol (e.g., DOGE/USDT)')
    parser.add_argument('--continuous', action='store_true', help='Run continuously')
    parser.add_argument('--delay', type=int, default=DEFAULT_DELAY, help='Delay between runs in seconds')
    parser.add_argument('--live', action='store_true', help='Use live trades data instead of historical OHLCV')

    args = parser.parse_args()

    logger.info(f"Starting trading system for {args.symbol}")

    if args.continuous:
        logger.info(f"Running continuously with {args.delay}s delay")
        try:
            while True:
                decision, explanation, parsed_response = run_trading_system(
                    args.symbol,
                    use_live_data=args.live
                )

                print(f"\n{'='*50}")
                print(f"Symbol: {args.symbol}")
                print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Decision: {decision}")
                print(f"Explanation: {explanation}")
                print(f"{'='*50}\n")

                time.sleep(args.delay)

        except KeyboardInterrupt:
            logger.info("Trading system stopped by user")

    else:
        decision, explanation, parsed_response = run_trading_system(
            args.symbol,
            use_live_data=args.live
        )

        print(f"\n{'='*50}")
        print(f"Symbol: {args.symbol}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Decision: {decision}")
        print(f"Explanation: {explanation}")
        print(f"{'='*50}\n")

    logger.info("Trading system finished")

if __name__ == "__main__":
    main()

