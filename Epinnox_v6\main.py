"""
Main Application for Trading System
This module connects all components and runs the trading system.
"""
import logging
import argparse
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import time
import textwrap
from pathlib import Path
import yaml
import os
import sys

# Initialize GPU environment at startup
try:
    from startup import setup_environment
    gpu_config = setup_environment()
    if gpu_config['gpu_enabled']:
        print(f"GPU acceleration enabled: {gpu_config['device_info']['device_name']}\n")
    else:
        print("GPU acceleration not available. Using CPU only.\n")
except Exception as e:
    print(f"Error setting up GPU environment: {e}\n")

from config.config import config
from config import DEFAULT_SYMBOL, DEFAULT_INTERVAL, DEFAULT_PERIOD, DEFAULT_DELAY
from trading.trading_system_interface import TradingSystemInterface
from trading.simulation_interface import SimulationInterface
from core.multi_timeframe import MultiTimeframeAnalyzer
from core.market_regime import MarketRegimeDetector
from core.adaptive_risk import AdaptiveRiskManager
from core.signal_scoring import SignalScorer
from core.prompt import build_prompt, parse_llm_response
from llama.runner import <PERSON>lamaRunner
from data.exchange import ExchangeDataFetcher
from core.features import extract_features, analyze_tick_data, combine_features

# Configure logging
class WrapperFormatter(logging.Formatter):
    def __init__(self, fmt=None, datefmt=None, style='%', width=100):
        super().__init__(fmt, datefmt, style)
        self.width = width

    def format(self, record):
        # Format the message using the parent formatter
        formatted = super().format(record)

        # Wrap the message
        lines = formatted.split('\n')
        wrapped_lines = []

        for line in lines:
            # Find the position of the message part (after the timestamp, level, etc.)
            parts = line.split(' - ', 3)
            if len(parts) >= 4:
                prefix = ' - '.join(parts[:3]) + ' - '
                message = parts[3]

                # Wrap the message part
                wrapped_message = textwrap.fill(message,
                                              width=self.width,
                                              initial_indent='',
                                              subsequent_indent=' ' * len(prefix))

                # Reconstruct the line
                wrapped_lines.append(prefix + wrapped_message)
            else:
                wrapped_lines.append(line)

        return '\n'.join(wrapped_lines)

# Create formatter
formatter = WrapperFormatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    width=100
)

# Configure logging
file_handler = logging.FileHandler(config.system.log_file)
file_handler.setFormatter(formatter)

console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)

logger = logging.getLogger(__name__)
logger.setLevel(getattr(logging, config.system.log_level))
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def fetch_market_data(symbol, interval='1m', period='1d'):
    """
    Fetch market data for a given symbol.

    Args:
        symbol: Trading symbol (e.g., 'BTC-USD')
        interval: Data interval (e.g., '1m', '5m', '1h')
        period: Data period (e.g., '1d', '5d', '1mo')

    Returns:
        DataFrame: OHLCV data
    """
    logger.info(f"Fetching market data for {symbol} ({interval}, {period})")
    try:
        df = yf.download(symbol, interval=interval, period=period, progress=False)
        if df.empty:
            logger.error(f"No data returned for {symbol}")
            return None

        # Reset index to make Date a column
        df = df.reset_index()

        # Rename columns to lowercase
        df.columns = [col.lower() if isinstance(col, str) else col[0].lower() for col in df.columns]

        logger.info(f"Fetched {len(df)} rows of data for {symbol}")
        return df

    except Exception as e:
        logger.exception(f"Error fetching market data: {e}")
        return None

def simulate_tick_data(ohlcv_df, num_ticks=100):
    """
    Simulate tick data based on OHLCV data.
    In a real system, this would be replaced with actual tick data.

    Args:
        ohlcv_df: OHLCV DataFrame
        num_ticks: Number of ticks to simulate

    Returns:
        DataFrame: Simulated tick data
    """
    if ohlcv_df is None or ohlcv_df.empty:
        return pd.DataFrame()

    # Get the last candle
    last_candle = ohlcv_df.iloc[-1]

    # Create a range of prices between the high and low
    price_range = pd.Series(
        [last_candle['low'] + (last_candle['high'] - last_candle['low']) * i / num_ticks
         for i in range(num_ticks)]
    )

    # Add some randomness to the prices
    price_range = price_range + (price_range * 0.001 * (pd.Series(np.random.randn(num_ticks))))

    # Create a DataFrame with the simulated ticks
    ticks = pd.DataFrame({
        'timestamp': [last_candle['datetime'] + timedelta(seconds=i) for i in range(num_ticks)],
        'price': price_range,
        'volume': [last_candle['volume'] / num_ticks * (1 + 0.5 * np.random.randn()) for _ in range(num_ticks)]
    })

    return ticks

def fetch_exchange_data(symbol, use_exchange=False, use_live_data=False, timeframes=None):
    """
    Fetch data from the exchange.

    Args:
        symbol: Trading symbol (e.g., 'DOGE/USDT')
        use_exchange: Whether to use the exchange data fetcher
        use_live_data: Whether to use live trades data instead of historical OHLCV
        timeframes: List of timeframes to fetch (e.g., ['1m', '5m', '15m'])

    Returns:
        tuple: (ohlcv_df, exchange_features, multi_timeframe_data)
    """
    if not use_exchange:
        return None, {}, {}

    try:
        # Initialize exchange data fetcher
        fetcher = ExchangeDataFetcher(exchange_id='htx')

        # Determine spot and futures symbols
        if '/' in symbol:
            # Check if it's already a futures symbol
            if ':' in symbol:
                # It's a futures symbol, extract the spot symbol
                spot_symbol = symbol.split(':')[0]
                futures_symbol = symbol
                logger.info(f"Using futures symbol: {futures_symbol} and spot symbol: {spot_symbol}")
            else:
                # It's a spot symbol, construct the futures symbol
                spot_symbol = symbol
                base, quote = symbol.split('/')
                futures_symbol = f"{base}/{quote}:{quote}"
                logger.info(f"Using spot symbol: {spot_symbol} and futures symbol: {futures_symbol}")
        else:
            # Default to DOGE/USDT if symbol format is not recognized
            spot_symbol = 'DOGE/USDT'
            futures_symbol = 'DOGE/USDT:USDT'
            logger.warning(f"Symbol format not recognized: {symbol}. Using default: {spot_symbol}")

        # Ensure we don't have duplicate quote in futures symbol
        if futures_symbol.endswith(':USDT:USDT'):
            futures_symbol = futures_symbol.replace(':USDT:USDT', ':USDT')
            logger.info(f"Corrected futures symbol format to: {futures_symbol}")

        # Fetch combined data
        if use_live_data:
            logger.info(f"Using live trades data for {spot_symbol} and {futures_symbol}")
            combined_data = fetcher.fetch_combined_data(
                spot_symbol,
                futures_symbol,
                timeframe='1m',
                limit=100,
                ob_limit=5,
                trades_limit=100,
                include_trades=True
            )
        else:
            logger.info(f"Using historical OHLCV data for {spot_symbol} and {futures_symbol}")
            combined_data = fetcher.fetch_combined_data(
                spot_symbol,
                futures_symbol,
                timeframe='1m',
                limit=100,
                ob_limit=5
            )

        # Extract features
        exchange_features = fetcher.extract_features_from_combined_data(combined_data)

        # Create OHLCV DataFrame from spot data
        ohlcv_df = None
        if combined_data['spot_ohlcv']:
            ohlcv_df = pd.DataFrame(combined_data['spot_ohlcv'])
            logger.info(f"Fetched {len(ohlcv_df)} rows of exchange data for {spot_symbol}")
        else:
            logger.warning(f"No OHLCV data fetched from exchange for {spot_symbol}")

        # Fetch multi-timeframe data if requested
        multi_timeframe_data = {}
        if timeframes:
            # Initialize multi-timeframe analyzer
            mt_analyzer = MultiTimeframeAnalyzer(timeframes=timeframes)

            # Fetch data for each timeframe
            multi_timeframe_data = mt_analyzer.fetch_multi_timeframe_data(fetcher, spot_symbol)

            logger.info(f"Fetched data for {len(multi_timeframe_data)} timeframes: {list(multi_timeframe_data.keys())}")

        return ohlcv_df, exchange_features, multi_timeframe_data

    except Exception as e:
        logger.exception(f"Error fetching exchange data: {e}")
        return None, {}, {}

def run_trading_system(symbol, interval='1m', period='1d', use_exchange=False, use_live_data=False):
    """
    Run the trading system for a given symbol.

    Args:
        symbol: Trading symbol (e.g., 'BTC-USD' or 'DOGE/USDT')
        interval: Data interval (e.g., '1m', '5m', '1h')
        period: Data period (e.g., '1d', '5d', '1mo')
        use_exchange: Whether to use the exchange data fetcher
        use_live_data: Whether to use live trades data instead of historical OHLCV

    Returns:
        tuple: (decision, explanation, parsed_response)
        parsed_response is a dictionary containing:
            - decision: LONG, SHORT, or WAIT
            - explanation: Explanation for the decision
            - confidence: Confidence level (0-100%)
            - take_profit: Take profit percentage (optional)
            - stop_loss: Stop loss percentage (optional)
            - market_regime: Detected market regime
            - multi_timeframe_analysis: Analysis across multiple timeframes
            - position_scaling: Position scaling strategy
    """
    # Define timeframes for multi-timeframe analysis
    timeframes = ['1m', '5m', '15m']

    # 1. Try to fetch data from the exchange if requested
    exchange_df, exchange_features, multi_timeframe_data = fetch_exchange_data(
        symbol, use_exchange, use_live_data, timeframes=timeframes
    )

    # 2. Fetch market data from yfinance if exchange data is not available
    if exchange_df is None:
        ohlcv_df = fetch_market_data(symbol, interval, period)
        if ohlcv_df is None:
            return "ERROR", "Failed to fetch market data", {"decision": "ERROR", "explanation": "Failed to fetch market data"}
    else:
        ohlcv_df = exchange_df
        logger.info(f"Using exchange data for {symbol}")

    # 3. Simulate tick data (in a real system, this would be actual tick data)
    tick_df = simulate_tick_data(ohlcv_df)

    # 4. Extract features from OHLCV data
    ohlcv_features = extract_features(ohlcv_df)

    # 5. Extract features from tick data
    tick_features = analyze_tick_data(tick_df)

    # 6. Combine features
    combined_features = combine_features(ohlcv_features, tick_features)

    # 7. Add exchange features if available
    if exchange_features:
        logger.info(f"Adding {len(exchange_features)} exchange features")
        # Add exchange features that don't conflict with existing features
        for k, v in exchange_features.items():
            if k not in combined_features:
                combined_features[k] = v

    # 8. Perform multi-timeframe analysis
    multi_timeframe_analysis = {}
    trend_strength = 0
    trend_direction = "neutral"
    trend_alignment = 0
    volatility = 1.0

    if multi_timeframe_data and len(multi_timeframe_data) > 0:
        try:
            # Initialize multi-timeframe analyzer
            mt_analyzer = MultiTimeframeAnalyzer(timeframes=timeframes)

            # Calculate indicators for each timeframe
            indicators = mt_analyzer.calculate_indicators(multi_timeframe_data)

            # Analyze trend for each timeframe
            trend_analysis = mt_analyzer.analyze_trend(indicators)

            # Calculate overall trend strength
            trend_metrics = mt_analyzer.calculate_trend_strength(trend_analysis)

            # Store results
            multi_timeframe_analysis = {
                'trend_metrics': trend_metrics,
                'timeframe_analysis': trend_analysis
            }

            # Extract key metrics for market regime detection
            trend_strength = trend_metrics['trend_strength']
            trend_direction = trend_metrics['trend_direction']
            trend_alignment = trend_metrics['trend_alignment']
            volatility = trend_metrics['volatility']

            logger.info(f"Multi-timeframe analysis: Trend direction: {trend_direction}, Strength: {trend_strength:.2f}, Alignment: {trend_alignment:.2f}")
        except Exception as e:
            logger.warning(f"Error in multi-timeframe analysis: {e}. Proceeding with single timeframe.")

    # 9. Detect market regime
    market_regime = {}
    try:
        # Initialize market regime detector
        regime_detector = MarketRegimeDetector()

        # Detect market regime
        regime_result = regime_detector.detect_regime(
            ohlcv_df, trend_strength=trend_strength,
            volatility=volatility, trend_alignment=trend_alignment
        )

        # Store results
        market_regime = regime_result

        logger.info(f"Detected market regime: {regime_result['primary_regime']}")
        logger.info(f"Regime adjustments: {regime_result['adjustments']}")
    except Exception as e:
        logger.warning(f"Error detecting market regime: {e}. Proceeding without regime detection.")

    # 10. Calculate signal scores with longer timeframe confirmation
    signal_scores = None
    try:
        # Initialize signal scorer with smoothing and longer timeframe confirmation
        scorer = SignalScorer(smoothing_periods=3, use_longer_timeframe=True)

        # Calculate scores with longer timeframe data if available
        signal_scores = scorer.calculate_scores(ohlcv_df, None)  # We're using our own multi-timeframe analysis now

        # Get decision with enhanced criteria
        decision_from_score, confidence_from_score, alignment = scorer.get_decision(signal_scores)
        logger.info(f"Signal-based decision: {decision_from_score} with confidence {confidence_from_score:.2f}% and alignment {alignment:.2f}%")
        logger.info(f"Calculated signal scores: {signal_scores}")

        # Enhance signal scores with multi-timeframe analysis
        if multi_timeframe_analysis:
            signal_scores['trend_strength'] = trend_strength
            signal_scores['trend_direction'] = trend_direction
            signal_scores['trend_alignment'] = trend_alignment
            signal_scores['multi_timeframe_confidence'] = (trend_alignment + 0.5) * 100  # Convert to percentage
    except ImportError:
        logger.warning("Signal scoring module not available. Proceeding without signal scores.")
    except Exception as e:
        logger.warning(f"Error calculating signal scores: {e}. Proceeding without signal scores.")

    # 11. Build prompt for LLaMA with enhanced market context
    prompt = build_prompt(combined_features, signal_scores=signal_scores,
                         market_regime=market_regime.get('primary_regime', 'unknown'),
                         multi_timeframe_analysis=multi_timeframe_analysis)

    # 12. Run LLaMA inference
    llama = LlamaRunner()
    response = llama.run_inference(prompt)

    # 13. Parse the response
    parsed_response = parse_llm_response(response)
    decision = parsed_response['decision']
    explanation = parsed_response['explanation']
    confidence = parsed_response['confidence']
    take_profit = parsed_response['take_profit']
    stop_loss = parsed_response['stop_loss']

    # 14. Calculate adaptive risk parameters
    try:
        # Initialize adaptive risk manager
        risk_manager = AdaptiveRiskManager()

        # Get current price
        current_price = ohlcv_df['close'].iloc[-1] if not ohlcv_df.empty else 0

        # Calculate volatility-based stops
        if decision in ['LONG', 'SHORT']:
            adaptive_stops = risk_manager.calculate_volatility_based_stops(
                ohlcv_df, current_price, decision,
                confidence=confidence/100 if confidence else 0.7
            )

            # Override take profit and stop loss if not provided by LLM
            if not take_profit:
                take_profit = adaptive_stops['take_profit_pct']
                parsed_response['take_profit'] = take_profit
                logger.info(f"Using adaptive take profit: {take_profit:.2f}%")

            if not stop_loss:
                stop_loss = adaptive_stops['stop_loss_pct']
                parsed_response['stop_loss'] = stop_loss
                logger.info(f"Using adaptive stop loss: {stop_loss:.2f}%")

            # Calculate position scaling strategy
            position_scaling = risk_manager.calculate_position_scaling(
                1.0,  # Initial position size (normalized)
                100.0,  # Available balance (placeholder)
                confidence/100 if confidence else 0.7
            )

            parsed_response['position_scaling'] = position_scaling
            logger.info(f"Position scaling strategy: {position_scaling['scaling_stages']} stages")

            # Add adaptive risk parameters to response
            parsed_response['adaptive_stops'] = adaptive_stops
    except Exception as e:
        logger.warning(f"Error calculating adaptive risk parameters: {e}. Using standard parameters.")

    # 15. Log the decision and details
    logger.info(f"Decision for {symbol}: {decision}")
    logger.info(f"Explanation: {explanation}")
    if confidence:
        logger.info(f"Confidence: {confidence}%")
    if take_profit:
        logger.info(f"Take Profit: {take_profit}%")
    if stop_loss:
        logger.info(f"Stop Loss: {stop_loss}%")

    # 16. Add additional data to the parsed response
    if signal_scores:
        parsed_response['signal_scores'] = signal_scores

    parsed_response['market_regime'] = market_regime.get('primary_regime', 'unknown')
    parsed_response['multi_timeframe_analysis'] = multi_timeframe_analysis

    return decision, explanation, parsed_response

def main():
    """
    Main function to run the trading system.
    """
    parser = argparse.ArgumentParser(description='Run the trading system')
    parser.add_argument('--symbol', type=str, default=DEFAULT_SYMBOL, help='Trading symbol')
    parser.add_argument('--interval', type=str, default=DEFAULT_INTERVAL, help='Data interval')
    parser.add_argument('--period', type=str, default=DEFAULT_PERIOD, help='Data period')
    parser.add_argument('--continuous', action='store_true', help='Run continuously')
    parser.add_argument('--delay', type=int, default=DEFAULT_DELAY, help='Delay between runs in seconds')
    parser.add_argument('--exchange', action='store_true', help='Use exchange data (HTX) instead of yfinance')
    parser.add_argument('--live', action='store_true', help='Use live trades data instead of historical OHLCV')

    args = parser.parse_args()

    logger.info(f"Starting trading system for {args.symbol}")

    if args.continuous:
        logger.info(f"Running continuously with {args.delay}s delay")
        try:
            while True:
                decision, explanation, parsed_response = run_trading_system(
                    args.symbol,
                    interval=args.interval,
                    period=args.period,
                    use_exchange=args.exchange,
                    use_live_data=args.live
                )

                print(f"\n{'='*50}")
                print(f"Symbol: {args.symbol}")
                print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Decision: {decision}")
                print(f"Explanation: {explanation}")
                print(f"{'='*50}\n")

                time.sleep(args.delay)

        except KeyboardInterrupt:
            logger.info("Trading system stopped by user")

    else:
        decision, explanation, parsed_response = run_trading_system(
            args.symbol,
            interval=args.interval,
            period=args.period,
            use_exchange=args.exchange,
            use_live_data=args.live
        )

        print(f"\n{'='*50}")
        print(f"Symbol: {args.symbol}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Decision: {decision}")
        print(f"Explanation: {explanation}")
        print(f"{'='*50}\n")

    logger.info("Trading system finished")

if __name__ == "__main__":
    main()

