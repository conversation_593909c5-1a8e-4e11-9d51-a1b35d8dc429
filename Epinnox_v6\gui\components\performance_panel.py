"""
Performance Panel
Displays trading performance metrics and historical data
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QFrame, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class PerformancePanel(QWidget):
    """Panel displaying performance metrics and trading statistics"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Overall Performance Summary
        summary_group = QGroupBox("📈 Performance Summary")
        summary_layout = QGridLayout(summary_group)
        
        # Key metrics
        self.total_signals_label = QLabel("0")
        self.total_signals_label.setFont(QFont("Arial", 14, QFont.Bold))
        
        self.successful_signals_label = QLabel("0")
        self.successful_signals_label.setFont(QFont("Arial", 14, QFont.Bold))
        
        self.success_rate_label = QLabel("0%")
        self.success_rate_label.setFont(QFont("Arial", 14, QFont.Bold))
        
        self.avg_confidence_label = QLabel("0%")
        self.avg_confidence_label.setFont(QFont("Arial", 12))
        
        # Success rate progress bar
        self.success_rate_bar = QProgressBar()
        self.success_rate_bar.setRange(0, 100)
        self.success_rate_bar.setValue(0)
        self.success_rate_bar.setTextVisible(True)
        
        summary_layout.addWidget(QLabel("Total Signals:"), 0, 0)
        summary_layout.addWidget(self.total_signals_label, 0, 1)
        summary_layout.addWidget(QLabel("Successful:"), 0, 2)
        summary_layout.addWidget(self.successful_signals_label, 0, 3)
        
        summary_layout.addWidget(QLabel("Success Rate:"), 1, 0)
        summary_layout.addWidget(self.success_rate_label, 1, 1)
        summary_layout.addWidget(self.success_rate_bar, 1, 2, 1, 2)
        
        summary_layout.addWidget(QLabel("Avg Confidence:"), 2, 0)
        summary_layout.addWidget(self.avg_confidence_label, 2, 1, 1, 3)
        
        layout.addWidget(summary_group)
        
        # Signal Type Breakdown
        breakdown_group = QGroupBox("🎯 Signal Type Performance")
        breakdown_layout = QVBoxLayout(breakdown_group)
        
        self.breakdown_table = QTableWidget(3, 4)
        self.breakdown_table.setHorizontalHeaderLabels([
            "Signal Type", "Count", "Success Rate", "Avg Confidence"
        ])
        self.breakdown_table.horizontalHeader().setStretchLastSection(True)
        self.breakdown_table.setMaximumHeight(120)
        
        # Initialize signal types
        signal_types = ['LONG', 'SHORT', 'WAIT']
        for i, signal_type in enumerate(signal_types):
            self.breakdown_table.setItem(i, 0, QTableWidgetItem(signal_type))
            self.breakdown_table.setItem(i, 1, QTableWidgetItem("0"))
            self.breakdown_table.setItem(i, 2, QTableWidgetItem("0%"))
            self.breakdown_table.setItem(i, 3, QTableWidgetItem("0%"))
        
        breakdown_layout.addWidget(self.breakdown_table)
        layout.addWidget(breakdown_group)
        
        # Time-based Performance
        time_group = QGroupBox("⏰ Time-based Performance")
        time_layout = QGridLayout(time_group)
        
        self.runtime_label = QLabel("00:00:00")
        self.signals_per_hour_label = QLabel("0.0")
        self.last_24h_signals_label = QLabel("0")
        self.peak_performance_label = QLabel("--")
        
        time_layout.addWidget(QLabel("Total Runtime:"), 0, 0)
        time_layout.addWidget(self.runtime_label, 0, 1)
        time_layout.addWidget(QLabel("Signals/Hour:"), 0, 2)
        time_layout.addWidget(self.signals_per_hour_label, 0, 3)
        
        time_layout.addWidget(QLabel("Last 24h Signals:"), 1, 0)
        time_layout.addWidget(self.last_24h_signals_label, 1, 1)
        time_layout.addWidget(QLabel("Peak Performance:"), 1, 2)
        time_layout.addWidget(self.peak_performance_label, 1, 3)
        
        layout.addWidget(time_group)
        
        # Confidence Distribution
        confidence_group = QGroupBox("📊 Confidence Distribution")
        confidence_layout = QGridLayout(confidence_group)
        
        # Confidence ranges
        self.very_high_conf_label = QLabel("0")  # 80-100%
        self.high_conf_label = QLabel("0")       # 60-80%
        self.medium_conf_label = QLabel("0")     # 40-60%
        self.low_conf_label = QLabel("0")        # 20-40%
        self.very_low_conf_label = QLabel("0")   # 0-20%
        
        confidence_layout.addWidget(QLabel("Very High (80-100%):"), 0, 0)
        confidence_layout.addWidget(self.very_high_conf_label, 0, 1)
        confidence_layout.addWidget(QLabel("High (60-80%):"), 0, 2)
        confidence_layout.addWidget(self.high_conf_label, 0, 3)
        
        confidence_layout.addWidget(QLabel("Medium (40-60%):"), 1, 0)
        confidence_layout.addWidget(self.medium_conf_label, 1, 1)
        confidence_layout.addWidget(QLabel("Low (20-40%):"), 1, 2)
        confidence_layout.addWidget(self.low_conf_label, 1, 3)
        
        confidence_layout.addWidget(QLabel("Very Low (0-20%):"), 2, 0)
        confidence_layout.addWidget(self.very_low_conf_label, 2, 1, 1, 3)
        
        layout.addWidget(confidence_group)
        
        # Recent Performance Trend
        trend_group = QGroupBox("📈 Recent Performance Trend")
        trend_layout = QGridLayout(trend_group)
        
        self.trend_direction_label = QLabel("Stable")
        self.trend_direction_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.improvement_label = QLabel("0%")
        self.best_streak_label = QLabel("0")
        self.current_streak_label = QLabel("0")
        
        trend_layout.addWidget(QLabel("Trend:"), 0, 0)
        trend_layout.addWidget(self.trend_direction_label, 0, 1)
        trend_layout.addWidget(QLabel("Improvement:"), 0, 2)
        trend_layout.addWidget(self.improvement_label, 0, 3)
        
        trend_layout.addWidget(QLabel("Best Streak:"), 1, 0)
        trend_layout.addWidget(self.best_streak_label, 1, 1)
        trend_layout.addWidget(QLabel("Current Streak:"), 1, 2)
        trend_layout.addWidget(self.current_streak_label, 1, 3)
        
        layout.addWidget(trend_group)
        
        # System Efficiency
        efficiency_group = QGroupBox("⚡ System Efficiency")
        efficiency_layout = QGridLayout(efficiency_group)
        
        self.avg_analysis_time_label = QLabel("0.0s")
        self.memory_efficiency_label = QLabel("100%")
        self.cpu_efficiency_label = QLabel("100%")
        self.error_rate_label = QLabel("0%")
        
        efficiency_layout.addWidget(QLabel("Avg Analysis Time:"), 0, 0)
        efficiency_layout.addWidget(self.avg_analysis_time_label, 0, 1)
        efficiency_layout.addWidget(QLabel("Memory Efficiency:"), 0, 2)
        efficiency_layout.addWidget(self.memory_efficiency_label, 0, 3)
        
        efficiency_layout.addWidget(QLabel("CPU Efficiency:"), 1, 0)
        efficiency_layout.addWidget(self.cpu_efficiency_label, 1, 1)
        efficiency_layout.addWidget(QLabel("Error Rate:"), 1, 2)
        efficiency_layout.addWidget(self.error_rate_label, 1, 3)
        
        layout.addWidget(efficiency_group)
    
    def update_data(self, data):
        """Update the panel with new performance data"""
        try:
            # Update overall performance summary
            total_signals = data.get('total_signals', 0)
            successful_signals = data.get('successful_signals', 0)
            success_rate = data.get('success_rate', 0.0)
            avg_confidence = data.get('avg_confidence', 0.0)
            
            self.total_signals_label.setText(str(total_signals))
            self.successful_signals_label.setText(str(successful_signals))
            self.success_rate_label.setText(f"{success_rate:.1f}%")
            self.avg_confidence_label.setText(f"{avg_confidence:.1f}%")
            
            # Color code success rate
            if success_rate >= 70:
                self.success_rate_label.setStyleSheet("color: #00ff88; font-weight: bold;")
                self.success_rate_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            elif success_rate >= 50:
                self.success_rate_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
                self.success_rate_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.success_rate_label.setStyleSheet("color: #ff4444; font-weight: bold;")
                self.success_rate_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            
            self.success_rate_bar.setValue(int(success_rate))
            
            # Update time-based performance
            runtime = data.get('total_runtime', '00:00:00')
            self.runtime_label.setText(runtime)
            
            # Calculate signals per hour (simplified)
            if total_signals > 0 and runtime != '00:00:00':
                # This is a simplified calculation - would need actual time parsing
                signals_per_hour = total_signals / max(1, total_signals / 10)  # Placeholder
                self.signals_per_hour_label.setText(f"{signals_per_hour:.1f}")
            
            # Update other metrics with placeholder data
            self.update_signal_breakdown(data)
            self.update_confidence_distribution(data)
            self.update_performance_trend(data)
            self.update_system_efficiency(data)
            
        except Exception as e:
            print(f"Error updating performance panel: {e}")
    
    def update_signal_breakdown(self, data):
        """Update signal type breakdown table"""
        try:
            # This would be enhanced with actual signal tracking
            # For now, using placeholder data
            signal_data = [
                ('LONG', 5, 80.0, 75.0),
                ('SHORT', 3, 66.7, 70.0),
                ('WAIT', 12, 100.0, 45.0)  # WAIT signals are always "successful"
            ]
            
            for i, (signal_type, count, success_rate, avg_conf) in enumerate(signal_data):
                if i < self.breakdown_table.rowCount():
                    count_item = QTableWidgetItem(str(count))
                    success_item = QTableWidgetItem(f"{success_rate:.1f}%")
                    conf_item = QTableWidgetItem(f"{avg_conf:.1f}%")
                    
                    # Color code success rate
                    if success_rate >= 70:
                        success_item.setBackground(QColor(0, 255, 136, 50))
                    elif success_rate >= 50:
                        success_item.setBackground(QColor(255, 170, 0, 50))
                    else:
                        success_item.setBackground(QColor(255, 68, 68, 50))
                    
                    self.breakdown_table.setItem(i, 1, count_item)
                    self.breakdown_table.setItem(i, 2, success_item)
                    self.breakdown_table.setItem(i, 3, conf_item)
                    
        except Exception as e:
            print(f"Error updating signal breakdown: {e}")
    
    def update_confidence_distribution(self, data):
        """Update confidence distribution"""
        try:
            # Placeholder confidence distribution
            self.very_high_conf_label.setText("2")
            self.very_high_conf_label.setStyleSheet("color: #00ff88; font-weight: bold;")
            
            self.high_conf_label.setText("3")
            self.high_conf_label.setStyleSheet("color: #88ff00; font-weight: bold;")
            
            self.medium_conf_label.setText("8")
            self.medium_conf_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
            
            self.low_conf_label.setText("5")
            self.low_conf_label.setStyleSheet("color: #ff8800; font-weight: bold;")
            
            self.very_low_conf_label.setText("2")
            self.very_low_conf_label.setStyleSheet("color: #ff4444; font-weight: bold;")
            
        except Exception as e:
            print(f"Error updating confidence distribution: {e}")
    
    def update_performance_trend(self, data):
        """Update performance trend information"""
        try:
            # Placeholder trend data
            self.trend_direction_label.setText("Improving")
            self.trend_direction_label.setStyleSheet("color: #00ff88; font-weight: bold;")
            
            self.improvement_label.setText("+5.2%")
            self.improvement_label.setStyleSheet("color: #00ff88; font-weight: bold;")
            
            self.best_streak_label.setText("7")
            self.current_streak_label.setText("3")
            
        except Exception as e:
            print(f"Error updating performance trend: {e}")
    
    def update_system_efficiency(self, data):
        """Update system efficiency metrics"""
        try:
            # Use actual data if available, otherwise placeholders
            memory_usage = data.get('memory_usage', 0.0)
            cpu_usage = data.get('cpu_usage', 0.0)
            
            self.avg_analysis_time_label.setText("2.3s")
            
            # Calculate efficiency based on resource usage
            memory_efficiency = max(0, 100 - (memory_usage / 10))
            cpu_efficiency = max(0, 100 - cpu_usage)
            
            self.memory_efficiency_label.setText(f"{memory_efficiency:.1f}%")
            self.cpu_efficiency_label.setText(f"{cpu_efficiency:.1f}%")
            
            # Color code efficiency
            if memory_efficiency >= 80:
                self.memory_efficiency_label.setStyleSheet("color: #00ff88;")
            elif memory_efficiency >= 60:
                self.memory_efficiency_label.setStyleSheet("color: #ffaa00;")
            else:
                self.memory_efficiency_label.setStyleSheet("color: #ff4444;")
            
            if cpu_efficiency >= 80:
                self.cpu_efficiency_label.setStyleSheet("color: #00ff88;")
            elif cpu_efficiency >= 60:
                self.cpu_efficiency_label.setStyleSheet("color: #ffaa00;")
            else:
                self.cpu_efficiency_label.setStyleSheet("color: #ff4444;")
            
            self.error_rate_label.setText("0.5%")
            self.error_rate_label.setStyleSheet("color: #00ff88;")
            
        except Exception as e:
            print(f"Error updating system efficiency: {e}")
