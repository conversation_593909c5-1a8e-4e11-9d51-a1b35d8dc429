"""
LMStudio API Integration
This module handles interaction with LMStudio's local API server.
"""
import requests
import logging
import json

logger = logging.getLogger(__name__)

class LMStudioRunner:
    """
    Class to handle running inference with LMStudio's local API.
    """
    
    def __init__(self, api_url="http://localhost:1234/v1", model_name="phi-3.1-mini-128k-instruct"):
        """
        Initialize the LMStudio runner.
        
        Args:
            api_url: Base URL for LMStudio API
            model_name: Name of the model to use
        """
        self.api_url = api_url
        self.model_name = model_name
        self.chat_url = f"{api_url}/chat/completions"
        
        # Test connection
        try:
            response = requests.get(f"{api_url}/models", timeout=5)
            if response.status_code == 200:
                models = response.json()
                available_models = [model['id'] for model in models['data']]
                logger.info(f"Connected to LMStudio. Available models: {available_models}")
                
                if self.model_name not in available_models:
                    logger.warning(f"Model {self.model_name} not found. Using first available model.")
                    if available_models:
                        self.model_name = available_models[0]
                        logger.info(f"Using model: {self.model_name}")
            else:
                logger.error(f"Failed to connect to LMStudio: {response.status_code}")
        except Exception as e:
            logger.error(f"Error connecting to LMStudio: {e}")
    
    def run_inference(self, prompt, temperature=0.1, max_tokens=512):
        """
        Run inference with the LMStudio model.
        
        Args:
            prompt: The prompt to send to the model
            temperature: Temperature for sampling
            max_tokens: Maximum tokens to generate
            
        Returns:
            str: Model's response
        """
        try:
            # Prepare the request
            headers = {
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system", 
                        "content": "You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:\n\nDECISION: [LONG/SHORT/WAIT]\nCONFIDENCE: [50-100]%\nTAKE_PROFIT: [percentage]%\nSTOP_LOSS: [percentage]%\nEXPLANATION: [Your detailed analysis]\n\nBe concise but thorough in your analysis."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            logger.info(f"Sending request to LMStudio with model: {self.model_name}")
            
            # Make the request
            response = requests.post(self.chat_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    logger.info("Successfully got response from LMStudio")
                    return content
                else:
                    logger.error("No choices in LMStudio response")
                    return "Error: No response from model"
            else:
                logger.error(f"LMStudio API error: {response.status_code} - {response.text}")
                return f"Error: LMStudio API returned {response.status_code}"
                
        except requests.exceptions.Timeout:
            logger.error("LMStudio request timed out")
            return "Error: Request timed out"
        except requests.exceptions.ConnectionError:
            logger.error("Could not connect to LMStudio")
            return "Error: Could not connect to LMStudio"
        except Exception as e:
            logger.error(f"Error in LMStudio inference: {e}")
            return f"Error: {str(e)}"
    
    def test_connection(self):
        """
        Test the connection to LMStudio.
        
        Returns:
            bool: True if connection is successful
        """
        try:
            response = requests.get(f"{self.api_url}/models", timeout=5)
            return response.status_code == 200
        except:
            return False
