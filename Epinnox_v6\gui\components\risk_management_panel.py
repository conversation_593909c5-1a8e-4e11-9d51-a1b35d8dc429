"""
Risk Management Panel
Displays adaptive risk management calculations and position sizing
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QFrame, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class RiskManagementPanel(QWidget):
    """Panel displaying risk management and position sizing information"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Position Sizing
        position_group = QGroupBox("💰 Position Sizing")
        position_layout = QGridLayout(position_group)
        
        self.position_size_label = QLabel("$0.00")
        self.position_size_label.setFont(QFont("Arial", 14, QFont.Bold))
        
        self.max_position_label = QLabel("$100.00")
        self.risk_percentage_label = QLabel("0.0%")
        
        # Position size progress bar
        self.position_bar = QProgressBar()
        self.position_bar.setRange(0, 100)
        self.position_bar.setValue(0)
        self.position_bar.setTextVisible(True)
        
        position_layout.addWidget(QLabel("Recommended Size:"), 0, 0)
        position_layout.addWidget(self.position_size_label, 0, 1)
        position_layout.addWidget(QLabel("Max Position:"), 0, 2)
        position_layout.addWidget(self.max_position_label, 0, 3)
        
        position_layout.addWidget(QLabel("Risk %:"), 1, 0)
        position_layout.addWidget(self.risk_percentage_label, 1, 1)
        position_layout.addWidget(self.position_bar, 1, 2, 1, 2)
        
        layout.addWidget(position_group)
        
        # Adaptive Stop Loss & Take Profit
        adaptive_group = QGroupBox("🎯 Adaptive Risk Levels")
        adaptive_layout = QGridLayout(adaptive_group)
        
        self.adaptive_stop_loss_label = QLabel("0.00%")
        self.adaptive_stop_loss_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.adaptive_take_profit_label = QLabel("0.00%")
        self.adaptive_take_profit_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.atr_volatility_label = QLabel("0.00%")
        self.risk_reward_ratio_label = QLabel("1:1")
        
        adaptive_layout.addWidget(QLabel("Adaptive Stop Loss:"), 0, 0)
        adaptive_layout.addWidget(self.adaptive_stop_loss_label, 0, 1)
        adaptive_layout.addWidget(QLabel("Adaptive Take Profit:"), 0, 2)
        adaptive_layout.addWidget(self.adaptive_take_profit_label, 0, 3)
        
        adaptive_layout.addWidget(QLabel("ATR Volatility:"), 1, 0)
        adaptive_layout.addWidget(self.atr_volatility_label, 1, 1)
        adaptive_layout.addWidget(QLabel("Risk:Reward:"), 1, 2)
        adaptive_layout.addWidget(self.risk_reward_ratio_label, 1, 3)
        
        layout.addWidget(adaptive_group)
        
        # Risk Score Analysis
        risk_score_group = QGroupBox("📊 Risk Score Analysis")
        risk_score_layout = QVBoxLayout(risk_score_group)
        
        # Risk score display
        risk_score_header = QHBoxLayout()
        self.risk_score_label = QLabel("0.0")
        self.risk_score_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.risk_score_label.setAlignment(Qt.AlignCenter)
        
        self.risk_level_label = QLabel("LOW")
        self.risk_level_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.risk_level_label.setAlignment(Qt.AlignCenter)
        
        risk_score_header.addWidget(QLabel("Risk Score:"))
        risk_score_header.addWidget(self.risk_score_label)
        risk_score_header.addWidget(QLabel("Level:"))
        risk_score_header.addWidget(self.risk_level_label)
        
        # Risk score progress bar
        self.risk_score_bar = QProgressBar()
        self.risk_score_bar.setRange(0, 100)
        self.risk_score_bar.setValue(0)
        self.risk_score_bar.setTextVisible(True)
        
        risk_score_layout.addLayout(risk_score_header)
        risk_score_layout.addWidget(self.risk_score_bar)
        
        layout.addWidget(risk_score_group)
        
        # Risk Factors Breakdown
        factors_group = QGroupBox("🔍 Risk Factors Breakdown")
        factors_layout = QVBoxLayout(factors_group)
        
        self.factors_table = QTableWidget(5, 3)
        self.factors_table.setHorizontalHeaderLabels([
            "Factor", "Value", "Impact"
        ])
        self.factors_table.horizontalHeader().setStretchLastSection(True)
        self.factors_table.setMaximumHeight(180)
        
        # Initialize risk factors
        factors = [
            'Market Volatility',
            'Position Size',
            'Trend Strength',
            'Signal Confidence',
            'Market Regime'
        ]
        
        for i, factor in enumerate(factors):
            self.factors_table.setItem(i, 0, QTableWidgetItem(factor))
            self.factors_table.setItem(i, 1, QTableWidgetItem("--"))
            self.factors_table.setItem(i, 2, QTableWidgetItem("Neutral"))
        
        factors_layout.addWidget(self.factors_table)
        layout.addWidget(factors_group)
        
        # Risk Management Rules
        rules_group = QGroupBox("📋 Active Risk Rules")
        rules_layout = QVBoxLayout(rules_group)
        
        self.max_daily_loss_label = QLabel("Max Daily Loss: $50.00")
        self.max_position_count_label = QLabel("Max Positions: 3")
        self.correlation_limit_label = QLabel("Correlation Limit: 70%")
        self.drawdown_limit_label = QLabel("Max Drawdown: 10%")
        
        rules_layout.addWidget(self.max_daily_loss_label)
        rules_layout.addWidget(self.max_position_count_label)
        rules_layout.addWidget(self.correlation_limit_label)
        rules_layout.addWidget(self.drawdown_limit_label)
        
        layout.addWidget(rules_group)
    
    def update_data(self, data):
        """Update the panel with new risk management data"""
        try:
            # Update position sizing
            position_size = data.get('position_size', 0.0)
            max_position = data.get('max_position_size', 100.0)
            
            self.position_size_label.setText(f"${position_size:.2f}")
            self.max_position_label.setText(f"${max_position:.2f}")
            
            # Calculate position percentage
            position_percentage = (position_size / max_position * 100) if max_position > 0 else 0
            self.position_bar.setValue(int(position_percentage))
            
            # Color code position bar
            if position_percentage > 80:
                self.position_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            elif position_percentage > 60:
                self.position_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.position_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            
            # Update adaptive levels
            adaptive_stop_loss = data.get('adaptive_stop_loss', 0.0)
            adaptive_take_profit = data.get('adaptive_take_profit', 0.0)
            atr_volatility = data.get('atr_volatility', 0.0)
            
            self.adaptive_stop_loss_label.setText(f"{adaptive_stop_loss:.2f}%")
            self.adaptive_stop_loss_label.setStyleSheet("color: #ff4444;")
            
            self.adaptive_take_profit_label.setText(f"{adaptive_take_profit:.2f}%")
            self.adaptive_take_profit_label.setStyleSheet("color: #00ff88;")
            
            self.atr_volatility_label.setText(f"{atr_volatility:.2f}%")
            
            # Calculate risk:reward ratio
            if adaptive_stop_loss > 0:
                risk_reward = adaptive_take_profit / adaptive_stop_loss
                self.risk_reward_ratio_label.setText(f"1:{risk_reward:.1f}")
                
                if risk_reward >= 2:
                    self.risk_reward_ratio_label.setStyleSheet("color: #00ff88;")
                elif risk_reward >= 1.5:
                    self.risk_reward_ratio_label.setStyleSheet("color: #ffaa00;")
                else:
                    self.risk_reward_ratio_label.setStyleSheet("color: #ff4444;")
            
            # Update risk score
            risk_score = data.get('risk_score', 0.0)
            self.update_risk_score(risk_score)
            
            # Update risk factors
            self.update_risk_factors(data)
            
        except Exception as e:
            print(f"Error updating risk management panel: {e}")
    
    def update_risk_score(self, risk_score):
        """Update the risk score display"""
        try:
            self.risk_score_label.setText(f"{risk_score:.1f}")
            
            # Determine risk level and colors
            if risk_score >= 8:
                risk_level = "VERY HIGH"
                color = "#ff0000"
                bg_color = "rgba(255, 0, 0, 20)"
            elif risk_score >= 6:
                risk_level = "HIGH"
                color = "#ff4444"
                bg_color = "rgba(255, 68, 68, 20)"
            elif risk_score >= 4:
                risk_level = "MEDIUM"
                color = "#ffaa00"
                bg_color = "rgba(255, 170, 0, 20)"
            elif risk_score >= 2:
                risk_level = "LOW"
                color = "#88ff00"
                bg_color = "rgba(136, 255, 0, 20)"
            else:
                risk_level = "VERY LOW"
                color = "#00ff88"
                bg_color = "rgba(0, 255, 136, 20)"
            
            self.risk_level_label.setText(risk_level)
            self.risk_level_label.setStyleSheet(f"color: {color}; background-color: {bg_color}; padding: 5px; border-radius: 3px;")
            
            # Update risk score bar
            risk_percentage = min(risk_score * 10, 100)  # Scale to 0-100
            self.risk_score_bar.setValue(int(risk_percentage))
            
            if risk_percentage >= 80:
                self.risk_score_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            elif risk_percentage >= 60:
                self.risk_score_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.risk_score_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
                
        except Exception as e:
            print(f"Error updating risk score: {e}")
    
    def update_risk_factors(self, data):
        """Update the risk factors breakdown table"""
        try:
            # Risk factors with their values and impacts
            factors_data = [
                ('Market Volatility', data.get('atr_volatility', 0.0), 'volatility'),
                ('Position Size', data.get('position_size', 0.0), 'position'),
                ('Trend Strength', data.get('trend_strength', 0.0), 'trend'),
                ('Signal Confidence', data.get('signal_confidence', 0.0), 'confidence'),
                ('Market Regime', data.get('regime_risk', 0.0), 'regime')
            ]
            
            for i, (factor_name, value, factor_type) in enumerate(factors_data):
                if i < self.factors_table.rowCount():
                    # Format value based on type
                    if factor_type in ['volatility', 'confidence']:
                        value_text = f"{value:.2f}%"
                    elif factor_type == 'position':
                        value_text = f"${value:.2f}"
                    else:
                        value_text = f"{value:.2f}"
                    
                    # Determine impact
                    if factor_type == 'volatility':
                        if value > 5:
                            impact = "High Risk"
                            color = QColor(255, 68, 68, 50)
                        elif value > 2:
                            impact = "Medium Risk"
                            color = QColor(255, 170, 0, 50)
                        else:
                            impact = "Low Risk"
                            color = QColor(0, 255, 136, 50)
                    elif factor_type == 'confidence':
                        if value > 70:
                            impact = "Low Risk"
                            color = QColor(0, 255, 136, 50)
                        elif value > 50:
                            impact = "Medium Risk"
                            color = QColor(255, 170, 0, 50)
                        else:
                            impact = "High Risk"
                            color = QColor(255, 68, 68, 50)
                    else:
                        impact = "Neutral"
                        color = QColor(255, 255, 255, 20)
                    
                    # Update table items
                    value_item = QTableWidgetItem(value_text)
                    impact_item = QTableWidgetItem(impact)
                    impact_item.setBackground(color)
                    
                    self.factors_table.setItem(i, 1, value_item)
                    self.factors_table.setItem(i, 2, impact_item)
                    
        except Exception as e:
            print(f"Error updating risk factors: {e}")
