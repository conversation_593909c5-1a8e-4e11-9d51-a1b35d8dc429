"""
Configuration for Trading System
"""
from dataclasses import dataclass
from typing import Dict, Any
import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).resolve().parent.parent
ASSETS_DIR = BASE_DIR / "assets"
MODELS_DIR = BASE_DIR / "models"  # This can be changed if models are stored elsewhere
DATA_DIR = BASE_DIR / "data"
LOG_DIR = BASE_DIR / "logs"

# Create directories if they don't exist
for directory in [ASSETS_DIR, MODELS_DIR, DATA_DIR, LOG_DIR]:
    os.makedirs(directory, exist_ok=True)

# Logging configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = str(LOG_DIR / "trading_system.log")

@dataclass
class LlamaConfig:
    use_mock: bool = False
    # You can change these paths to point to your actual model locations
    model_path: str = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
    guard_path: str = str(MODELS_DIR / 'Llama-Guard-3-8B.gguf')
    bin_path: str = r"C:\Users\<USER>\Documents\dev\llama.cpp\main"
    context_size: int = 4096
    temperature: float = 0.1
    top_p: float = 0.9
    max_tokens: int = 1024

@dataclass
class SystemConfig:
    log_level: str = LOG_LEVEL
    log_file: str = LOG_FILE
    log_format: str = LOG_FORMAT

@dataclass
class Config:
    llama: LlamaConfig = LlamaConfig()
    system: SystemConfig = SystemConfig()

# Create a global config instance
config = Config()

# Default trading parameters
DEFAULT_SYMBOL = "BTC-USD"
DEFAULT_INTERVAL = "1m"
DEFAULT_PERIOD = "1d"
DEFAULT_DELAY = 60  # seconds

# LLaMA configuration
USE_MOCK_LLAMA = False
# You can change these paths to point to your actual model locations
LLAMA_MODEL_PATH = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf"
LLAMA_GUARD_PATH = str(MODELS_DIR / 'Llama-Guard-3-8B.gguf')
LLAMA_BIN_PATH = r"C:\Users\<USER>\Documents\dev\llama.cpp\main"

# Trading configuration
USE_MOCK_DATA = True  # Changed to True to avoid API errors
USE_EXCHANGE = False  # Changed to False to avoid API errors
USE_LIVE_DATA = False  # Changed to False to avoid API errors

# Exchange configuration
EXCHANGE_ID = "htx"
EXCHANGE_API_KEY = "tycf4rw2-72d300ec-fb900970-27ef8"
EXCHANGE_SECRET = "b4d92e15-523563a0-72a16ad9-9a275"

# Feature extraction parameters
RSI_PERIOD = 14
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9
BOLLINGER_WINDOW = 20
BOLLINGER_STD = 2

# These logging configurations are already defined above

def get_config():
    """
    Get the current configuration

    Returns:
        dict: Configuration dictionary
    """
    return {
        'default_symbol': DEFAULT_SYMBOL,
        'default_interval': DEFAULT_INTERVAL,
        'default_period': DEFAULT_PERIOD,
        'default_delay': DEFAULT_DELAY,
        'use_mock_llama': USE_MOCK_LLAMA,
        'llama_model_path': LLAMA_MODEL_PATH,
        'llama_guard_path': LLAMA_GUARD_PATH,
        'llama_bin_path': LLAMA_BIN_PATH,
        'use_mock_data': USE_MOCK_DATA,
        'use_exchange': USE_EXCHANGE,
        'use_live_data': USE_LIVE_DATA,
        'exchange_id': EXCHANGE_ID,
        'exchange_api_key': EXCHANGE_API_KEY,
        'exchange_secret': EXCHANGE_SECRET
    }

