"""
AI Analysis Panel
Displays LLM/AI analysis results and reasoning
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QTextEdit, QFrame, QProgressBar
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class AIAnalysisPanel(QWidget):
    """Panel displaying AI/LLM analysis and decision making"""

    def __init__(self):
        super().__init__()
        self.current_theme = "dark_professional"
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # AI Decision Summary (remove redundant header)
        decision_group = QGroupBox()  # No title to reduce clutter
        decision_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #00AA00;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #001100;
            }
        """)
        decision_layout = QGridLayout(decision_group)
        
        # Decision display
        self.decision_label = QLabel("WAIT")
        self.decision_label.setFont(QFont("Arial", 20, QFont.Bold))
        self.decision_label.setAlignment(Qt.AlignCenter)
        
        self.confidence_label = QLabel("0%")
        self.confidence_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.confidence_label.setAlignment(Qt.AlignCenter)
        
        # Confidence progress bar
        self.confidence_bar = QProgressBar()
        self.confidence_bar.setRange(0, 100)
        self.confidence_bar.setValue(0)
        self.confidence_bar.setTextVisible(True)
        
        decision_layout.addWidget(QLabel("Decision:"), 0, 0)
        decision_layout.addWidget(self.decision_label, 0, 1, 1, 2)
        decision_layout.addWidget(QLabel("Confidence:"), 1, 0)
        decision_layout.addWidget(self.confidence_label, 1, 1)
        decision_layout.addWidget(self.confidence_bar, 1, 2)
        
        layout.addWidget(decision_group)
        
        # Risk Parameters (remove redundant header)
        risk_group = QGroupBox()  # No title to reduce clutter
        risk_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #00AA00;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #001100;
            }
        """)
        risk_layout = QGridLayout(risk_group)
        
        self.take_profit_label = QLabel("0.00%")
        self.take_profit_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.stop_loss_label = QLabel("0.00%")
        self.stop_loss_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.risk_reward_label = QLabel("1:1")
        self.risk_reward_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        risk_layout.addWidget(QLabel("Take Profit:"), 0, 0)
        risk_layout.addWidget(self.take_profit_label, 0, 1)
        risk_layout.addWidget(QLabel("Stop Loss:"), 0, 2)
        risk_layout.addWidget(self.stop_loss_label, 0, 3)
        risk_layout.addWidget(QLabel("Risk/Reward:"), 1, 0)
        risk_layout.addWidget(self.risk_reward_label, 1, 1)
        
        risk_layout.addWidget(QLabel("Risk:Reward:"), 1, 0)
        risk_layout.addWidget(self.risk_reward_label, 1, 1)
        
        layout.addWidget(risk_group)
        
        # AI Reasoning
        reasoning_group = QGroupBox("🧠 AI Reasoning & Analysis")
        reasoning_layout = QVBoxLayout(reasoning_group)
        
        self.reasoning_text = QTextEdit()
        self.reasoning_text.setMaximumHeight(150)
        self.reasoning_text.setReadOnly(True)
        self.reasoning_text.setPlainText("No AI analysis available yet...")

        # Professional styling for text area
        self.reasoning_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
            }
        """)
        
        reasoning_layout.addWidget(self.reasoning_text)
        layout.addWidget(reasoning_group)
        
        # Model Information
        model_group = QGroupBox("🔧 Model Information")
        model_layout = QGridLayout(model_group)
        
        self.model_name_label = QLabel("Mock Model")
        self.model_status_label = QLabel("🔴 Disconnected")
        self.last_analysis_label = QLabel("--")
        self.analysis_time_label = QLabel("--")
        
        model_layout.addWidget(QLabel("Model:"), 0, 0)
        model_layout.addWidget(self.model_name_label, 0, 1)
        model_layout.addWidget(QLabel("Status:"), 0, 2)
        model_layout.addWidget(self.model_status_label, 0, 3)
        
        model_layout.addWidget(QLabel("Last Analysis:"), 1, 0)
        model_layout.addWidget(self.last_analysis_label, 1, 1)
        model_layout.addWidget(QLabel("Analysis Time:"), 1, 2)
        model_layout.addWidget(self.analysis_time_label, 1, 3)
        
        layout.addWidget(model_group)
        
        # Analysis Quality Metrics
        quality_group = QGroupBox("📊 Analysis Quality")
        quality_layout = QGridLayout(quality_group)
        
        self.reasoning_length_label = QLabel("0 words")
        self.decision_clarity_label = QLabel("--")
        self.risk_assessment_label = QLabel("--")
        
        quality_layout.addWidget(QLabel("Reasoning Length:"), 0, 0)
        quality_layout.addWidget(self.reasoning_length_label, 0, 1)
        quality_layout.addWidget(QLabel("Decision Clarity:"), 0, 2)
        quality_layout.addWidget(self.decision_clarity_label, 0, 3)
        
        quality_layout.addWidget(QLabel("Risk Assessment:"), 1, 0)
        quality_layout.addWidget(self.risk_assessment_label, 1, 1)
        
        layout.addWidget(quality_group)
    
    def update_data(self, data):
        """Update the panel with new AI analysis data"""
        try:
            # Update AI decision
            decision = data.get('decision', 'WAIT').upper()
            confidence = data.get('confidence', 0.0)
            
            self.decision_label.setText(decision)
            self.confidence_label.setText(f"{confidence:.0f}%")
            
            # Apply theme-specific decision styling
            self.apply_decision_styling(decision)
            
            # Update confidence bar with theme-specific styling
            self.confidence_bar.setValue(int(confidence))
            self.apply_confidence_bar_styling(confidence)
            
            # Update risk parameters
            take_profit = data.get('take_profit', 0.0)
            stop_loss = data.get('stop_loss', 0.0)
            
            self.take_profit_label.setText(f"{take_profit:.2f}%")
            self.take_profit_label.setStyleSheet("""
                QLabel {
                    color: #00FF00;
                    font-weight: bold;
                    background-color: #00FF0015;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-family: 'Courier New', monospace;
                }
            """)

            self.stop_loss_label.setText(f"{stop_loss:.2f}%")
            self.stop_loss_label.setStyleSheet("""
                QLabel {
                    color: #FF0000;
                    font-weight: bold;
                    background-color: #FF000015;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-family: 'Courier New', monospace;
                }
            """)

            # Calculate and display Risk/Reward ratio with color coding
            if stop_loss > 0:
                risk_reward_ratio = take_profit / stop_loss

                # Color code based on R:R ratio
                if risk_reward_ratio > 1.0:
                    rr_color = "#00FF00"  # Green for good R:R
                    rr_bg = "#00FF0015"
                    rr_icon = "🟢"
                elif risk_reward_ratio >= 0.8:
                    rr_color = "#FFFF00"  # Yellow for acceptable R:R
                    rr_bg = "#FFFF0015"
                    rr_icon = "🟡"
                else:
                    rr_color = "#FF0000"  # Red for poor R:R
                    rr_bg = "#FF000015"
                    rr_icon = "🔴"

                # Update risk/reward display if it exists
                if hasattr(self, 'risk_reward_label'):
                    self.risk_reward_label.setText(f"{rr_icon} R:R 1:{risk_reward_ratio:.1f}")
                    self.risk_reward_label.setStyleSheet(f"""
                        QLabel {{
                            color: {rr_color};
                            font-weight: bold;
                            background-color: {rr_bg};
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-family: 'Courier New', monospace;
                        }}
                    """)
            
            # Calculate risk:reward ratio
            if stop_loss > 0:
                risk_reward = take_profit / stop_loss
                self.risk_reward_label.setText(f"1:{risk_reward:.1f}")
                
                if risk_reward >= 2:
                    self.risk_reward_label.setStyleSheet("color: #00ff88;")
                elif risk_reward >= 1.5:
                    self.risk_reward_label.setStyleSheet("color: #ffaa00;")
                else:
                    self.risk_reward_label.setStyleSheet("color: #ff4444;")
            
            # Update reasoning
            reasoning = data.get('reasoning', 'No analysis available')
            self.reasoning_text.setPlainText(reasoning)
            
            # Update model information
            model_info = data.get('model_info', 'Unknown Model')
            self.model_name_label.setText(model_info)
            
            # Determine model status based on recent analysis
            analysis_timestamp = data.get('analysis_timestamp', '')
            if analysis_timestamp:
                self.model_status_label.setText("🟢 Active")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        color: #00D4AA;
                        font-weight: bold;
                        background-color: #00D4AA15;
                        padding: 2px 6px;
                        border-radius: 3px;
                    }
                """)
                self.last_analysis_label.setText(analysis_timestamp.split('T')[1][:8] if 'T' in analysis_timestamp else analysis_timestamp)
            else:
                self.model_status_label.setText("🔴 Inactive")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF4757;
                        font-weight: bold;
                        background-color: #FF475715;
                        padding: 2px 6px;
                        border-radius: 3px;
                    }
                """)
            
            # Update analysis quality metrics
            self.update_quality_metrics(reasoning, decision, confidence)
            
        except Exception as e:
            print(f"Error updating AI analysis panel: {e}")
    
    def update_quality_metrics(self, reasoning, decision, confidence):
        """Update analysis quality metrics"""
        try:
            # Reasoning length
            word_count = len(reasoning.split()) if reasoning else 0
            self.reasoning_length_label.setText(f"{word_count} words")
            
            # Color code reasoning length with professional colors
            if word_count >= 50:
                self.reasoning_length_label.setStyleSheet("color: #00D4AA; font-weight: bold;")
            elif word_count >= 20:
                self.reasoning_length_label.setStyleSheet("color: #FFA726; font-weight: bold;")
            else:
                self.reasoning_length_label.setStyleSheet("color: #FF4757; font-weight: bold;")
            
            # Decision clarity based on confidence and decision type
            if decision != 'WAIT' and confidence >= 70:
                clarity = "High"
                clarity_color = "#00D4AA"
            elif decision != 'WAIT' and confidence >= 50:
                clarity = "Medium"
                clarity_color = "#FFA726"
            elif decision == 'WAIT':
                clarity = "Cautious"
                clarity_color = "#FFA726"
            else:
                clarity = "Low"
                clarity_color = "#FF4757"
            
            self.decision_clarity_label.setText(clarity)
            self.decision_clarity_label.setStyleSheet(f"color: {clarity_color}; font-weight: bold;")
            
            # Risk assessment based on reasoning content
            risk_keywords = ['risk', 'stop', 'loss', 'volatility', 'caution']
            risk_mentions = sum(1 for keyword in risk_keywords if keyword in reasoning.lower())
            
            if risk_mentions >= 3:
                risk_assessment = "Comprehensive"
                risk_color = "#00D4AA"
            elif risk_mentions >= 1:
                risk_assessment = "Basic"
                risk_color = "#FFA726"
            else:
                risk_assessment = "Limited"
                risk_color = "#FF4757"

            self.risk_assessment_label.setText(risk_assessment)
            self.risk_assessment_label.setStyleSheet(f"color: {risk_color}; font-weight: bold;")
            
        except Exception as e:
            print(f"Error updating quality metrics: {e}")

    def apply_theme(self, theme_name):
        """Apply theme-specific styling to the panel"""
        self.current_theme = theme_name

        if theme_name == "matrix":
            self.apply_matrix_theme()
        elif theme_name == "light":
            self.apply_light_theme()
        elif theme_name == "high_contrast":
            self.apply_high_contrast_theme()
        else:  # dark_professional
            self.apply_dark_professional_theme()

    def apply_matrix_theme(self):
        """Apply Matrix theme styling"""
        # Text area styling
        self.reasoning_text.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #00FF00;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                line-height: 1.4;
            }
        """)

        # Update decision colors for Matrix theme
        self.update_decision_colors_matrix()

        # Update confidence bar for Matrix theme
        self.update_confidence_bar_matrix()

    def apply_light_theme(self):
        """Apply Light theme styling"""
        self.reasoning_text.setStyleSheet("""
            QTextEdit {
                background-color: #FFFFFF;
                color: #000000;
                border: 1px solid #CCCCCC;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
            }
        """)

    def apply_high_contrast_theme(self):
        """Apply High Contrast theme styling"""
        self.reasoning_text.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #FFFFFF;
                border: 2px solid #FFFFFF;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Arial', sans-serif;
                font-size: 12px;
                font-weight: bold;
                line-height: 1.4;
            }
        """)

    def apply_dark_professional_theme(self):
        """Apply Dark Professional theme styling"""
        self.reasoning_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
            }
        """)

    def update_decision_colors_matrix(self):
        """Update decision colors for Matrix theme"""
        if hasattr(self, 'decision_label'):
            decision = self.decision_label.text()
            matrix_colors = {
                'LONG': '#00FF00',
                'SHORT': '#FF0000',
                'WAIT': '#FFFF00'
            }
            color = matrix_colors.get(decision, '#00FF00')
            self.decision_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    background-color: #001100;
                    border: 2px solid {color};
                    padding: 12px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 18px;
                    font-family: 'Courier New', monospace;
                }}
            """)

    def update_confidence_bar_matrix(self):
        """Update confidence bar for Matrix theme"""
        if hasattr(self, 'confidence_bar'):
            self.confidence_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #00AA00;
                    border-radius: 6px;
                    text-align: center;
                    background-color: #001100;
                    color: #00FF00;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Courier New', monospace;
                }
                QProgressBar::chunk {
                    background-color: #00AA00;
                    border-radius: 5px;
                }
            """)

    def apply_decision_styling(self, decision):
        """Apply theme-specific decision styling"""
        if self.current_theme == "matrix":
            matrix_colors = {
                'LONG': '#00FF00',
                'SHORT': '#FF0000',
                'WAIT': '#FFFF00'
            }
            color = matrix_colors.get(decision, '#00FF00')
            self.decision_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    background-color: #001100;
                    border: 2px solid {color};
                    padding: 12px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 18px;
                    font-family: 'Courier New', monospace;
                }}
            """)
        elif self.current_theme == "light":
            light_colors = {
                'LONG': '#007ACC',
                'SHORT': '#D83B01',
                'WAIT': '#FF8C00'
            }
            color = light_colors.get(decision, '#000000')
            self.decision_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    background-color: {color}15;
                    border: 2px solid {color};
                    padding: 12px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 18px;
                }}
            """)
        elif self.current_theme == "high_contrast":
            hc_colors = {
                'LONG': '#FFFFFF',
                'SHORT': '#FFFFFF',
                'WAIT': '#FFFF00'
            }
            bg_colors = {
                'LONG': '#008000',
                'SHORT': '#FF0000',
                'WAIT': '#000000'
            }
            color = hc_colors.get(decision, '#FFFFFF')
            bg_color = bg_colors.get(decision, '#000000')
            self.decision_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    background-color: {bg_color};
                    border: 3px solid {color};
                    padding: 12px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 18px;
                }}
            """)
        else:  # dark_professional
            professional_colors = {
                'LONG': '#00D4AA',
                'SHORT': '#FF4757',
                'WAIT': '#FFA726'
            }
            color = professional_colors.get(decision, '#FFFFFF')
            self.decision_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    background-color: {color}15;
                    border: 2px solid {color};
                    padding: 12px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 18px;
                }}
            """)

    def apply_confidence_bar_styling(self, confidence):
        """Apply theme-specific confidence bar styling"""
        if self.current_theme == "matrix":
            if confidence >= 80:
                bar_color = "#00FF00"
            elif confidence >= 60:
                bar_color = "#FFFF00"
            else:
                bar_color = "#FF0000"

            self.confidence_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid #00AA00;
                    border-radius: 6px;
                    text-align: center;
                    background-color: #001100;
                    color: #00FF00;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Courier New', monospace;
                }}
                QProgressBar::chunk {{
                    background-color: {bar_color};
                    border-radius: 5px;
                }}
            """)
        elif self.current_theme == "light":
            if confidence >= 80:
                bar_color = "#007ACC"
            elif confidence >= 60:
                bar_color = "#FF8C00"
            else:
                bar_color = "#D83B01"

            self.confidence_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid #CCCCCC;
                    border-radius: 6px;
                    text-align: center;
                    background-color: #F5F5F5;
                    color: #000000;
                    font-weight: bold;
                    font-size: 11px;
                }}
                QProgressBar::chunk {{
                    background-color: {bar_color};
                    border-radius: 5px;
                }}
            """)
        elif self.current_theme == "high_contrast":
            if confidence >= 80:
                bar_color = "#00FF00"
            elif confidence >= 60:
                bar_color = "#FFFF00"
            else:
                bar_color = "#FF0000"

            self.confidence_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 2px solid #FFFFFF;
                    border-radius: 6px;
                    text-align: center;
                    background-color: #000000;
                    color: #FFFFFF;
                    font-weight: bold;
                    font-size: 12px;
                }}
                QProgressBar::chunk {{
                    background-color: {bar_color};
                    border-radius: 4px;
                }}
            """)
        else:  # dark_professional
            if confidence >= 80:
                bar_color = "#00D4AA"
            elif confidence >= 60:
                bar_color = "#FFA726"
            else:
                bar_color = "#FF4757"

            self.confidence_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid #555555;
                    border-radius: 6px;
                    text-align: center;
                    background-color: #2B2B2B;
                    color: #FFFFFF;
                    font-weight: bold;
                    font-size: 11px;
                }}
                QProgressBar::chunk {{
                    background-color: {bar_color};
                    border-radius: 5px;
                }}
            """)
