2025-04-20 09:09:20,265 - startup - INFO - Setting up environment for Epinnox Trading System
2025-04-20 09:09:20,267 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-20 09:09:21,757 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:09:21,757 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:09:21,757 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-04-20 09:09:21,758 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:09:21,758 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-04-20 09:09:21,759 - startup - INFO - GPU acceleration enabled
2025-04-20 09:09:21,759 - startup - INFO - Device: cuda
2025-04-20 09:09:21,759 - startup - INFO - Data Type: torch.bfloat16
2025-04-20 09:09:21,759 - startup - INFO - Device Name: NVIDIA GeForce RTX 4070 Laptop GPU
2025-04-20 09:09:21,760 - startup - INFO - CUDA Version: 12.1
2025-04-20 09:09:23,167 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-20 09:09:23,178 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-20 09:09:23,180 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-20 09:09:23,182 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-20 09:09:23,236 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-04-20 09:09:23,236 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-20 09:09:23,237 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-04-20 09:09:23,237 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-20 10:14:25,058 - __main__ - INFO - Starting Epinnox Trading System
2025-04-20 10:14:25,060 - __main__ - INFO - Setting up environment for Epinnox Trading System
2025-04-20 10:14:25,084 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-20 10:14:25,086 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-04-20 10:14:27,825 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 10:14:27,825 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 10:14:27,827 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 10:14:27,828 - models.model_cache - INFO - Model cache cleanup thread started
2025-04-20 10:14:27,828 - models.model_cache - INFO - Model cache initialized
2025-04-20 10:14:27,828 - models.model_cache - INFO - Model cache size set to 3
2025-04-23 13:41:16,644 - startup - INFO - Setting up environment for Epinnox Trading System
2025-04-23 13:41:16,644 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-23 13:41:16,644 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-04-23 13:41:18,224 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 11.8)
2025-04-23 13:41:18,224 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 11.8)
2025-04-23 13:41:18,229 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 11.8)
2025-04-23 13:41:18,233 - models.model_cache - INFO - Model cache cleanup thread started
2025-04-23 13:41:18,233 - models.model_cache - INFO - Model cache initialized
2025-04-23 13:41:18,233 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 11:49:39,671 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 11:49:39,672 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 11:49:39,674 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 11:49:41,358 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:49:41,358 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:49:41,360 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:49:41,360 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 11:49:41,361 - models.model_cache - INFO - Model cache initialized
2025-05-03 11:49:41,361 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 11:53:19,907 - __main__ - INFO - Starting Epinnox Trading System
2025-05-03 11:53:19,908 - __main__ - INFO - Setting up environment for Epinnox Trading System
2025-05-03 11:53:19,932 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 11:53:19,933 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 11:53:21,789 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:53:21,789 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:53:21,792 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:53:21,793 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 11:53:21,793 - models.model_cache - INFO - Model cache initialized
2025-05-03 11:53:21,793 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 12:23:48,166 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 12:23:48,168 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 12:23:48,170 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 12:23:51,295 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 12:23:51,296 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 12:23:51,297 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 12:23:51,299 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 12:23:51,299 - models.model_cache - INFO - Model cache initialized
2025-05-03 12:23:51,300 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 13:16:51,500 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 13:16:51,501 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 13:16:51,502 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 13:16:53,080 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:16:53,080 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:16:53,082 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:16:53,083 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 13:16:53,083 - models.model_cache - INFO - Model cache initialized
2025-05-03 13:16:53,084 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 13:17:17,609 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 13:17:17,610 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 13:17:17,611 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 13:17:19,110 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:17:19,110 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:17:19,112 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:17:19,112 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 13:17:19,112 - models.model_cache - INFO - Model cache initialized
2025-05-03 13:17:19,113 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 13:18:37,712 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 13:18:37,712 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 13:18:37,714 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 13:18:39,183 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:18:39,183 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:18:39,184 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:18:39,184 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 13:18:39,186 - models.model_cache - INFO - Model cache initialized
2025-05-03 13:18:39,186 - models.model_cache - INFO - Model cache size set to 3
2025-05-04 21:17:12,353 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-04 21:17:12,355 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-04 21:17:12,372 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-04 21:17:17,587 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:17,588 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:17,590 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:17,590 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-04 21:17:17,590 - models.model_cache - INFO - Model cache initialized
2025-05-04 21:17:17,592 - models.model_cache - INFO - Model cache size set to 3
2025-05-04 21:17:50,834 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-04 21:17:50,835 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-04 21:17:50,836 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-04 21:17:52,425 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:52,426 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:52,427 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:52,428 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-04 21:17:52,428 - models.model_cache - INFO - Model cache initialized
2025-05-04 21:17:52,429 - models.model_cache - INFO - Model cache size set to 3
2025-05-07 17:16:08,796 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-07 17:16:08,797 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-07 17:16:08,797 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-07 17:16:24,841 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:16:24,843 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:16:24,858 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:16:24,858 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-07 17:16:24,858 - models.model_cache - INFO - Model cache initialized
2025-05-07 17:16:24,865 - models.model_cache - INFO - Model cache size set to 3
2025-05-07 17:30:50,048 - __main__ - INFO - Starting Epinnox Trading System
2025-05-07 17:30:50,048 - __main__ - INFO - Setting up environment for Epinnox Trading System
2025-05-07 17:30:50,067 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-07 17:30:50,069 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-07 17:30:51,615 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:30:51,615 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:30:51,617 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:30:51,618 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-07 17:30:51,618 - models.model_cache - INFO - Model cache initialized
2025-05-07 17:30:51,618 - models.model_cache - INFO - Model cache size set to 3
2025-06-15 07:54:20,800 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 07:54:20,803 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 07:54:20,817 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 07:54:22,486 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 07:54:22,486 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 07:54:22,490 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 07:54:22,490 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 07:54:22,490 - models.model_cache - INFO - Model cache initialized
2025-06-15 07:54:22,490 - models.model_cache - INFO - Model cache size set to 3
2025-06-15 08:05:35,011 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:05:35,011 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:05:35,015 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:05:36,630 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:05:36,632 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:05:36,634 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:05:36,635 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:05:36,635 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:05:36,635 - models.model_cache - INFO - Model cache size set to 3
2025-06-15 08:11:12,622 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:11:12,623 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:11:12,623 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:11:12,623 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:11:12,623 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:11:14,195 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:11:14,199 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:11:14,199 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:11:14,199 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:11:14,199 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:11:14,199 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:11:14,199 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:11:15,560 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:11:15,579 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:15,582 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:15,583 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:15,655 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:11:15,655 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:11:15,655 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:11:15,655 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
