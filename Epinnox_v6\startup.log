2025-04-20 09:09:20,265 - startup - INFO - Setting up environment for Epinnox Trading System
2025-04-20 09:09:20,267 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-20 09:09:21,757 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:09:21,757 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:09:21,757 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-04-20 09:09:21,758 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:09:21,758 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-04-20 09:09:21,759 - startup - INFO - GPU acceleration enabled
2025-04-20 09:09:21,759 - startup - INFO - Device: cuda
2025-04-20 09:09:21,759 - startup - INFO - Data Type: torch.bfloat16
2025-04-20 09:09:21,759 - startup - INFO - Device Name: NVIDIA GeForce RTX 4070 Laptop GPU
2025-04-20 09:09:21,760 - startup - INFO - CUDA Version: 12.1
2025-04-20 09:09:23,167 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-20 09:09:23,178 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-20 09:09:23,180 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-20 09:09:23,182 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-20 09:09:23,236 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-04-20 09:09:23,236 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-20 09:09:23,237 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-04-20 09:09:23,237 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-20 10:14:25,058 - __main__ - INFO - Starting Epinnox Trading System
2025-04-20 10:14:25,060 - __main__ - INFO - Setting up environment for Epinnox Trading System
2025-04-20 10:14:25,084 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-20 10:14:25,086 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-04-20 10:14:27,825 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 10:14:27,825 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 10:14:27,827 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 10:14:27,828 - models.model_cache - INFO - Model cache cleanup thread started
2025-04-20 10:14:27,828 - models.model_cache - INFO - Model cache initialized
2025-04-20 10:14:27,828 - models.model_cache - INFO - Model cache size set to 3
2025-04-23 13:41:16,644 - startup - INFO - Setting up environment for Epinnox Trading System
2025-04-23 13:41:16,644 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-23 13:41:16,644 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-04-23 13:41:18,224 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 11.8)
2025-04-23 13:41:18,224 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 11.8)
2025-04-23 13:41:18,229 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 11.8)
2025-04-23 13:41:18,233 - models.model_cache - INFO - Model cache cleanup thread started
2025-04-23 13:41:18,233 - models.model_cache - INFO - Model cache initialized
2025-04-23 13:41:18,233 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 11:49:39,671 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 11:49:39,672 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 11:49:39,674 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 11:49:41,358 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:49:41,358 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:49:41,360 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:49:41,360 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 11:49:41,361 - models.model_cache - INFO - Model cache initialized
2025-05-03 11:49:41,361 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 11:53:19,907 - __main__ - INFO - Starting Epinnox Trading System
2025-05-03 11:53:19,908 - __main__ - INFO - Setting up environment for Epinnox Trading System
2025-05-03 11:53:19,932 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 11:53:19,933 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 11:53:21,789 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:53:21,789 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:53:21,792 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 11:53:21,793 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 11:53:21,793 - models.model_cache - INFO - Model cache initialized
2025-05-03 11:53:21,793 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 12:23:48,166 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 12:23:48,168 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 12:23:48,170 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 12:23:51,295 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 12:23:51,296 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 12:23:51,297 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 12:23:51,299 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 12:23:51,299 - models.model_cache - INFO - Model cache initialized
2025-05-03 12:23:51,300 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 13:16:51,500 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 13:16:51,501 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 13:16:51,502 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 13:16:53,080 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:16:53,080 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:16:53,082 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:16:53,083 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 13:16:53,083 - models.model_cache - INFO - Model cache initialized
2025-05-03 13:16:53,084 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 13:17:17,609 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 13:17:17,610 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 13:17:17,611 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 13:17:19,110 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:17:19,110 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:17:19,112 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:17:19,112 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 13:17:19,112 - models.model_cache - INFO - Model cache initialized
2025-05-03 13:17:19,113 - models.model_cache - INFO - Model cache size set to 3
2025-05-03 13:18:37,712 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-03 13:18:37,712 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-03 13:18:37,714 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-03 13:18:39,183 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:18:39,183 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:18:39,184 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-03 13:18:39,184 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-03 13:18:39,186 - models.model_cache - INFO - Model cache initialized
2025-05-03 13:18:39,186 - models.model_cache - INFO - Model cache size set to 3
2025-05-04 21:17:12,353 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-04 21:17:12,355 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-04 21:17:12,372 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-04 21:17:17,587 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:17,588 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:17,590 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:17,590 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-04 21:17:17,590 - models.model_cache - INFO - Model cache initialized
2025-05-04 21:17:17,592 - models.model_cache - INFO - Model cache size set to 3
2025-05-04 21:17:50,834 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-04 21:17:50,835 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-04 21:17:50,836 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-04 21:17:52,425 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:52,426 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:52,427 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-04 21:17:52,428 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-04 21:17:52,428 - models.model_cache - INFO - Model cache initialized
2025-05-04 21:17:52,429 - models.model_cache - INFO - Model cache size set to 3
2025-05-07 17:16:08,796 - startup - INFO - Setting up environment for Epinnox Trading System
2025-05-07 17:16:08,797 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-07 17:16:08,797 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-07 17:16:24,841 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:16:24,843 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:16:24,858 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:16:24,858 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-07 17:16:24,858 - models.model_cache - INFO - Model cache initialized
2025-05-07 17:16:24,865 - models.model_cache - INFO - Model cache size set to 3
2025-05-07 17:30:50,048 - __main__ - INFO - Starting Epinnox Trading System
2025-05-07 17:30:50,048 - __main__ - INFO - Setting up environment for Epinnox Trading System
2025-05-07 17:30:50,067 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-05-07 17:30:50,069 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-05-07 17:30:51,615 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:30:51,615 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:30:51,617 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-05-07 17:30:51,618 - models.model_cache - INFO - Model cache cleanup thread started
2025-05-07 17:30:51,618 - models.model_cache - INFO - Model cache initialized
2025-05-07 17:30:51,618 - models.model_cache - INFO - Model cache size set to 3
2025-06-15 07:54:20,800 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 07:54:20,803 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 07:54:20,817 - gpu_integration - INFO - Loaded GPU configuration from c:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 07:54:22,486 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 07:54:22,486 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 07:54:22,490 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 07:54:22,490 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 07:54:22,490 - models.model_cache - INFO - Model cache initialized
2025-06-15 07:54:22,490 - models.model_cache - INFO - Model cache size set to 3
2025-06-15 08:05:35,011 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:05:35,011 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:05:35,015 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:05:36,630 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:05:36,632 - gpu_integration - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:05:36,634 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:05:36,635 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:05:36,635 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:05:36,635 - models.model_cache - INFO - Model cache size set to 3
2025-06-15 08:11:12,622 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:11:12,623 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:11:12,623 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:11:12,623 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:11:12,623 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:11:14,195 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:11:14,199 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:11:14,199 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:11:14,199 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:11:14,199 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:11:14,199 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:11:14,199 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:11:15,560 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:11:15,579 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:15,582 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:15,583 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:15,655 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:11:15,655 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:11:15,655 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:11:15,655 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:11:51,601 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:11:51,601 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:11:51,605 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:11:51,605 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:11:51,605 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:11:53,178 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:11:53,180 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:11:53,181 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:11:53,181 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:11:53,181 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:11:53,181 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:11:53,181 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:11:54,318 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:11:54,330 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:54,331 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:54,333 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:11:54,401 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:11:54,401 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:11:54,401 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:11:54,401 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:12:02,125 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:12:02,125 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:12:02,125 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:12:02,125 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:12:02,125 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:12:03,708 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:12:03,708 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:12:03,719 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:12:03,719 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:12:03,719 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:12:03,719 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:12:03,719 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:12:04,821 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:12:04,838 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:12:04,838 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:12:04,838 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:12:04,904 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:12:04,904 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:12:04,904 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:12:04,904 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:12:04,904 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:12:10,377 - data.exchange - INFO - Cached markets data
2025-06-15 08:12:10,377 - data.exchange - INFO - Connected to htx exchange (spot and futures)
2025-06-15 08:12:10,377 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:12:10,377 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:12:10,636 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-06-15 08:12:10,892 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-06-15 08:12:11,150 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-06-15 08:12:11,407 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-06-15 08:12:11,809 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-06-15 08:12:12,073 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-06-15 08:12:12,073 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-06-15 08:12:12,090 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:12:12,090 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:12,090 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:12:12,090 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:12:12,090 - core.multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-06-15 08:12:12,343 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-06-15 08:12:12,343 - core.multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-06-15 08:12:12,596 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-06-15 08:12:12,596 - core.multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-06-15 08:12:12,596 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:12,608 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:12:12,625 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:12:12,625 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:12,625 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:12:12,631 - core.multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-06-15 08:12:12,636 - core.multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-06-15 08:12:12,640 - core.multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-06-15 08:12:12,641 - core.multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:12:12,641 - core.multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:12:12,641 - core.multi_timeframe - INFO - Analyzed trend for 15m timeframe: bearish (score: -0.33)
2025-06-15 08:12:12,642 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:12:12,642 - core.market_regime - INFO - Initialized market regime detector
2025-06-15 08:12:12,642 - core.market_regime - INFO - Detected market regime: low_volatility
2025-06-15 08:12:12,642 - core.market_regime - INFO - Regime metrics - Trend strength: 0.33, Volatility: 0.22%, Alignment: 0.67
2025-06-15 08:12:12,642 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:12:12,642 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor': 0.8223036861454263, 'stop_loss_factor': 1.0847483474386233, 'take_profit_factor': 0.9009952904175134, 'entry_confidence': 0.6649930940148552}
2025-06-15 08:12:12,642 - core.signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-06-15 08:12:12,642 - core.signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:12:12,642 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:12:12,642 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:12:12,644 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:12:12,644 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-06-15 08:12:12,644 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:12:12,644 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-06-15 08:12:12,644 - llama.runner - INFO - Using mock LLaMA implementation (configured or model/binary not found)
2025-06-15 08:12:12,644 - llama.runner - INFO - Running mock LLaMA inference
2025-06-15 08:12:12,644 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-06-15 08:12:12,644 - __main__ - INFO - Decision for DOGE/USDT: WAIT
2025-06-15 08:12:12,644 - __main__ - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (2 each). Better to wait for clearer market direction.
2025-06-15 08:12:45,108 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:12:45,108 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:12:45,108 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:12:45,108 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:12:45,108 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:12:46,655 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:12:46,657 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:12:46,657 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:12:46,658 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:12:46,658 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:12:46,658 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:12:46,658 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:12:47,772 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:12:47,784 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:12:47,785 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:12:47,786 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:12:47,856 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:12:47,856 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:12:47,857 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:12:47,857 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:12:47,859 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:12:47,912 - data.exchange - INFO - Loaded markets from cache
2025-06-15 08:12:47,912 - data.exchange - INFO - Connected to htx exchange (spot and futures)
2025-06-15 08:12:47,912 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:12:47,912 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:12:47,917 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:12:47,918 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT:USDT (future)
2025-06-15 08:12:48,570 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-06-15 08:12:49,337 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-06-15 08:12:49,733 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-06-15 08:12:50,005 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-06-15 08:12:50,007 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-06-15 08:12:50,012 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:12:50,012 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:50,012 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:12:50,013 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:12:50,013 - core.multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-06-15 08:12:50,014 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:12:50,015 - core.multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-06-15 08:12:50,016 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:12:50,016 - core.multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-06-15 08:12:50,016 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:50,024 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:12:50,033 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:12:50,034 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:50,034 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:12:50,040 - core.multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-06-15 08:12:50,043 - core.multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-06-15 08:12:50,049 - core.multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-06-15 08:12:50,050 - core.multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:12:50,050 - core.multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:12:50,050 - core.multi_timeframe - INFO - Analyzed trend for 15m timeframe: bearish (score: -0.33)
2025-06-15 08:12:50,050 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:12:50,051 - core.market_regime - INFO - Initialized market regime detector
2025-06-15 08:12:50,051 - core.market_regime - INFO - Detected market regime: low_volatility
2025-06-15 08:12:50,051 - core.market_regime - INFO - Regime metrics - Trend strength: 0.33, Volatility: 0.22%, Alignment: 0.67
2025-06-15 08:12:50,051 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:12:50,051 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor': 0.8223036861454263, 'stop_loss_factor': 1.0847483474386233, 'take_profit_factor': 0.9009952904175134, 'entry_confidence': 0.6649930940148552}
2025-06-15 08:12:50,051 - core.signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-06-15 08:12:50,052 - core.signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:12:50,052 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:12:50,052 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:12:50,053 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:12:50,053 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-06-15 08:12:50,053 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:12:50,053 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-06-15 08:12:50,053 - llama.runner - INFO - Using mock LLaMA implementation (configured or model/binary not found)
2025-06-15 08:12:50,053 - llama.runner - INFO - Running mock LLaMA inference
2025-06-15 08:12:50,054 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-06-15 08:12:50,055 - core.adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.16%, TP: 0.18% (ATR: 0.09%)
2025-06-15 08:12:50,055 - __main__ - INFO - Using adaptive take profit: 0.18%
2025-06-15 08:12:50,055 - __main__ - INFO - Using adaptive stop loss: 0.16%
2025-06-15 08:12:50,055 - core.adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-06-15 08:12:50,055 - __main__ - INFO - Position scaling strategy: 0 stages
2025-06-15 08:12:50,055 - __main__ - INFO - Decision for DOGE/USDT: SHORT
2025-06-15 08:12:50,055 - __main__ - INFO - Explanation: Multiple bearish signals detected: MACD shows bearish momentum. Recent trades show selling activity. Futures market has higher buy ratio than spot, suggesting potential reversal. These factors suggest downward price movement.
2025-06-15 08:12:50,056 - __main__ - INFO - Take Profit: 0.1815470084635197%
2025-06-15 08:12:50,056 - __main__ - INFO - Stop Loss: 0.15734074066837742%
2025-06-15 08:12:50,056 - __main__ - INFO - Trading system finished
2025-06-15 08:14:06,849 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:14:06,853 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:14:06,853 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:14:06,853 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:14:06,853 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:14:08,466 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:14:08,466 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:14:08,470 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:14:08,470 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:14:08,470 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:14:08,470 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:14:08,470 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:14:09,607 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:14:09,619 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:14:09,620 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:14:09,622 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:14:09,694 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:14:09,694 - llama.runner - INFO - Using model path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:14:09,694 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:14:09,694 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:14:09,696 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:14:09,697 - __main__ - INFO - Running continuously with 60s delay
2025-06-15 08:14:09,749 - data.exchange - INFO - Loaded markets from cache
2025-06-15 08:14:09,749 - data.exchange - INFO - Connected to htx exchange (spot and futures)
2025-06-15 08:14:09,749 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:14:09,749 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:14:09,749 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:14:09,753 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT:USDT (future)
2025-06-15 08:14:10,399 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-06-15 08:14:10,874 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-06-15 08:14:11,271 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-06-15 08:14:11,544 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-06-15 08:14:11,544 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-06-15 08:14:11,551 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:14:11,551 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:14:11,551 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:14:11,553 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:14:11,553 - core.multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-06-15 08:14:11,554 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:14:11,554 - core.multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-06-15 08:14:11,555 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:14:11,555 - core.multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-06-15 08:14:11,556 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:14:11,564 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:14:11,573 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:14:11,574 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:14:11,574 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:14:11,579 - core.multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-06-15 08:14:11,583 - core.multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-06-15 08:14:11,586 - core.multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-06-15 08:14:11,586 - core.multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:14:11,586 - core.multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:14:11,590 - core.multi_timeframe - INFO - Analyzed trend for 15m timeframe: bearish (score: -0.33)
2025-06-15 08:14:11,590 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:14:11,590 - core.market_regime - INFO - Initialized market regime detector
2025-06-15 08:14:11,590 - core.market_regime - INFO - Detected market regime: low_volatility
2025-06-15 08:14:11,590 - core.market_regime - INFO - Regime metrics - Trend strength: 0.33, Volatility: 0.22%, Alignment: 0.67
2025-06-15 08:14:11,591 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:14:11,591 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor': 0.8223036861454263, 'stop_loss_factor': 1.0847483474386233, 'take_profit_factor': 0.9009952904175134, 'entry_confidence': 0.6649930940148552}
2025-06-15 08:14:11,591 - core.signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-06-15 08:14:11,591 - core.signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:14:11,592 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:14:11,592 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:14:11,592 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:14:11,592 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-06-15 08:14:11,593 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:14:11,593 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-06-15 08:14:11,593 - llama.runner - INFO - Using mock LLaMA implementation (configured or model/binary not found)
2025-06-15 08:14:11,593 - llama.runner - INFO - Running mock LLaMA inference
2025-06-15 08:14:11,594 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-06-15 08:14:11,594 - __main__ - INFO - Decision for DOGE/USDT: WAIT
2025-06-15 08:14:11,594 - __main__ - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-06-15 08:15:11,710 - data.exchange - INFO - Loaded markets from cache
2025-06-15 08:15:11,710 - data.exchange - INFO - Connected to htx exchange (spot and futures)
2025-06-15 08:15:11,710 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:15:11,710 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:15:11,722 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:15:11,722 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT:USDT (future)
2025-06-15 08:15:12,352 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-06-15 08:15:13,090 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-06-15 08:15:13,489 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-06-15 08:15:13,756 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-06-15 08:15:13,759 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-06-15 08:15:13,761 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:15:13,761 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:15:13,761 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:15:13,764 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:15:13,764 - core.multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-06-15 08:15:13,765 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:15:13,765 - core.multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-06-15 08:15:13,767 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:15:13,767 - core.multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-06-15 08:15:13,767 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:15:13,777 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:15:13,786 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:15:13,787 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:15:13,787 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:15:13,790 - core.multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-06-15 08:15:13,795 - core.multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-06-15 08:15:13,800 - core.multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-06-15 08:15:13,800 - core.multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:15:13,800 - core.multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:15:13,801 - core.multi_timeframe - INFO - Analyzed trend for 15m timeframe: bearish (score: -0.33)
2025-06-15 08:15:13,801 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:15:13,802 - core.market_regime - INFO - Initialized market regime detector
2025-06-15 08:15:13,802 - core.market_regime - INFO - Detected market regime: low_volatility
2025-06-15 08:15:13,802 - core.market_regime - INFO - Regime metrics - Trend strength: 0.33, Volatility: 0.22%, Alignment: 0.67
2025-06-15 08:15:13,802 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:15:13,802 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor': 0.8223036861454263, 'stop_loss_factor': 1.0847483474386233, 'take_profit_factor': 0.9009952904175134, 'entry_confidence': 0.6649930940148552}
2025-06-15 08:15:13,803 - core.signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-06-15 08:15:13,803 - core.signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:15:13,803 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:15:13,803 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0, 'volume_score': 0.060172236685842304, 'price_action_score': -0.06004632561372948, 'trend_score': 0.0, 'total_score': -0.024005472579932657, 'confidence': 49.24982898187711, 'alignment': 50.0}
2025-06-15 08:15:13,804 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-06-15 08:15:13,804 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-06-15 08:15:13,804 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:15:13,804 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-06-15 08:15:13,804 - llama.runner - INFO - Using mock LLaMA implementation (configured or model/binary not found)
2025-06-15 08:15:13,804 - llama.runner - INFO - Running mock LLaMA inference
2025-06-15 08:15:13,804 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-06-15 08:15:13,804 - __main__ - INFO - Decision for DOGE/USDT: WAIT
2025-06-15 08:15:13,806 - __main__ - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-06-15 08:17:12,258 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:17:12,259 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:17:12,260 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:17:12,260 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:17:12,260 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:17:13,832 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:17:13,832 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:17:13,832 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:17:13,832 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:17:13,832 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:17:13,832 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:17:13,832 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:17:14,963 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:17:14,974 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:17:14,976 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:17:14,977 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:17:15,042 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:17:15,042 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-15 08:17:15,046 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:17:15,046 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:17:15,046 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:17:21,400 - data.exchange - INFO - Cached markets data
2025-06-15 08:17:21,400 - data.exchange - INFO - Connected to htx exchange (spot and futures)
2025-06-15 08:17:21,400 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:17:21,400 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:17:21,657 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-06-15 08:17:21,922 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-06-15 08:17:22,159 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-06-15 08:17:22,424 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-06-15 08:17:22,825 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-06-15 08:17:23,090 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-06-15 08:17:23,092 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-06-15 08:17:23,096 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:17:23,096 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:17:23,096 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:17:23,097 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:17:23,097 - core.multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-06-15 08:17:23,337 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-06-15 08:17:23,337 - core.multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-06-15 08:17:23,602 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-06-15 08:17:23,602 - core.multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-06-15 08:17:23,602 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:17:23,615 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:17:23,624 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:17:23,625 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:17:23,625 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:17:23,629 - core.multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-06-15 08:17:23,633 - core.multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-06-15 08:17:23,638 - core.multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-06-15 08:17:23,638 - core.multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:17:23,640 - core.multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:17:23,640 - core.multi_timeframe - INFO - Analyzed trend for 15m timeframe: bearish (score: -0.33)
2025-06-15 08:17:23,640 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:17:23,641 - core.market_regime - INFO - Initialized market regime detector
2025-06-15 08:17:23,641 - core.market_regime - INFO - Detected market regime: low_volatility
2025-06-15 08:17:23,641 - core.market_regime - INFO - Regime metrics - Trend strength: 0.33, Volatility: 0.22%, Alignment: 0.67
2025-06-15 08:17:23,641 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:17:23,642 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449056553885, 'position_size_factor': 0.8222942780637245, 'stop_loss_factor': 1.0847785358271018, 'take_profit_factor': 0.9010064150614908, 'entry_confidence': 0.6650006452383264}
2025-06-15 08:17:23,642 - core.signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-06-15 08:17:23,642 - core.signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.023958910039551033, 'orderbook_score': 0.0, 'volume_score': -0.047429296436959055, 'price_action_score': 0.0600427996690159, 'trend_score': 0.0, 'total_score': -0.011345406807494192, 'confidence': 49.64545603726581, 'alignment': 62.5}
2025-06-15 08:17:23,642 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.65% and alignment 62.50%
2025-06-15 08:17:23,642 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.023958910039551033, 'orderbook_score': 0.0, 'volume_score': -0.047429296436959055, 'price_action_score': 0.0600427996690159, 'trend_score': 0.0, 'total_score': -0.011345406807494192, 'confidence': 49.64545603726581, 'alignment': 62.5}
2025-06-15 08:17:23,644 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:17:23,644 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-06-15 08:17:23,644 - llama.runner - INFO - Using mock LLaMA implementation (configured or model/binary not found)
2025-06-15 08:17:23,644 - llama.runner - INFO - Running mock LLaMA inference
2025-06-15 08:17:23,645 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-06-15 08:17:23,646 - core.adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.18%, TP: 0.21% (ATR: 0.10%)
2025-06-15 08:17:23,646 - __main__ - INFO - Using adaptive take profit: 0.21%
2025-06-15 08:17:23,646 - __main__ - INFO - Using adaptive stop loss: 0.18%
2025-06-15 08:17:23,646 - core.adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-06-15 08:17:23,647 - __main__ - INFO - Position scaling strategy: 0 stages
2025-06-15 08:17:23,647 - __main__ - INFO - Decision for DOGE/USDT: SHORT
2025-06-15 08:17:23,647 - __main__ - INFO - Explanation: Multiple bearish signals detected: MACD shows bearish momentum. Order book shows selling pressure. These factors suggest downward price movement.
2025-06-15 08:17:23,647 - __main__ - INFO - Take Profit: 0.2053085530305893%
2025-06-15 08:17:23,647 - __main__ - INFO - Stop Loss: 0.17793407929317845%
2025-06-15 08:17:23,648 - __main__ - INFO - Trading system finished
2025-06-15 08:18:25,933 - startup - INFO - Setting up environment for Epinnox Trading System
2025-06-15 08:18:25,935 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 08:18:25,937 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 08:18:25,937 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 08:18:25,937 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 08:18:27,524 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:18:27,524 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 08:18:27,524 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 08:18:27,524 - models.model_cache - INFO - Model cache initialized
2025-06-15 08:18:27,524 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 08:18:27,524 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 08:18:27,528 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 08:18:28,674 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 08:18:28,686 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:18:28,687 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:18:28,688 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 08:18:28,756 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 08:18:28,756 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-15 08:18:28,757 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 08:18:28,757 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:18:28,760 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:18:28,811 - data.exchange - INFO - Loaded markets from cache
2025-06-15 08:18:28,811 - data.exchange - INFO - Connected to htx exchange (spot and futures)
2025-06-15 08:18:28,811 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:18:28,811 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:18:28,815 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:18:28,815 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT:USDT (future)
2025-06-15 08:18:29,463 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-06-15 08:18:30,353 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-06-15 08:18:30,615 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-06-15 08:18:30,911 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-06-15 08:18:30,911 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-06-15 08:18:30,915 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:18:30,915 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:18:30,915 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:18:30,918 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:18:30,919 - core.multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-06-15 08:18:30,921 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:18:30,921 - core.multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-06-15 08:18:30,922 - data.exchange - INFO - Using cached OHLCV data for DOGE/USDT (spot)
2025-06-15 08:18:30,922 - core.multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-06-15 08:18:30,922 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:18:30,931 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:18:30,941 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:18:30,942 - core.multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-06-15 08:18:30,942 - core.multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-06-15 08:18:30,946 - core.multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-06-15 08:18:30,952 - core.multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-06-15 08:18:30,957 - core.multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-06-15 08:18:30,957 - core.multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:18:30,958 - core.multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-06-15 08:18:30,958 - core.multi_timeframe - INFO - Analyzed trend for 15m timeframe: bearish (score: -0.33)
2025-06-15 08:18:30,959 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:18:30,959 - core.market_regime - INFO - Initialized market regime detector
2025-06-15 08:18:30,959 - core.market_regime - INFO - Detected market regime: low_volatility
2025-06-15 08:18:30,959 - core.market_regime - INFO - Regime metrics - Trend strength: 0.33, Volatility: 0.22%, Alignment: 0.67
2025-06-15 08:18:30,959 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:18:30,959 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449056553885, 'position_size_factor': 0.8222942780637245, 'stop_loss_factor': 1.0847785358271018, 'take_profit_factor': 0.9010064150614908, 'entry_confidence': 0.6650006452383264}
2025-06-15 08:18:30,959 - core.signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-06-15 08:18:30,961 - core.signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.023958910039551033, 'orderbook_score': 0.0, 'volume_score': -0.047429296436959055, 'price_action_score': 0.0600427996690159, 'trend_score': 0.0, 'total_score': -0.011345406807494192, 'confidence': 49.64545603726581, 'alignment': 62.5}
2025-06-15 08:18:30,961 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.65% and alignment 62.50%
2025-06-15 08:18:30,962 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.023958910039551033, 'orderbook_score': 0.0, 'volume_score': -0.047429296436959055, 'price_action_score': 0.0600427996690159, 'trend_score': 0.0, 'total_score': -0.011345406807494192, 'confidence': 49.64545603726581, 'alignment': 62.5}
2025-06-15 08:18:30,963 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 08:18:30,963 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-06-15 08:18:33,000 - llama.lmstudio_runner - INFO - Connected to LMStudio. Available models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-15 08:18:35,016 - llama.runner - INFO - Using LMStudio for inference
2025-06-15 08:18:35,016 - llama.lmstudio_runner - INFO - Sending request to LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-15 08:18:41,051 - llama.lmstudio_runner - INFO - Successfully got response from LMStudio
2025-06-15 08:18:41,052 - core.adaptive_risk - INFO - Initialized adaptive risk manager
2025-06-15 08:18:41,052 - core.adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.18%, TP: 0.21% (ATR: 0.10%)
2025-06-15 08:18:41,052 - __main__ - INFO - Using adaptive take profit: 0.21%
2025-06-15 08:18:41,052 - __main__ - INFO - Using adaptive stop loss: 0.18%
2025-06-15 08:18:41,052 - core.adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-06-15 08:18:41,052 - __main__ - INFO - Position scaling strategy: 0 stages
2025-06-15 08:18:41,052 - __main__ - INFO - Decision for DOGE/USDT: LONG
2025-06-15 08:18:41,052 - __main__ - INFO - Explanation: The market regime is 'low volatility', which usually suggests a more stable and predictable environment for trading strategies like long positions to succeed, as lower volatility often indicates less aggressive price swings that can be exploited by bullish trends. Furthermore, the Multi-Timeframe Analysis shows high confidence (116.67%) in an overall Bullish Trend Direction with a moderate strength of 0.33 and alignment at 0.67 indicating signal agreement is above average which further supports taking a long position. The bullish price action, along with the positive indicators like MACD trending towards neutral (which typically means no immediate bearish reversal) despite being slightly negative, suggests that it's in favor of entering or holding onto a LONG position here for potential gains as reflected by this multi-faceted analysis.
2025-06-15 08:18:41,052 - __main__ - INFO - Take Profit: 0.2053085530305893%
2025-06-15 08:18:41,052 - __main__ - INFO - Stop Loss: 0.17793407929317845%
2025-06-15 08:18:41,052 - __main__ - INFO - Trading system finished
