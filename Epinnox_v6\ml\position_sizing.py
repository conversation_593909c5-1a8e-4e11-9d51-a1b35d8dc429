"""
Smart Position Sizing Module

This module implements intelligent position sizing based on market depth,
volume analysis, and liquidity to avoid making large market impacts.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

class SmartPositionSizer:
    """
    Intelligent position sizing to minimize market impact
    """
    
    def __init__(self, conservative_mode: bool = False):
        """Initialize smart position sizer with calibrated parameters"""
        self.volume_history = []
        self.trade_history = []

        # CALIBRATED PARAMETERS - Less conservative for moderate liquidity
        if conservative_mode:
            self.market_impact_threshold = 0.03   # 3% of average volume (more conservative)
            self.max_order_book_impact = 0.015    # 1.5% of order book depth
            self.liquidity_buffer = 0.4           # 40% safety buffer
            self.min_liquidity_score = 0.15       # Minimum liquidity to trade
        else:
            self.market_impact_threshold = 0.08   # 8% of average volume (less conservative)
            self.max_order_book_impact = 0.04     # 4% of order book depth
            self.liquidity_buffer = 0.2           # 20% safety buffer (reduced)
            self.min_liquidity_score = 0.08       # Lower minimum liquidity threshold

        # Dynamic thresholds based on market conditions
        self.dynamic_thresholds = {
            'high_volatility': {'impact_multiplier': 0.7, 'buffer_multiplier': 1.3},
            'low_volatility': {'impact_multiplier': 1.2, 'buffer_multiplier': 0.8},
            'trending': {'impact_multiplier': 1.1, 'buffer_multiplier': 0.9},
            'range_bound': {'impact_multiplier': 0.9, 'buffer_multiplier': 1.1}
        }

        logger.info(f"Smart Position Sizer initialized (conservative_mode: {conservative_mode})")
    
    def calculate_optimal_position_size(self, 
                                      base_position_size: float,
                                      market_data: Dict[str, Any],
                                      confidence: float,
                                      account_balance: float) -> Dict[str, Any]:
        """
        Calculate optimal position size based on market conditions
        
        Args:
            base_position_size: Base position size from risk management
            market_data: Current market data including order book and volume
            confidence: Model confidence (0-1)
            account_balance: Available account balance
            
        Returns:
            Dict with optimal position sizing information
        """
        try:
            # Extract market data
            order_book = market_data.get('order_book', {})
            recent_volume = market_data.get('volume_24h', 0)
            current_price = market_data.get('current_price', 0)
            recent_trades = market_data.get('recent_trades', [])
            
            # Calculate liquidity metrics
            liquidity_metrics = self._calculate_liquidity_metrics(
                order_book, recent_volume, recent_trades
            )
            
            # Calculate volume-based constraints
            volume_constraints = self._calculate_volume_constraints(
                recent_volume, recent_trades
            )
            
            # Calculate order book impact
            order_book_constraints = self._calculate_order_book_constraints(
                order_book, current_price
            )
            
            # Apply confidence scaling
            confidence_factor = self._calculate_confidence_factor(confidence)
            
            # Calculate final position size
            optimal_size = self._calculate_final_position_size(
                base_position_size,
                liquidity_metrics,
                volume_constraints,
                order_book_constraints,
                confidence_factor,
                account_balance
            )
            
            # Calculate execution strategy
            execution_strategy = self._calculate_execution_strategy(
                optimal_size, liquidity_metrics, order_book
            )
            
            result = {
                'optimal_position_size': optimal_size['position_size'],
                'position_usd': optimal_size['position_usd'],
                'max_safe_size': optimal_size['max_safe_size'],
                'liquidity_score': liquidity_metrics['liquidity_score'],
                'market_impact_estimate': optimal_size['market_impact_estimate'],
                'execution_strategy': execution_strategy,
                'constraints': {
                    'volume_limit': volume_constraints['max_position'],
                    'order_book_limit': order_book_constraints['max_position'],
                    'confidence_factor': confidence_factor
                },
                'recommendations': self._generate_recommendations(optimal_size, liquidity_metrics)
            }
            
            logger.info(f"Optimal position size calculated: {optimal_size['position_size']:.2f} "
                       f"(${optimal_size['position_usd']:.2f}, impact: {optimal_size['market_impact_estimate']:.3f}%)")
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating optimal position size: {e}")
            return {
                'optimal_position_size': base_position_size * 0.5,  # Conservative fallback
                'position_usd': base_position_size * 0.5 * current_price,
                'market_impact_estimate': 0.1,
                'execution_strategy': {'type': 'single_order', 'splits': 1}
            }
    
    def _calculate_liquidity_metrics(self, order_book: Dict, volume_24h: float, recent_trades: List) -> Dict[str, float]:
        """Calculate market liquidity metrics"""
        try:
            metrics = {
                'liquidity_score': 0.5,
                'bid_ask_spread': 0.001,
                'order_book_depth': 0,
                'volume_consistency': 0.5,
                'trade_frequency': 0
            }
            
            # Calculate bid-ask spread
            if order_book and 'bids' in order_book and 'asks' in order_book:
                bids = order_book['bids']
                asks = order_book['asks']
                
                if bids and asks:
                    best_bid = float(bids[0][0]) if isinstance(bids[0], list) else float(bids[0])
                    best_ask = float(asks[0][0]) if isinstance(asks[0], list) else float(asks[0])
                    
                    if best_bid > 0 and best_ask > 0:
                        spread = (best_ask - best_bid) / best_bid
                        metrics['bid_ask_spread'] = spread
                        
                        # Calculate order book depth (total volume in top 5 levels)
                        bid_depth = sum(float(bid[1]) if isinstance(bid, list) else 0 for bid in bids[:5])
                        ask_depth = sum(float(ask[1]) if isinstance(ask, list) else 0 for ask in asks[:5])
                        metrics['order_book_depth'] = bid_depth + ask_depth
            
            # Calculate volume consistency
            if recent_trades:
                trade_volumes = [float(trade.get('amount', 0)) for trade in recent_trades[-20:]]
                if trade_volumes:
                    volume_std = np.std(trade_volumes)
                    volume_mean = np.mean(trade_volumes)
                    metrics['volume_consistency'] = 1.0 - min(1.0, volume_std / (volume_mean + 1e-8))
                    metrics['trade_frequency'] = len(recent_trades)
            
            # CALIBRATED LIQUIDITY SCORING - Less conservative
            # Improved spread scoring (less penalty for wider spreads)
            spread_score = max(0, 1.0 - (metrics['bid_ask_spread'] * 500))  # Reduced penalty

            # Improved depth scoring (lower threshold for acceptable depth)
            depth_score = min(1.0, metrics['order_book_depth'] / 5000)      # Reduced threshold

            # Volume consistency score
            volume_score = metrics['volume_consistency']

            # Trade frequency score (more lenient)
            frequency_score = min(1.0, metrics['trade_frequency'] / 50)     # Reduced threshold

            # Base liquidity score with adjusted weights
            base_score = (spread_score * 0.25 + depth_score * 0.35 +
                         volume_score * 0.2 + frequency_score * 0.2)

            # Apply minimum viable liquidity boost
            if base_score >= 0.05:  # If there's any reasonable liquidity
                base_score = max(base_score, 0.15)  # Boost to minimum viable level

            metrics['liquidity_score'] = min(1.0, base_score)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating liquidity metrics: {e}")
            return {'liquidity_score': 0.3, 'bid_ask_spread': 0.002, 'order_book_depth': 0}
    
    def _calculate_volume_constraints(self, volume_24h: float, recent_trades: List) -> Dict[str, float]:
        """Calculate position size constraints based on volume"""
        try:
            # Calculate average trade size
            if recent_trades:
                trade_sizes = [float(trade.get('amount', 0)) for trade in recent_trades[-50:]]
                avg_trade_size = np.mean(trade_sizes) if trade_sizes else 0
                median_trade_size = np.median(trade_sizes) if trade_sizes else 0
            else:
                avg_trade_size = volume_24h / 1000 if volume_24h > 0 else 0
                median_trade_size = avg_trade_size
            
            # Calculate hourly volume (assuming 24h volume)
            hourly_volume = volume_24h / 24 if volume_24h > 0 else 0
            
            # Set maximum position as percentage of hourly volume
            max_position_by_volume = hourly_volume * self.market_impact_threshold
            
            # Don't exceed 5x the median trade size
            max_position_by_trade_size = median_trade_size * 5
            
            # Take the more conservative limit
            max_position = min(max_position_by_volume, max_position_by_trade_size) if max_position_by_trade_size > 0 else max_position_by_volume
            
            return {
                'max_position': max_position,
                'avg_trade_size': avg_trade_size,
                'median_trade_size': median_trade_size,
                'hourly_volume': hourly_volume,
                'volume_impact_threshold': self.market_impact_threshold
            }
            
        except Exception as e:
            logger.error(f"Error calculating volume constraints: {e}")
            return {'max_position': 0, 'avg_trade_size': 0}
    
    def _calculate_order_book_constraints(self, order_book: Dict, current_price: float) -> Dict[str, float]:
        """Calculate constraints based on order book depth"""
        try:
            if not order_book or 'bids' not in order_book or 'asks' not in order_book:
                return {'max_position': 0, 'depth_score': 0}
            
            bids = order_book['bids']
            asks = order_book['asks']
            
            if not bids or not asks:
                return {'max_position': 0, 'depth_score': 0}
            
            # Calculate available liquidity within 2% of current price
            available_bid_liquidity = 0
            available_ask_liquidity = 0
            
            price_threshold = current_price * 0.02  # 2% price impact threshold
            
            for bid in bids:
                if isinstance(bid, list) and len(bid) >= 2:
                    bid_price = float(bid[0])
                    bid_volume = float(bid[1])
                    
                    if current_price - bid_price <= price_threshold:
                        available_bid_liquidity += bid_volume
            
            for ask in asks:
                if isinstance(ask, list) and len(ask) >= 2:
                    ask_price = float(ask[0])
                    ask_volume = float(ask[1])
                    
                    if ask_price - current_price <= price_threshold:
                        available_ask_liquidity += ask_volume
            
            # Use the smaller of bid/ask liquidity
            available_liquidity = min(available_bid_liquidity, available_ask_liquidity)
            
            # Maximum position should not exceed a percentage of available liquidity
            max_position = available_liquidity * self.max_order_book_impact
            
            # Calculate depth score
            total_depth = available_bid_liquidity + available_ask_liquidity
            depth_score = min(1.0, total_depth / 1000)  # Normalize to 0-1
            
            return {
                'max_position': max_position,
                'available_liquidity': available_liquidity,
                'depth_score': depth_score,
                'bid_liquidity': available_bid_liquidity,
                'ask_liquidity': available_ask_liquidity
            }
            
        except Exception as e:
            logger.error(f"Error calculating order book constraints: {e}")
            return {'max_position': 0, 'depth_score': 0}
    
    def _calculate_confidence_factor(self, confidence: float) -> float:
        """Calculate position scaling factor based on model confidence"""
        try:
            # Scale position size based on confidence
            # High confidence (>0.8): 100% of calculated size
            # Medium confidence (0.6-0.8): 70-100% of calculated size
            # Low confidence (<0.6): 30-70% of calculated size
            
            if confidence >= 0.8:
                return 1.0
            elif confidence >= 0.6:
                return 0.7 + (confidence - 0.6) * 1.5  # Linear scaling from 0.7 to 1.0
            else:
                return 0.3 + (confidence * 0.67)  # Linear scaling from 0.3 to 0.7
                
        except Exception as e:
            logger.error(f"Error calculating confidence factor: {e}")
            return 0.5
    
    def _calculate_final_position_size(self, 
                                     base_size: float,
                                     liquidity_metrics: Dict,
                                     volume_constraints: Dict,
                                     order_book_constraints: Dict,
                                     confidence_factor: float,
                                     account_balance: float) -> Dict[str, float]:
        """Calculate final optimal position size"""
        try:
            # Apply all constraints
            max_by_volume = volume_constraints.get('max_position', float('inf'))
            max_by_order_book = order_book_constraints.get('max_position', float('inf'))
            max_by_liquidity = liquidity_metrics.get('order_book_depth', 0) * 0.1  # 10% of depth
            
            # Take the most conservative constraint
            max_safe_size = min(max_by_volume, max_by_order_book, max_by_liquidity)
            
            # Apply confidence scaling
            confidence_adjusted_size = base_size * confidence_factor
            
            # Apply liquidity buffer
            buffered_max_size = max_safe_size * (1 - self.liquidity_buffer)
            
            # Final position size
            optimal_size = min(confidence_adjusted_size, buffered_max_size)
            
            # Ensure we don't exceed account balance (assuming 1:1 for simplicity)
            max_by_balance = account_balance * 0.1  # Use max 10% of balance per trade
            optimal_size = min(optimal_size, max_by_balance)
            
            # Estimate market impact
            avg_trade_size = volume_constraints.get('avg_trade_size', 1)
            market_impact = (optimal_size / avg_trade_size) * 0.001 if avg_trade_size > 0 else 0.01
            
            return {
                'position_size': max(0, optimal_size),
                'position_usd': max(0, optimal_size * 0.32),  # Approximate USD value
                'max_safe_size': max_safe_size,
                'market_impact_estimate': market_impact,
                'constraints_applied': {
                    'volume_limit': max_by_volume,
                    'order_book_limit': max_by_order_book,
                    'liquidity_limit': max_by_liquidity,
                    'balance_limit': max_by_balance
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating final position size: {e}")
            return {'position_size': 0, 'position_usd': 0, 'market_impact_estimate': 0.1}
    
    def _calculate_execution_strategy(self, optimal_size: Dict, liquidity_metrics: Dict, order_book: Dict) -> Dict[str, Any]:
        """Calculate optimal execution strategy"""
        try:
            position_size = optimal_size.get('position_size', 0)
            liquidity_score = liquidity_metrics.get('liquidity_score', 0.5)
            
            # Determine if we should split the order
            if position_size > 1000 or liquidity_score < 0.4:
                # Split large orders or in low liquidity conditions
                num_splits = min(5, max(2, int(position_size / 500)))
                split_size = position_size / num_splits
                
                return {
                    'type': 'split_order',
                    'splits': num_splits,
                    'split_size': split_size,
                    'execution_interval': 30,  # seconds between splits
                    'order_type': 'limit',
                    'price_improvement': 0.001  # Try to get better prices
                }
            else:
                return {
                    'type': 'single_order',
                    'splits': 1,
                    'order_type': 'market' if liquidity_score > 0.7 else 'limit',
                    'price_improvement': 0.0005 if liquidity_score > 0.7 else 0.002
                }
                
        except Exception as e:
            logger.error(f"Error calculating execution strategy: {e}")
            return {'type': 'single_order', 'splits': 1}
    
    def _generate_recommendations(self, optimal_size: Dict, liquidity_metrics: Dict) -> List[str]:
        """Generate trading recommendations (Unicode-safe)"""
        recommendations = []

        try:
            liquidity_score = liquidity_metrics.get('liquidity_score', 0.5)
            market_impact = optimal_size.get('market_impact_estimate', 0)

            # Use ASCII-safe warnings to avoid encoding issues
            if liquidity_score < 0.3:
                recommendations.append("WARNING: Low liquidity detected - consider reducing position size")

            if market_impact > 0.05:
                recommendations.append("INFO: High market impact expected - consider splitting order")

            if optimal_size.get('position_size', 0) < optimal_size.get('max_safe_size', 0) * 0.5:
                recommendations.append("NOTICE: Position size significantly reduced due to market constraints")

            if liquidity_score > 0.8:
                recommendations.append("SUCCESS: Excellent liquidity - optimal conditions for trading")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["WARNING: Unable to generate recommendations"]
