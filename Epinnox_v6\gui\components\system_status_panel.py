"""
System Status Panel
Displays system status, connection health, and operational metrics
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QFrame, QTextEdit, QPushButton
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor
from datetime import datetime

class SystemStatusPanel(QWidget):
    """Panel displaying system status and operational information"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
        # Timer for updating countdown
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_timer.start(1000)  # Update every second
        
        self.next_analysis_time = None
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # System Status Overview
        status_group = QGroupBox("🖥️ System Status")
        status_layout = QGridLayout(status_group)
        
        # Status indicators
        self.connection_status = QLabel("🔴 Disconnected")
        self.connection_status.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.trading_status = QLabel("🔴 Stopped")
        self.trading_status.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.uptime_label = QLabel("00:00:00")
        self.uptime_label.setFont(QFont("Arial", 11))
        
        status_layout.addWidget(QLabel("Connection:"), 0, 0)
        status_layout.addWidget(self.connection_status, 0, 1)
        status_layout.addWidget(QLabel("Trading:"), 0, 2)
        status_layout.addWidget(self.trading_status, 0, 3)
        
        status_layout.addWidget(QLabel("Uptime:"), 1, 0)
        status_layout.addWidget(self.uptime_label, 1, 1, 1, 3)
        
        layout.addWidget(status_group)
        
        # Analysis Timing
        timing_group = QGroupBox("⏰ Analysis Timing")
        timing_layout = QGridLayout(timing_group)
        
        self.last_analysis_label = QLabel("--")
        self.next_analysis_label = QLabel("--")
        self.countdown_label = QLabel("--")
        self.countdown_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        timing_layout.addWidget(QLabel("Last Analysis:"), 0, 0)
        timing_layout.addWidget(self.last_analysis_label, 0, 1)
        timing_layout.addWidget(QLabel("Next Analysis:"), 1, 0)
        timing_layout.addWidget(self.next_analysis_label, 1, 1)
        timing_layout.addWidget(QLabel("Countdown:"), 2, 0)
        timing_layout.addWidget(self.countdown_label, 2, 1)
        
        layout.addWidget(timing_group)
        
        # System Health
        health_group = QGroupBox("💊 System Health")
        health_layout = QGridLayout(health_group)
        
        # Error and warning counts
        self.errors_label = QLabel("0")
        self.errors_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.errors_label.setStyleSheet("color: #ff4444;")
        
        self.warnings_label = QLabel("0")
        self.warnings_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.warnings_label.setStyleSheet("color: #ffaa00;")
        
        # Health score
        self.health_score_label = QLabel("100%")
        self.health_score_label.setFont(QFont("Arial", 14, QFont.Bold))
        
        self.health_bar = QProgressBar()
        self.health_bar.setRange(0, 100)
        self.health_bar.setValue(100)
        self.health_bar.setTextVisible(True)
        self.health_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
        
        health_layout.addWidget(QLabel("Errors:"), 0, 0)
        health_layout.addWidget(self.errors_label, 0, 1)
        health_layout.addWidget(QLabel("Warnings:"), 0, 2)
        health_layout.addWidget(self.warnings_label, 0, 3)
        
        health_layout.addWidget(QLabel("Health Score:"), 1, 0)
        health_layout.addWidget(self.health_score_label, 1, 1)
        health_layout.addWidget(self.health_bar, 1, 2, 1, 2)
        
        layout.addWidget(health_group)
        
        # Resource Usage
        resources_group = QGroupBox("📊 Resource Usage")
        resources_layout = QGridLayout(resources_group)
        
        # Memory usage
        self.memory_label = QLabel("0 MB")
        self.memory_bar = QProgressBar()
        self.memory_bar.setRange(0, 100)
        self.memory_bar.setValue(0)
        self.memory_bar.setTextVisible(True)
        
        # CPU usage
        self.cpu_label = QLabel("0%")
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setRange(0, 100)
        self.cpu_bar.setValue(0)
        self.cpu_bar.setTextVisible(True)
        
        resources_layout.addWidget(QLabel("Memory:"), 0, 0)
        resources_layout.addWidget(self.memory_label, 0, 1)
        resources_layout.addWidget(self.memory_bar, 0, 2)
        
        resources_layout.addWidget(QLabel("CPU:"), 1, 0)
        resources_layout.addWidget(self.cpu_label, 1, 1)
        resources_layout.addWidget(self.cpu_bar, 1, 2)
        
        layout.addWidget(resources_group)
        
        # System Controls
        controls_group = QGroupBox("🎮 System Controls")
        controls_layout = QHBoxLayout(controls_group)
        
        self.start_button = QPushButton("▶️ Start")
        self.start_button.setStyleSheet("QPushButton { background-color: #00ff88; color: black; font-weight: bold; padding: 8px; }")
        
        self.stop_button = QPushButton("⏹️ Stop")
        self.stop_button.setStyleSheet("QPushButton { background-color: #ff4444; color: white; font-weight: bold; padding: 8px; }")
        
        self.restart_button = QPushButton("🔄 Restart")
        self.restart_button.setStyleSheet("QPushButton { background-color: #ffaa00; color: black; font-weight: bold; padding: 8px; }")
        
        controls_layout.addWidget(self.start_button)
        controls_layout.addWidget(self.stop_button)
        controls_layout.addWidget(self.restart_button)
        
        layout.addWidget(controls_group)
        
        # Recent Events Log
        events_group = QGroupBox("📝 Recent Events")
        events_layout = QVBoxLayout(events_group)
        
        self.events_log = QTextEdit()
        self.events_log.setMaximumHeight(120)
        self.events_log.setReadOnly(True)
        self.events_log.setPlainText("System initialized...\n")
        
        events_layout.addWidget(self.events_log)
        layout.addWidget(events_group)
    
    def update_data(self, data):
        """Update the panel with new system status data"""
        try:
            # Update connection status
            connected = data.get('connected', False)
            if connected:
                self.connection_status.setText("🟢 Connected")
                self.connection_status.setStyleSheet("color: #00ff88; font-weight: bold;")
            else:
                self.connection_status.setText("🔴 Disconnected")
                self.connection_status.setStyleSheet("color: #ff4444; font-weight: bold;")
            
            # Update trading status
            running = data.get('running', False)
            if running:
                self.trading_status.setText("🟢 Running")
                self.trading_status.setStyleSheet("color: #00ff88; font-weight: bold;")
            else:
                self.trading_status.setText("🔴 Stopped")
                self.trading_status.setStyleSheet("color: #ff4444; font-weight: bold;")
            
            # Update uptime
            uptime = data.get('uptime', '00:00:00')
            self.uptime_label.setText(uptime)
            
            # Update analysis timing
            last_analysis = data.get('last_analysis', '--')
            self.last_analysis_label.setText(last_analysis)
            
            next_analysis = data.get('next_analysis', '--')
            self.next_analysis_label.setText(next_analysis)
            
            # Update health metrics
            errors = data.get('errors', 0)
            warnings = data.get('warnings', 0)
            
            self.errors_label.setText(str(errors))
            self.warnings_label.setText(str(warnings))
            
            # Calculate health score
            health_score = max(0, 100 - (errors * 10) - (warnings * 2))
            self.health_score_label.setText(f"{health_score}%")
            self.health_bar.setValue(health_score)
            
            # Color code health bar
            if health_score >= 80:
                self.health_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
                self.health_score_label.setStyleSheet("color: #00ff88; font-weight: bold;")
            elif health_score >= 60:
                self.health_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
                self.health_score_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
            else:
                self.health_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
                self.health_score_label.setStyleSheet("color: #ff4444; font-weight: bold;")
            
            # Update resource usage (if available)
            memory_usage = data.get('memory_usage', 0.0)
            cpu_usage = data.get('cpu_usage', 0.0)
            
            self.memory_label.setText(f"{memory_usage:.1f} MB")
            self.memory_bar.setValue(int(min(memory_usage / 10, 100)))  # Scale for display
            
            self.cpu_label.setText(f"{cpu_usage:.1f}%")
            self.cpu_bar.setValue(int(cpu_usage))
            
            # Color code resource bars
            if memory_usage > 500:
                self.memory_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            elif memory_usage > 300:
                self.memory_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.memory_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            
            if cpu_usage > 80:
                self.cpu_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            elif cpu_usage > 60:
                self.cpu_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.cpu_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            
        except Exception as e:
            print(f"Error updating system status panel: {e}")
    
    def update_countdown(self):
        """Update the countdown to next analysis"""
        try:
            if self.next_analysis_time:
                now = datetime.now()
                if self.next_analysis_time > now:
                    remaining = self.next_analysis_time - now
                    seconds = int(remaining.total_seconds())
                    
                    if seconds > 0:
                        minutes, seconds = divmod(seconds, 60)
                        self.countdown_label.setText(f"{minutes:02d}:{seconds:02d}")
                        self.countdown_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
                    else:
                        self.countdown_label.setText("Analyzing...")
                        self.countdown_label.setStyleSheet("color: #00ff88; font-weight: bold;")
                else:
                    self.countdown_label.setText("--")
                    self.countdown_label.setStyleSheet("color: #ffffff; font-weight: bold;")
            else:
                self.countdown_label.setText("--")
                
        except Exception as e:
            print(f"Error updating countdown: {e}")
    
    def add_event(self, event_text):
        """Add an event to the events log"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            event_line = f"[{timestamp}] {event_text}\n"
            
            current_text = self.events_log.toPlainText()
            lines = current_text.split('\n')
            
            # Keep only last 10 lines
            if len(lines) > 10:
                lines = lines[-10:]
            
            lines.append(event_line.strip())
            self.events_log.setPlainText('\n'.join(lines))
            
            # Scroll to bottom
            self.events_log.moveCursor(self.events_log.textCursor().End)
            
        except Exception as e:
            print(f"Error adding event: {e}")
    
    def set_next_analysis_time(self, next_time):
        """Set the next analysis time for countdown"""
        self.next_analysis_time = next_time
