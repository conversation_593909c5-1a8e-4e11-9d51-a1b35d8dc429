#!/usr/bin/env python3
"""
Epinnox v6 Optimized Trading System Launcher

This script launches the optimized trading system with all enhancements:
- Intelligent Signal Hierarchy
- Enhanced ML Models (SVM, Random Forest, LSTM ready)
- Smart Position Sizing
- Improved Confidence Calculation
- Multi-timeframe Analysis
- Market Regime Detection
- Unicode-safe Logging
"""

import sys
import os
import argparse
from datetime import datetime

def print_banner():
    """Print the optimized system banner"""
    print("=" * 70)
    print("🚀 EPINNOX v6 OPTIMIZED TRADING SYSTEM")
    print("=" * 70)
    print("✅ Intelligent Signal Hierarchy")
    print("✅ Enhanced ML Models (SVM, Random Forest)")
    print("✅ Smart Position Sizing")
    print("✅ Improved Confidence Calculation")
    print("✅ Multi-timeframe Analysis")
    print("✅ Market Regime Detection")
    print("✅ Unicode-safe Logging")
    print("=" * 70)
    print()

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    compatibility_issues = []

    # Check core dependencies with error handling
    try:
        import ccxt
    except ImportError:
        missing_deps.append("ccxt")
    except Exception as e:
        compatibility_issues.append(f"ccxt: {e}")

    try:
        import numpy
        print(f"✅ NumPy version: {numpy.__version__}")
    except ImportError:
        missing_deps.append("numpy")
    except Exception as e:
        compatibility_issues.append(f"numpy: {e}")

    try:
        import pandas
        print(f"✅ Pandas version: {pandas.__version__}")
    except ImportError:
        missing_deps.append("pandas")
    except Exception as e:
        compatibility_issues.append(f"pandas: {e}")
        print("⚠️  Pandas compatibility issue detected. Trying to continue...")

    try:
        import sklearn
        print(f"✅ Scikit-learn version: {sklearn.__version__}")
    except ImportError:
        missing_deps.append("scikit-learn")
    except Exception as e:
        compatibility_issues.append(f"scikit-learn: {e}")

    # Optional dependencies
    optional_deps = []

    try:
        import tensorflow
        print(f"✅ TensorFlow version: {tensorflow.__version__}")
    except ImportError:
        optional_deps.append("tensorflow (for LSTM models)")
    except Exception as e:
        optional_deps.append(f"tensorflow (error: {e})")

    try:
        import talib
        print(f"✅ TA-Lib available")
    except ImportError:
        optional_deps.append("TA-Lib (using numpy fallback)")
    except Exception as e:
        optional_deps.append(f"TA-Lib (error: {e})")

    try:
        import PyQt5
        print(f"✅ PyQt5 available")
    except ImportError:
        optional_deps.append("PyQt5 (for GUI dashboard)")
    except Exception as e:
        optional_deps.append(f"PyQt5 (error: {e})")

    if missing_deps:
        print("\n❌ MISSING REQUIRED DEPENDENCIES:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nInstall with: pip install " + " ".join(missing_deps))
        return False

    if compatibility_issues:
        print("\n⚠️  COMPATIBILITY ISSUES DETECTED:")
        for issue in compatibility_issues:
            print(f"   - {issue}")
        print("\nNote: System will attempt to continue despite compatibility warnings.")

    if optional_deps:
        print("\n⚠️  OPTIONAL DEPENDENCIES:")
        for dep in optional_deps:
            print(f"   - {dep}")

    print("\n✅ Core dependencies check completed!")
    print()
    return True

def main():
    """Main launcher function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Parse arguments
    parser = argparse.ArgumentParser(
        description='Launch Epinnox v6 Optimized Trading System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_optimized.py --symbol DOGE/USDT
  python run_optimized.py --symbol BTC/USDT --continuous --delay 60
  python run_optimized.py --symbol ETH/USDT --live
  python run_optimized.py --test-optimizations
        """
    )
    
    parser.add_argument('--symbol', type=str, default='DOGE/USDT', 
                       help='Trading symbol (default: DOGE/USDT)')
    parser.add_argument('--continuous', action='store_true', 
                       help='Run continuously')
    parser.add_argument('--delay', type=int, default=30, 
                       help='Delay between runs in seconds (default: 30)')
    parser.add_argument('--live', action='store_true', 
                       help='Use live trades data instead of historical OHLCV')
    parser.add_argument('--test-optimizations', action='store_true',
                       help='Run optimization tests')
    parser.add_argument('--conservative-sizing', action='store_true',
                       help='Use conservative position sizing mode')
    
    args = parser.parse_args()
    
    # Show configuration
    print(f"📊 CONFIGURATION:")
    print(f"   Symbol: {args.symbol}")
    print(f"   Mode: {'Continuous' if args.continuous else 'Single run'}")
    if args.continuous:
        print(f"   Delay: {args.delay} seconds")
    print(f"   Data: {'Live trades' if args.live else 'Historical OHLCV'}")
    print(f"   Position Sizing: {'Conservative' if args.conservative_sizing else 'Moderate'}")
    print()
    
    if args.test_optimizations:
        print("🧪 Running optimization tests...")
        try:
            import test_optimizations
            test_optimizations.test_optimizations()
            test_optimizations.test_signal_hierarchy_standalone()
            test_optimizations.test_position_sizing_standalone()
        except ImportError:
            print("❌ test_optimizations.py not found")
            sys.exit(1)
        return
    
    # Import and run main system
    try:
        print(f"🚀 Starting optimized trading system at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Set environment variable for position sizing mode
        if args.conservative_sizing:
            os.environ['EPINNOX_CONSERVATIVE_SIZING'] = '1'
        
        # Import main system
        from main import main as run_main
        
        # Override sys.argv to pass arguments to main
        original_argv = sys.argv.copy()
        sys.argv = ['main.py', '--symbol', args.symbol]
        
        if args.continuous:
            sys.argv.extend(['--continuous', '--delay', str(args.delay)])
        
        if args.live:
            sys.argv.append('--live')
        
        # Run the main system
        run_main()
        
        # Restore original argv
        sys.argv = original_argv
        
    except KeyboardInterrupt:
        print("\n🛑 Trading system stopped by user")
    except Exception as e:
        print(f"\n❌ Error running trading system: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
