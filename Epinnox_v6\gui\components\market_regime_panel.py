"""
Market Regime Panel
Displays market regime detection and regime-specific adjustments
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QFrame, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class MarketRegimePanel(QWidget):
    """Panel displaying market regime detection and adjustments"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Current Market Regime
        regime_group = QGroupBox("🌊 Current Market Regime")
        regime_layout = QGridLayout(regime_group)
        
        # Regime display
        self.current_regime_label = QLabel("UNKNOWN")
        self.current_regime_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.current_regime_label.setAlignment(Qt.AlignCenter)
        
        self.regime_description = QLabel("Analyzing market conditions...")
        self.regime_description.setWordWrap(True)
        self.regime_description.setAlignment(Qt.AlignCenter)
        
        regime_layout.addWidget(QLabel("Market Regime:"), 0, 0)
        regime_layout.addWidget(self.current_regime_label, 0, 1, 1, 2)
        regime_layout.addWidget(self.regime_description, 1, 0, 1, 3)
        
        layout.addWidget(regime_group)
        
        # Market Metrics
        metrics_group = QGroupBox("📊 Market Metrics")
        metrics_layout = QGridLayout(metrics_group)
        
        # Volatility
        self.volatility_label = QLabel("0.00%")
        self.volatility_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.volatility_bar = QProgressBar()
        self.volatility_bar.setRange(0, 100)
        self.volatility_bar.setValue(0)
        self.volatility_bar.setTextVisible(True)
        
        # Trend Strength
        self.trend_strength_label = QLabel("0.00")
        self.trend_strength_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.trend_strength_bar = QProgressBar()
        self.trend_strength_bar.setRange(0, 100)
        self.trend_strength_bar.setValue(0)
        self.trend_strength_bar.setTextVisible(True)
        
        metrics_layout.addWidget(QLabel("Volatility:"), 0, 0)
        metrics_layout.addWidget(self.volatility_label, 0, 1)
        metrics_layout.addWidget(self.volatility_bar, 0, 2)
        
        metrics_layout.addWidget(QLabel("Trend Strength:"), 1, 0)
        metrics_layout.addWidget(self.trend_strength_label, 1, 1)
        metrics_layout.addWidget(self.trend_strength_bar, 1, 2)
        
        layout.addWidget(metrics_group)
        
        # Regime Adjustments
        adjustments_group = QGroupBox("⚙️ Regime-Specific Adjustments")
        adjustments_layout = QVBoxLayout(adjustments_group)
        
        # Adjustments table
        self.adjustments_table = QTableWidget(5, 3)
        self.adjustments_table.setHorizontalHeaderLabels([
            "Parameter", "Factor", "Effect"
        ])
        self.adjustments_table.horizontalHeader().setStretchLastSection(True)
        self.adjustments_table.setMaximumHeight(180)
        
        # Initialize adjustment rows
        adjustments = [
            'Leverage Factor',
            'Position Size Factor', 
            'Stop Loss Factor',
            'Take Profit Factor',
            'Entry Confidence'
        ]
        
        for i, adjustment in enumerate(adjustments):
            self.adjustments_table.setItem(i, 0, QTableWidgetItem(adjustment))
            self.adjustments_table.setItem(i, 1, QTableWidgetItem("1.00"))
            self.adjustments_table.setItem(i, 2, QTableWidgetItem("Neutral"))
        
        adjustments_layout.addWidget(self.adjustments_table)
        layout.addWidget(adjustments_group)
        
        # Regime History and Transitions
        history_group = QGroupBox("📈 Regime Analysis")
        history_layout = QGridLayout(history_group)
        
        self.regime_duration = QLabel("Duration: --")
        self.regime_stability = QLabel("Stability: --")
        self.last_transition = QLabel("Last Change: --")
        self.transition_frequency = QLabel("Transitions: --")
        
        history_layout.addWidget(self.regime_duration, 0, 0)
        history_layout.addWidget(self.regime_stability, 0, 1)
        history_layout.addWidget(self.last_transition, 1, 0)
        history_layout.addWidget(self.transition_frequency, 1, 1)
        
        layout.addWidget(history_group)
    
    def update_data(self, data):
        """Update the panel with new market regime data"""
        try:
            # Update current regime
            current_regime = data.get('current_regime', 'unknown').upper()
            self.current_regime_label.setText(current_regime)
            
            # Set regime description and color
            regime_info = self.get_regime_info(current_regime)
            self.regime_description.setText(regime_info['description'])
            self.current_regime_label.setStyleSheet(f"color: {regime_info['color']}; background-color: {regime_info['bg_color']}; padding: 8px; border-radius: 5px;")
            
            # Update market metrics
            volatility = data.get('volatility', 0.0) * 100
            trend_strength = data.get('trend_strength', 0.0) * 100
            
            self.volatility_label.setText(f"{volatility:.2f}%")
            self.volatility_bar.setValue(int(min(volatility * 10, 100)))  # Scale for display
            
            self.trend_strength_label.setText(f"{trend_strength:.1f}")
            self.trend_strength_bar.setValue(int(trend_strength))
            
            # Color code volatility
            if volatility > 5:
                self.volatility_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            elif volatility > 2:
                self.volatility_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.volatility_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            
            # Color code trend strength
            if trend_strength > 70:
                self.trend_strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            elif trend_strength > 40:
                self.trend_strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.trend_strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            
            # Update regime adjustments
            adjustments = data.get('adjustments', {})
            self.update_adjustments_table(adjustments)
            
            # Update regime analysis
            self.update_regime_analysis(data)
            
        except Exception as e:
            print(f"Error updating market regime panel: {e}")
    
    def get_regime_info(self, regime):
        """Get regime information including description and colors"""
        regime_data = {
            'LOW_VOLATILITY': {
                'description': 'Market is in a low volatility state with stable price movements. Suitable for trend-following strategies.',
                'color': '#00ff88',
                'bg_color': 'rgba(0, 255, 136, 20)'
            },
            'HIGH_VOLATILITY': {
                'description': 'Market is experiencing high volatility with rapid price movements. Increased risk management required.',
                'color': '#ff4444',
                'bg_color': 'rgba(255, 68, 68, 20)'
            },
            'TRENDING': {
                'description': 'Market is in a strong trending phase. Momentum strategies may be effective.',
                'color': '#2a82da',
                'bg_color': 'rgba(42, 130, 218, 20)'
            },
            'RANGING': {
                'description': 'Market is moving sideways within a defined range. Mean reversion strategies may work well.',
                'color': '#ffaa00',
                'bg_color': 'rgba(255, 170, 0, 20)'
            },
            'UNKNOWN': {
                'description': 'Market regime is being analyzed. Please wait for regime detection.',
                'color': '#888888',
                'bg_color': 'rgba(136, 136, 136, 20)'
            }
        }
        
        return regime_data.get(regime, regime_data['UNKNOWN'])
    
    def update_adjustments_table(self, adjustments):
        """Update the regime adjustments table"""
        try:
            adjustment_keys = [
                'leverage_factor',
                'position_size_factor',
                'stop_loss_factor', 
                'take_profit_factor',
                'entry_confidence'
            ]
            
            for i, key in enumerate(adjustment_keys):
                if i < self.adjustments_table.rowCount():
                    factor = adjustments.get(key, 1.0)
                    
                    # Update factor
                    factor_item = QTableWidgetItem(f"{factor:.2f}")
                    
                    # Determine effect
                    if factor > 1.1:
                        effect = "Increased"
                        color = QColor(0, 255, 136, 50)
                    elif factor < 0.9:
                        effect = "Decreased"
                        color = QColor(255, 68, 68, 50)
                    else:
                        effect = "Neutral"
                        color = QColor(255, 170, 0, 30)
                    
                    factor_item.setBackground(color)
                    effect_item = QTableWidgetItem(effect)
                    effect_item.setBackground(color)
                    
                    self.adjustments_table.setItem(i, 1, factor_item)
                    self.adjustments_table.setItem(i, 2, effect_item)
                    
        except Exception as e:
            print(f"Error updating adjustments table: {e}")
    
    def update_regime_analysis(self, data):
        """Update regime analysis information"""
        try:
            # This would be enhanced with actual regime tracking
            # For now, we'll show basic information
            
            current_regime = data.get('current_regime', 'unknown')
            
            # Simulate regime duration (would be tracked in real implementation)
            self.regime_duration.setText("Duration: 15 min")
            
            # Regime stability based on volatility
            volatility = data.get('volatility', 0.0)
            if volatility < 0.02:
                stability = "High"
                stability_color = "#00ff88"
            elif volatility < 0.05:
                stability = "Medium"
                stability_color = "#ffaa00"
            else:
                stability = "Low"
                stability_color = "#ff4444"
            
            self.regime_stability.setText(f"Stability: {stability}")
            self.regime_stability.setStyleSheet(f"color: {stability_color};")
            
            # Last transition (would be tracked in real implementation)
            self.last_transition.setText("Last Change: 2h ago")
            
            # Transition frequency (would be calculated from history)
            self.transition_frequency.setText("Transitions: 3/day")
            
        except Exception as e:
            print(f"Error updating regime analysis: {e}")
