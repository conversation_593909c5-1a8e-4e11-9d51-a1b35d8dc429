#!/usr/bin/env python3
"""
Launch the Epinnox Trading System GUI
"""
import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """Main function to launch the GUI"""
    try:
        # FIX: DPI scaling for high-resolution displays
        import ctypes
        from PyQt5.QtCore import Qt
        from PyQt5.QtWidgets import QApplication

        # DPI awareness (for Windows)
        try:
            ctypes.windll.shcore.SetProcessDpiAwareness(1)
        except Exception:
            pass

        # Enable high DPI scaling
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # Import and run the GUI
        from gui.main_window import main as gui_main
        
        print("🚀 Launching Epinnox Trading System GUI...")
        print("📊 Comprehensive trading analysis dashboard")
        print("🤖 Real-time AI analysis with Phi-3.1-Mini model")
        print("📈 Multi-timeframe market analysis")
        print("⚖️ Advanced risk management")
        print("🔄 Live data from HTX exchange")
        print()
        
        # Launch the GUI
        sys.exit(gui_main())
        
    except ImportError as e:
        print(f"❌ Error importing GUI components: {e}")
        print("Make sure PyQt5 is installed: pip install PyQt5")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
