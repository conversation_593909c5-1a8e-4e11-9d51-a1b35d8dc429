"""
Market Data Panel
Displays real-time market data including OHLCV, order book, and recent trades
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QTableWidget, QTableWidgetItem, QHeaderView, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class MarketDataPanel(QWidget):
    """Panel displaying comprehensive market data"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Price Information Section
        price_group = QGroupBox("💰 Price Information")
        price_layout = QGridLayout(price_group)
        
        # Price labels
        self.symbol_label = QLabel("--")
        self.price_label = QLabel("$0.00")
        self.change_label = QLabel("0.00%")
        self.volume_label = QLabel("0")
        self.high_label = QLabel("$0.00")
        self.low_label = QLabel("$0.00")
        
        # Style price labels
        self.price_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.change_label.setFont(QFont("Arial", 12, QFont.Bold))
        
        # Add to grid
        price_layout.addWidget(QLabel("Symbol:"), 0, 0)
        price_layout.addWidget(self.symbol_label, 0, 1)
        price_layout.addWidget(QLabel("Price:"), 0, 2)
        price_layout.addWidget(self.price_label, 0, 3)
        
        price_layout.addWidget(QLabel("24h Change:"), 1, 0)
        price_layout.addWidget(self.change_label, 1, 1)
        price_layout.addWidget(QLabel("Volume:"), 1, 2)
        price_layout.addWidget(self.volume_label, 1, 3)
        
        price_layout.addWidget(QLabel("24h High:"), 2, 0)
        price_layout.addWidget(self.high_label, 2, 1)
        price_layout.addWidget(QLabel("24h Low:"), 2, 2)
        price_layout.addWidget(self.low_label, 2, 3)
        
        layout.addWidget(price_group)
        
        # Order Book Section
        orderbook_group = QGroupBox("📊 Order Book")
        orderbook_layout = QHBoxLayout(orderbook_group)
        
        # Bids table
        bids_layout = QVBoxLayout()
        bids_layout.addWidget(QLabel("Bids"))
        self.bids_table = QTableWidget(5, 2)
        self.bids_table.setHorizontalHeaderLabels(["Price", "Size"])
        self.bids_table.horizontalHeader().setStretchLastSection(True)
        self.bids_table.setMaximumHeight(150)
        bids_layout.addWidget(self.bids_table)
        
        # Asks table
        asks_layout = QVBoxLayout()
        asks_layout.addWidget(QLabel("Asks"))
        self.asks_table = QTableWidget(5, 2)
        self.asks_table.setHorizontalHeaderLabels(["Price", "Size"])
        self.asks_table.horizontalHeader().setStretchLastSection(True)
        self.asks_table.setMaximumHeight(150)
        asks_layout.addWidget(self.asks_table)
        
        orderbook_layout.addLayout(bids_layout)
        orderbook_layout.addLayout(asks_layout)
        
        # Order book metrics
        metrics_layout = QVBoxLayout()
        metrics_layout.addWidget(QLabel("Metrics"))
        
        self.spread_label = QLabel("Spread: 0.00%")
        self.imbalance_label = QLabel("Imbalance: 0.00%")
        
        metrics_layout.addWidget(self.spread_label)
        metrics_layout.addWidget(self.imbalance_label)
        metrics_layout.addStretch()
        
        orderbook_layout.addLayout(metrics_layout)
        
        layout.addWidget(orderbook_group)
        
        # Recent Trades Section
        trades_group = QGroupBox("📈 Recent Trades")
        trades_layout = QVBoxLayout(trades_group)
        
        self.trades_table = QTableWidget(10, 4)
        self.trades_table.setHorizontalHeaderLabels(["Time", "Price", "Size", "Side"])
        self.trades_table.horizontalHeader().setStretchLastSection(True)
        self.trades_table.setMaximumHeight(200)
        
        trades_layout.addWidget(self.trades_table)
        layout.addWidget(trades_group)
        
        # Data freshness indicator
        freshness_frame = QFrame()
        freshness_layout = QHBoxLayout(freshness_frame)
        freshness_layout.addWidget(QLabel("Data Freshness:"))
        self.freshness_label = QLabel("🔴 No Data")
        freshness_layout.addWidget(self.freshness_label)
        freshness_layout.addStretch()
        
        layout.addWidget(freshness_frame)

        # Apply initial Matrix theme to tables
        self.apply_matrix_theme_to_tables()

    def apply_matrix_theme_to_tables(self):
        """Apply Matrix theme to all tables"""
        matrix_table_style = """
            QTableWidget {
                background-color: #000000;
                color: #00FF00;
                border: 1px solid #00AA00;
                gridline-color: #003300;
                font-family: 'Courier New', monospace;
                font-size: 9px;
                selection-background-color: #004400;
            }
            QTableWidget::item {
                background-color: #000000;
                color: #00FF00;
                border: none;
                padding: 2px;
            }
            QTableWidget::item:selected {
                background-color: #004400;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 4px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
                font-size: 9px;
            }
        """

        self.bids_table.setStyleSheet(matrix_table_style)
        self.asks_table.setStyleSheet(matrix_table_style)
        self.trades_table.setStyleSheet(matrix_table_style)

    def apply_theme(self, theme_name):
        """Apply theme to the panel"""
        if theme_name == "matrix":
            self.apply_matrix_theme_to_tables()
            # Update all labels to Matrix colors
            matrix_label_style = "color: #00FF00; font-family: 'Courier New', monospace;"
            self.symbol_label.setStyleSheet(matrix_label_style)
            self.price_label.setStyleSheet(matrix_label_style)
            self.volume_label.setStyleSheet(matrix_label_style)
            self.high_label.setStyleSheet(matrix_label_style)
            self.low_label.setStyleSheet(matrix_label_style)
            self.spread_label.setStyleSheet(matrix_label_style)

    def update_data(self, data):
        """Update the panel with new market data"""
        try:
            # Update price information
            self.symbol_label.setText(data.get('symbol', '--'))
            
            price = data.get('price', 0.0)
            self.price_label.setText(f"${price:.6f}")
            
            change_percent = data.get('change_percent', 0.0)
            self.change_label.setText(f"{change_percent:+.2f}%")
            
            # Color code the change
            if change_percent > 0:
                self.change_label.setStyleSheet("color: #00ff88;")
            elif change_percent < 0:
                self.change_label.setStyleSheet("color: #ff4444;")
            else:
                self.change_label.setStyleSheet("color: #ffffff;")
            
            volume = data.get('volume', 0.0)
            self.volume_label.setText(f"{volume:,.0f}")
            
            self.high_label.setText(f"${data.get('high_24h', 0.0):.6f}")
            self.low_label.setText(f"${data.get('low_24h', 0.0):.6f}")
            
            # Update order book
            order_book = data.get('order_book', {})
            self.update_order_book(order_book)
            
            # Update recent trades
            trades = data.get('recent_trades', [])
            self.update_trades(trades)
            
            # Update freshness indicator
            self.freshness_label.setText("🟢 Live")
            self.freshness_label.setStyleSheet("color: #00ff88;")
            
        except Exception as e:
            print(f"Error updating market data panel: {e}")
    
    def update_order_book(self, order_book):
        """Update the order book tables"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            # Update bids table
            for i in range(min(5, len(bids))):
                if i < len(bids):
                    price_item = QTableWidgetItem(f"{bids[i][0]:.6f}")
                    size_item = QTableWidgetItem(f"{bids[i][1]:.2f}")
                    # Matrix theme colors
                    price_item.setBackground(QColor(0, 170, 0, 40))  # Matrix green
                    size_item.setBackground(QColor(0, 170, 0, 40))
                    price_item.setForeground(QColor(0, 255, 0))  # Bright green text
                    size_item.setForeground(QColor(0, 255, 0))
                    self.bids_table.setItem(i, 0, price_item)
                    self.bids_table.setItem(i, 1, size_item)
            
            # Update asks table
            for i in range(min(5, len(asks))):
                if i < len(asks):
                    price_item = QTableWidgetItem(f"{asks[i][0]:.6f}")
                    size_item = QTableWidgetItem(f"{asks[i][1]:.2f}")
                    # Matrix theme colors
                    price_item.setBackground(QColor(170, 0, 0, 40))  # Matrix red
                    size_item.setBackground(QColor(170, 0, 0, 40))
                    price_item.setForeground(QColor(255, 0, 0))  # Bright red text
                    size_item.setForeground(QColor(255, 0, 0))
                    self.asks_table.setItem(i, 0, price_item)
                    self.asks_table.setItem(i, 1, size_item)
            
            # Update metrics
            spread = order_book.get('spread', 0.0)
            imbalance = order_book.get('imbalance', 0.0)
            
            self.spread_label.setText(f"Spread: {spread:.4f}%")
            self.imbalance_label.setText(f"Imbalance: {imbalance:+.2f}%")
            
            # Color code imbalance
            if imbalance > 0:
                self.imbalance_label.setStyleSheet("color: #00ff88;")
            elif imbalance < 0:
                self.imbalance_label.setStyleSheet("color: #ff4444;")
            else:
                self.imbalance_label.setStyleSheet("color: #ffffff;")
                
        except Exception as e:
            print(f"Error updating order book: {e}")
    
    def update_trades(self, trades):
        """Update the recent trades table"""
        try:
            self.trades_table.setRowCount(len(trades))
            
            for i, trade in enumerate(trades):
                # Assuming trade format: [timestamp, price, size, side]
                if len(trade) >= 4:
                    time_item = QTableWidgetItem(str(trade[0]))
                    price_item = QTableWidgetItem(f"{trade[1]:.6f}")
                    size_item = QTableWidgetItem(f"{trade[2]:.2f}")
                    side_item = QTableWidgetItem(str(trade[3]))
                    
                    # Color code by side with Matrix colors
                    if trade[3].lower() == 'buy':
                        side_item.setBackground(QColor(0, 170, 0, 60))  # Matrix green
                        side_item.setForeground(QColor(0, 255, 0))
                    else:
                        side_item.setBackground(QColor(170, 0, 0, 60))  # Matrix red
                        side_item.setForeground(QColor(255, 0, 0))

                    # Set Matrix colors for other items too
                    time_item.setForeground(QColor(0, 255, 0))
                    price_item.setForeground(QColor(0, 255, 0))
                    size_item.setForeground(QColor(0, 255, 0))
                    
                    self.trades_table.setItem(i, 0, time_item)
                    self.trades_table.setItem(i, 1, price_item)
                    self.trades_table.setItem(i, 2, size_item)
                    self.trades_table.setItem(i, 3, side_item)
                    
        except Exception as e:
            print(f"Error updating trades: {e}")
