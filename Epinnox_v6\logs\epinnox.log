2025-06-15 08:49:54,185 - gui.data_manager - INFO - GUI Data Manager initialized
2025-06-15 08:49:55,029 - app.main_app - ERROR - Fatal error: 'QTabWidget' object has no attribute 'addWidget'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app\main_app.py", line 37, in main
    window = TradingSystemGUI()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\main_window.py", line 36, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\main_window.py", line 79, in init_ui
    self.content_stack.addWidget(self.dashboard)
AttributeError: 'QTabWidget' object has no attribute 'addWidget'
2025-06-15 08:50:18,351 - gui.data_manager - INFO - GUI Data Manager initialized
2025-06-15 08:50:18,946 - gui.main_window - INFO - Modern Trading System GUI initialized
2025-06-15 08:50:40,952 - gui.data_manager - INFO - GUI Data Manager initialized
2025-06-15 08:50:41,501 - gui.main_window - INFO - Modern Trading System GUI initialized
2025-06-15 08:54:03,707 - gui.data_manager - WARNING - Error reading data files: Expecting value: line 1 column 1 (char 0)
2025-06-15 09:13:50,706 - gui.data_manager - WARNING - Error reading data files: Expecting value: line 1 column 1 (char 0)
2025-06-15 09:26:21,700 - gui.data_manager - WARNING - Error reading data files: Expecting value: line 1 column 1 (char 0)
2025-06-15 09:57:38,384 - gui.main_window - INFO - Trading System GUI closing
