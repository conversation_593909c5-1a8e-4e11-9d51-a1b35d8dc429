"""
Reorganized UI Panels for Epinnox v6
Following the new UX strategy with role-based organization
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg
from datetime import datetime


class CompactMarketInfoPanel(QWidget):
    """Mini-panel below chart showing current price, spread, volume, etc."""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(20)
        
        # Current Price
        self.price_label = QLabel("Price: $0.3245")
        self.price_label.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 14px;")
        layout.addWidget(self.price_label)
        
        # Spread
        self.spread_label = QLabel("Spread: 0.0001")
        self.spread_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New';")
        layout.addWidget(self.spread_label)
        
        # 24h Range
        self.range_label = QLabel("24h: $0.31-$0.34")
        self.range_label.setStyleSheet("color: #00FFFF; font-family: 'Courier New';")
        layout.addWidget(self.range_label)
        
        # Volume
        self.volume_label = QLabel("Vol: 1.2M")
        self.volume_label.setStyleSheet("color: #FF00FF; font-family: 'Courier New';")
        layout.addWidget(self.volume_label)
        
        # Order Book Pressure (visual bar)
        pressure_frame = QFrame()
        pressure_layout = QVBoxLayout(pressure_frame)
        pressure_layout.setContentsMargins(0, 0, 0, 0)
        
        pressure_label = QLabel("OB Pressure:")
        pressure_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        pressure_layout.addWidget(pressure_label)
        
        self.pressure_bar = QProgressBar()
        self.pressure_bar.setRange(-100, 100)
        self.pressure_bar.setValue(25)  # Example: 25% bullish
        self.pressure_bar.setMaximumHeight(15)
        self.pressure_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 3px;
                background-color: #001100;
                text-align: center;
                color: #00FF00;
                font-family: 'Courier New';
                font-size: 9px;
            }
            QProgressBar::chunk {
                background-color: #00AA00;
                border-radius: 2px;
            }
        """)
        pressure_layout.addWidget(self.pressure_bar)
        
        layout.addWidget(pressure_frame)
        layout.addStretch()

    def update_data(self, data):
        """Update market info with new data"""
        if data and 'current_price' in data:
            self.price_label.setText(f"Price: ${data['current_price']:.4f}")
        if data and 'spread' in data:
            self.spread_label.setText(f"Spread: {data['spread']:.4f}")
        if data and 'volume_24h' in data:
            self.volume_label.setText(f"Vol: {data['volume_24h']}")
        if data and 'orderbook_pressure' in data:
            self.pressure_bar.setValue(int(data['orderbook_pressure'] * 100))


class TrendSystemPanel(QWidget):
    """Compact trend analysis with heatmap-style display"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Section title
        title = QLabel("✳️ TREND SYSTEM")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Overall Trend Summary
        summary_frame = QFrame()
        summary_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 5px; background-color: #001100;")
        summary_layout = QVBoxLayout(summary_frame)
        
        self.trend_direction = QLabel("STRONG BEARISH")
        self.trend_direction.setStyleSheet("color: #FF0000; font-weight: bold; font-family: 'Courier New'; font-size: 14px;")
        self.trend_direction.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(self.trend_direction)
        
        self.trend_strength = QLabel("Strength: -0.87")
        self.trend_strength.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 11px;")
        self.trend_strength.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(self.trend_strength)
        
        layout.addWidget(summary_frame)
        
        # Timeframe Heatmap
        heatmap_label = QLabel("Timeframe Analysis:")
        heatmap_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px; margin-top: 10px;")
        layout.addWidget(heatmap_label)
        
        heatmap_frame = QFrame()
        heatmap_layout = QGridLayout(heatmap_frame)
        heatmap_layout.setSpacing(2)
        
        timeframes = ["1m", "5m", "15m"]
        trends = ["bearish", "strong_bearish", "strong_bearish"]
        colors = ["#FF6600", "#FF0000", "#FF0000"]
        
        for i, (tf, trend, color) in enumerate(zip(timeframes, trends, colors)):
            tf_label = QLabel(tf)
            tf_label.setStyleSheet(f"background-color: {color}; color: #FFFFFF; padding: 3px; border-radius: 3px; font-family: 'Courier New'; font-size: 9px; font-weight: bold;")
            tf_label.setAlignment(Qt.AlignCenter)
            tf_label.setMinimumWidth(40)
            heatmap_layout.addWidget(tf_label, 0, i)
        
        layout.addWidget(heatmap_frame)
        
        # Momentum Status
        momentum_frame = QFrame()
        momentum_layout = QHBoxLayout(momentum_frame)
        momentum_layout.setContentsMargins(0, 5, 0, 0)
        
        momentum_label = QLabel("Momentum:")
        momentum_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        momentum_layout.addWidget(momentum_label)
        
        self.momentum_indicator = QLabel("⬇️ DECLINING")
        self.momentum_indicator.setStyleSheet("color: #FF0000; font-weight: bold; font-family: 'Courier New'; font-size: 10px;")
        momentum_layout.addWidget(self.momentum_indicator)
        
        layout.addWidget(momentum_frame)
        layout.addStretch()

    def update_data(self, data):
        """Update trend system with new data"""
        if data and 'trend_direction' in data:
            self.trend_direction.setText(data['trend_direction'].upper())
        if data and 'trend_strength' in data:
            self.trend_strength.setText(f"Strength: {data['trend_strength']:.2f}")
        if data and 'momentum' in data:
            momentum_text = "⬆️ RISING" if data['momentum'] > 0 else "⬇️ DECLINING"
            self.momentum_indicator.setText(momentum_text)


class SignalScoringCompactPanel(QWidget):
    """Compact signal scoring with top 3 signals"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Section title
        title = QLabel("✅ SIGNAL SCORING")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Top 3 Signals
        signals_frame = QFrame()
        signals_layout = QVBoxLayout(signals_frame)
        
        signals = [
            ("MACD", 0.043, "WAIT", "#FFFF00"),
            ("Volume", -0.056, "SHORT", "#FF0000"),
            ("Price Action", 0.044, "LONG", "#00FF00")
        ]
        
        for signal_name, score, direction, color in signals:
            signal_frame = QFrame()
            signal_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 3px; padding: 3px; margin: 2px; background-color: #001100;")
            signal_layout = QHBoxLayout(signal_frame)
            signal_layout.setContentsMargins(5, 2, 5, 2)
            
            # Signal name
            name_label = QLabel(signal_name)
            name_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
            signal_layout.addWidget(name_label)
            
            # Score
            score_label = QLabel(f"{score:.3f}")
            score_label.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 10px;")
            signal_layout.addWidget(score_label)
            
            signal_layout.addStretch()
            
            # Direction with color
            direction_label = QLabel(direction)
            direction_label.setStyleSheet(f"color: {color}; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
            signal_layout.addWidget(direction_label)
            
            signals_layout.addWidget(signal_frame)
        
        layout.addWidget(signals_frame)
        
        # Overall Confidence
        confidence_frame = QFrame()
        confidence_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 5px; background-color: #002200; margin-top: 10px;")
        confidence_layout = QVBoxLayout(confidence_frame)
        
        conf_label = QLabel("Overall Confidence:")
        conf_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        confidence_layout.addWidget(conf_label)
        
        self.confidence_bar = QProgressBar()
        self.confidence_bar.setRange(0, 100)
        self.confidence_bar.setValue(51)
        self.confidence_bar.setFormat("51%")
        self.confidence_bar.setMaximumHeight(20)
        self.confidence_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 3px;
                background-color: #001100;
                text-align: center;
                color: #00FF00;
                font-family: 'Courier New';
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #FF0000, stop:0.5 #FFFF00, stop:1 #00FF00);
                border-radius: 2px;
            }
        """)
        confidence_layout.addWidget(self.confidence_bar)
        
        layout.addWidget(confidence_frame)
        layout.addStretch()

    def update_data(self, data):
        """Update signal scoring with new data"""
        if data and 'confidence' in data:
            confidence = int(data['confidence'])
            self.confidence_bar.setValue(confidence)
            self.confidence_bar.setFormat(f"{confidence}%")


class MarketIntelligencePanel(QWidget):
    """Market regime and intelligence in compact card format"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Section title
        title = QLabel("🧩 MARKET INTELLIGENCE")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Current Environment Card
        env_card = QFrame()
        env_card.setStyleSheet("border: 2px solid #00AA00; border-radius: 8px; padding: 10px; background-color: #001100;")
        env_layout = QVBoxLayout(env_card)
        
        # Regime State
        regime_label = QLabel("Current Regime:")
        regime_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        env_layout.addWidget(regime_label)
        
        self.regime_state = QLabel("LOW VOLATILITY")
        self.regime_state.setStyleSheet("color: #FFFF00; font-weight: bold; font-family: 'Courier New'; font-size: 14px;")
        self.regime_state.setAlignment(Qt.AlignCenter)
        env_layout.addWidget(self.regime_state)
        
        # Gauges row
        gauges_frame = QFrame()
        gauges_layout = QHBoxLayout(gauges_frame)
        
        # Volatility gauge
        vol_frame = QFrame()
        vol_layout = QVBoxLayout(vol_frame)
        vol_layout.setContentsMargins(0, 0, 0, 0)
        
        vol_label = QLabel("Volatility")
        vol_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 9px;")
        vol_label.setAlignment(Qt.AlignCenter)
        vol_layout.addWidget(vol_label)
        
        self.vol_gauge = QProgressBar()
        self.vol_gauge.setRange(0, 100)
        self.vol_gauge.setValue(33)
        self.vol_gauge.setFormat("0.33%")
        self.vol_gauge.setOrientation(Qt.Vertical)
        self.vol_gauge.setMaximumWidth(30)
        self.vol_gauge.setMinimumHeight(60)
        vol_layout.addWidget(self.vol_gauge)
        
        gauges_layout.addWidget(vol_frame)
        
        # Trend Strength gauge
        trend_frame = QFrame()
        trend_layout = QVBoxLayout(trend_frame)
        trend_layout.setContentsMargins(0, 0, 0, 0)
        
        trend_label = QLabel("Trend")
        trend_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 9px;")
        trend_label.setAlignment(Qt.AlignCenter)
        trend_layout.addWidget(trend_label)
        
        self.trend_gauge = QProgressBar()
        self.trend_gauge.setRange(-100, 100)
        self.trend_gauge.setValue(-87)
        self.trend_gauge.setFormat("-0.87")
        self.trend_gauge.setOrientation(Qt.Vertical)
        self.trend_gauge.setMaximumWidth(30)
        self.trend_gauge.setMinimumHeight(60)
        trend_layout.addWidget(self.trend_gauge)
        
        gauges_layout.addWidget(trend_frame)
        
        env_layout.addWidget(gauges_frame)
        
        # Factor adjustments
        factors_label = QLabel("Risk Factors:")
        factors_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px; margin-top: 5px;")
        env_layout.addWidget(factors_label)
        
        factors_text = QLabel("Leverage: 0.79x | Stop: 1.2x | Position: 0.9x")
        factors_text.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 9px;")
        env_layout.addWidget(factors_text)
        
        layout.addWidget(env_card)
        
        # Market Stats (collapsible)
        stats_button = QPushButton("📉 Show History")
        stats_button.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 5px;
                border-radius: 3px;
                font-family: 'Courier New';
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #004400;
            }
        """)
        layout.addWidget(stats_button)
        
        layout.addStretch()


class RiskExecutionPanel(QWidget):
    """Compact risk management and execution controls"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Section title
        title = QLabel("💼 RISK & EXECUTION")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)

        # Position Sizing (compact)
        sizing_frame = QFrame()
        sizing_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 8px; background-color: #001100;")
        sizing_layout = QGridLayout(sizing_frame)
        sizing_layout.setSpacing(5)

        # Max Risk slider
        sizing_layout.addWidget(QLabel("Max Risk %:"), 0, 0)
        self.risk_slider = QSlider(Qt.Horizontal)
        self.risk_slider.setRange(1, 10)
        self.risk_slider.setValue(2)
        self.risk_slider.setStyleSheet("QSlider::groove:horizontal { background: #003300; } QSlider::handle:horizontal { background: #00FF00; }")
        sizing_layout.addWidget(self.risk_slider, 0, 1)
        self.risk_value = QLabel("2%")
        self.risk_value.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-weight: bold;")
        sizing_layout.addWidget(self.risk_value, 0, 2)

        # Account size
        sizing_layout.addWidget(QLabel("Account:"), 1, 0)
        self.account_label = QLabel("$500")
        self.account_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-weight: bold;")
        sizing_layout.addWidget(self.account_label, 1, 1)

        # Use Max checkbox
        self.use_max_check = QCheckBox("Use Max")
        self.use_max_check.setStyleSheet("color: #00FF00; font-family: 'Courier New';")
        sizing_layout.addWidget(self.use_max_check, 1, 2)

        # Auto size result
        sizing_layout.addWidget(QLabel("Auto Size:"), 2, 0)
        self.auto_size_label = QLabel("Qty: 308 | $10")
        self.auto_size_label.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 10px;")
        sizing_layout.addWidget(self.auto_size_label, 2, 1, 1, 2)

        layout.addWidget(sizing_frame)

        # Stop/Take Profit zones
        stp_frame = QFrame()
        stp_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 8px; background-color: #001100; margin-top: 5px;")
        stp_layout = QVBoxLayout(stp_frame)

        stp_title = QLabel("🧮 Adaptive Stop/Take Profit")
        stp_title.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        stp_layout.addWidget(stp_title)

        # ATR Stop
        atr_layout = QHBoxLayout()
        atr_layout.addWidget(QLabel("ATR Stop:"))
        self.atr_stop_label = QLabel("0.23%")
        self.atr_stop_label.setStyleSheet("color: #FF0000; font-family: 'Courier New'; font-weight: bold;")
        atr_layout.addWidget(self.atr_stop_label)
        atr_layout.addStretch()

        # Risk:Reward
        rr_label = QLabel("R:R 1:1.17")
        rr_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-weight: bold;")
        atr_layout.addWidget(rr_label)

        stp_layout.addLayout(atr_layout)

        # SL/TP visual bars
        bars_layout = QHBoxLayout()

        # Stop Loss bar
        sl_frame = QFrame()
        sl_layout = QVBoxLayout(sl_frame)
        sl_layout.setContentsMargins(0, 0, 0, 0)

        sl_label = QLabel("SL")
        sl_label.setStyleSheet("color: #FF0000; font-family: 'Courier New'; font-size: 9px; font-weight: bold;")
        sl_label.setAlignment(Qt.AlignCenter)
        sl_layout.addWidget(sl_label)

        sl_bar = QProgressBar()
        sl_bar.setRange(0, 100)
        sl_bar.setValue(23)
        sl_bar.setFormat("0.23%")
        sl_bar.setMaximumHeight(15)
        sl_bar.setStyleSheet("""
            QProgressBar { border: 1px solid #AA0000; background-color: #110000; color: #FF0000; font-family: 'Courier New'; font-size: 8px; }
            QProgressBar::chunk { background-color: #AA0000; }
        """)
        sl_layout.addWidget(sl_bar)

        bars_layout.addWidget(sl_frame)

        # Take Profit bar
        tp_frame = QFrame()
        tp_layout = QVBoxLayout(tp_frame)
        tp_layout.setContentsMargins(0, 0, 0, 0)

        tp_label = QLabel("TP")
        tp_label.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 9px; font-weight: bold;")
        tp_label.setAlignment(Qt.AlignCenter)
        tp_layout.addWidget(tp_label)

        tp_bar = QProgressBar()
        tp_bar.setRange(0, 100)
        tp_bar.setValue(27)
        tp_bar.setFormat("0.27%")
        tp_bar.setMaximumHeight(15)
        tp_bar.setStyleSheet("""
            QProgressBar { border: 1px solid #00AA00; background-color: #001100; color: #00FF00; font-family: 'Courier New'; font-size: 8px; }
            QProgressBar::chunk { background-color: #00AA00; }
        """)
        tp_layout.addWidget(tp_bar)

        bars_layout.addWidget(tp_frame)

        stp_layout.addLayout(bars_layout)
        layout.addWidget(stp_frame)

        # Risk Score & Rules (compact)
        risk_frame = QFrame()
        risk_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 5px; background-color: #002200; margin-top: 5px;")
        risk_layout = QHBoxLayout(risk_frame)

        # Risk Score
        risk_layout.addWidget(QLabel("Risk Score:"))
        self.risk_score_label = QLabel("7.2/10")
        self.risk_score_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-weight: bold;")
        risk_layout.addWidget(self.risk_score_label)

        risk_layout.addStretch()

        # Drawdown
        risk_layout.addWidget(QLabel("DD:"))
        self.drawdown_label = QLabel("2.1%")
        self.drawdown_label.setStyleSheet("color: #FF6600; font-family: 'Courier New'; font-weight: bold;")
        risk_layout.addWidget(self.drawdown_label)

        # Active Rules (collapsible)
        rules_btn = QPushButton("Rules")
        rules_btn.setMaximumWidth(50)
        rules_btn.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 2px;
                border-radius: 3px;
                font-family: 'Courier New';
                font-size: 9px;
            }
        """)
        risk_layout.addWidget(rules_btn)

        layout.addWidget(risk_frame)
        layout.addStretch()


class LogAnalyticsPanel(QWidget):
    """Optional tab/modal for logs and analytics"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Tab widget for different log types
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #00AA00;
                background-color: #001100;
            }
            QTabBar::tab {
                background-color: #002200;
                color: #00FF00;
                padding: 5px 10px;
                border: 1px solid #00AA00;
                font-family: 'Courier New';
                font-size: 10px;
            }
            QTabBar::tab:selected {
                background-color: #00AA00;
                color: #000000;
            }
        """)

        # Signal Archive
        signal_tab = QTextEdit()
        signal_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        signal_tab.setPlainText("Signal Archive:\n[20:39:27] MACD: 0.043 | Volume: -0.056 | Price Action: 0.044\n[20:39:27] Total Score: 0.031 | Confidence: 51%\n[20:39:27] Decision: WAIT")
        tab_widget.addTab(signal_tab, "Signals")

        # Prompt/Response
        prompt_tab = QTextEdit()
        prompt_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        prompt_tab.setPlainText("LLM Prompt/Response:\nPrompt: Analyze DOGE/USDT market conditions...\nResponse: Given bearish momentum across timeframes...")
        tab_widget.addTab(prompt_tab, "AI Logs")

        # Trade Execution
        trade_tab = QTextEdit()
        trade_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        trade_tab.setPlainText("Trade Execution:\n[20:39:27] Analysis complete - SHORT signal\n[20:39:27] Risk management: TP 0.27%, SL 0.23%")
        tab_widget.addTab(trade_tab, "Trades")

        # Risk Flags
        risk_tab = QTextEdit()
        risk_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        risk_tab.setPlainText("Risk Flags:\n[20:39:27] Low volatility regime detected\n[20:39:27] Leverage factor reduced to 0.79x")
        tab_widget.addTab(risk_tab, "Risk")

        layout.addWidget(tab_widget)

        # Auto-save toggle
        save_frame = QFrame()
        save_layout = QHBoxLayout(save_frame)

        self.auto_save_check = QCheckBox("Auto-Save Logs to ./trades/")
        self.auto_save_check.setChecked(True)
        self.auto_save_check.setStyleSheet("color: #00FF00; font-family: 'Courier New';")
        save_layout.addWidget(self.auto_save_check)

        save_layout.addStretch()

        clear_btn = QPushButton("Clear All")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #330000;
                color: #FF0000;
                border: 1px solid #AA0000;
                padding: 5px 10px;
                border-radius: 3px;
                font-family: 'Courier New';
            }
        """)
        save_layout.addWidget(clear_btn)

        layout.addWidget(save_frame)
