"""
Reorganized UI Panels for Epinnox v6
Following the new UX strategy with role-based organization
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg
from datetime import datetime


class CompactMarketInfoPanel(QWidget):
    """Mini-panel below chart showing current price, spread, volume, etc."""
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(80)  # FIX: Panel minimum height
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)  # FIX: Size policy
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 8, 15, 8)
        layout.setSpacing(25)

        # PROFESSIONAL MARKET INFO LAYOUT
        # Current Price (prominent)
        price_container = QFrame()
        price_container.setStyleSheet("""
            QFrame {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px 12px;
            }
        """)
        price_layout = QVBoxLayout(price_container)
        price_layout.setContentsMargins(8, 6, 8, 6)
        price_layout.setSpacing(2)

        price_title = QLabel("PRICE")
        price_title.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        price_layout.addWidget(price_title)

        self.price_label = QLabel("$0.3245")
        self.price_label.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 16px;")
        price_layout.addWidget(self.price_label)

        layout.addWidget(price_container)

        # Spread
        spread_container = QFrame()
        spread_container.setStyleSheet("""
            QFrame {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px 12px;
            }
        """)
        spread_layout = QVBoxLayout(spread_container)
        spread_layout.setContentsMargins(8, 6, 8, 6)
        spread_layout.setSpacing(2)

        spread_title = QLabel("SPREAD")
        spread_title.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        spread_layout.addWidget(spread_title)

        self.spread_label = QLabel("0.0001")
        self.spread_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 13px; font-weight: bold;")
        spread_layout.addWidget(self.spread_label)

        layout.addWidget(spread_container)

        # 24h Range
        range_container = QFrame()
        range_container.setStyleSheet("""
            QFrame {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px 12px;
            }
        """)
        range_layout = QVBoxLayout(range_container)
        range_layout.setContentsMargins(8, 6, 8, 6)
        range_layout.setSpacing(2)

        range_title = QLabel("24H RANGE")
        range_title.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        range_layout.addWidget(range_title)

        self.range_label = QLabel("$0.31-$0.34")
        self.range_label.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 13px; font-weight: bold;")
        range_layout.addWidget(self.range_label)

        layout.addWidget(range_container)

        # Volume
        volume_container = QFrame()
        volume_container.setStyleSheet("""
            QFrame {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px 12px;
            }
        """)
        volume_layout = QVBoxLayout(volume_container)
        volume_layout.setContentsMargins(8, 6, 8, 6)
        volume_layout.setSpacing(2)

        volume_title = QLabel("VOLUME")
        volume_title.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        volume_layout.addWidget(volume_title)

        self.volume_label = QLabel("1.2M")
        self.volume_label.setStyleSheet("color: #FF00FF; font-family: 'Courier New'; font-size: 13px; font-weight: bold;")
        volume_layout.addWidget(self.volume_label)

        layout.addWidget(volume_container)
        
        # Order Book Pressure (visual bar)
        pressure_frame = QFrame()
        pressure_layout = QVBoxLayout(pressure_frame)
        pressure_layout.setContentsMargins(0, 0, 0, 0)
        
        pressure_label = QLabel("OB Pressure:")
        pressure_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        pressure_layout.addWidget(pressure_label)
        
        self.pressure_bar = QProgressBar()
        self.pressure_bar.setRange(-100, 100)
        self.pressure_bar.setValue(25)  # Example: 25% bullish
        self.pressure_bar.setMaximumHeight(15)
        self.pressure_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 3px;
                background-color: #001100;
                text-align: center;
                color: #00FF00;
                font-family: 'Courier New';
                font-size: 9px;
            }
            QProgressBar::chunk {
                background-color: #00AA00;
                border-radius: 2px;
            }
        """)
        pressure_layout.addWidget(self.pressure_bar)
        
        layout.addWidget(pressure_frame)
        layout.addStretch()

    def update_data(self, data):
        """Update market info with new data"""
        if data and 'current_price' in data:
            self.price_label.setText(f"Price: ${data['current_price']:.4f}")
        if data and 'spread' in data:
            self.spread_label.setText(f"Spread: {data['spread']:.4f}")
        if data and 'volume_24h' in data:
            self.volume_label.setText(f"Vol: {data['volume_24h']}")
        if data and 'orderbook_pressure' in data:
            self.pressure_bar.setValue(int(data['orderbook_pressure'] * 100))


class TrendSystemPanel(QWidget):
    """Compact trend analysis with heatmap-style display"""

    def __init__(self):
        super().__init__()
        self.setMinimumHeight(300)  # FIX: Panel minimum height
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)  # FIX: Size policy
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Section title
        title = QLabel("✳️ TREND SYSTEM")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # PROFESSIONAL TREND SUMMARY
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background-color: #001100;
                border: 2px solid #00AA00;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        summary_layout = QVBoxLayout(summary_frame)
        summary_layout.setContentsMargins(12, 10, 12, 10)
        summary_layout.setSpacing(6)

        # Header
        trend_header = QLabel("OVERALL TREND")
        trend_header.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        trend_header.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(trend_header)

        self.trend_direction = QLabel("STRONG BEARISH")
        self.trend_direction.setStyleSheet("color: #FF0000; font-weight: bold; font-family: 'Courier New'; font-size: 16px;")
        self.trend_direction.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(self.trend_direction)

        self.trend_strength = QLabel("Strength: -0.87")
        self.trend_strength.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        self.trend_strength.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(self.trend_strength)
        
        layout.addWidget(summary_frame)
        
        # Timeframe Heatmap
        heatmap_label = QLabel("Timeframe Analysis:")
        heatmap_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px; margin-top: 10px;")
        layout.addWidget(heatmap_label)
        
        heatmap_frame = QFrame()
        heatmap_layout = QGridLayout(heatmap_frame)
        heatmap_layout.setSpacing(2)
        
        timeframes = ["1m", "5m", "15m"]
        trends = ["bearish", "strong_bearish", "strong_bearish"]
        colors = ["#FF6600", "#FF0000", "#FF0000"]
        
        for i, (tf, trend, color) in enumerate(zip(timeframes, trends, colors)):
            tf_label = QLabel(tf)
            tf_label.setStyleSheet(f"background-color: {color}; color: #FFFFFF; padding: 3px; border-radius: 3px; font-family: 'Courier New'; font-size: 9px; font-weight: bold;")
            tf_label.setAlignment(Qt.AlignCenter)
            tf_label.setMinimumWidth(40)
            heatmap_layout.addWidget(tf_label, 0, i)
        
        layout.addWidget(heatmap_frame)
        
        # Momentum Status
        momentum_frame = QFrame()
        momentum_layout = QHBoxLayout(momentum_frame)
        momentum_layout.setContentsMargins(0, 5, 0, 0)
        
        momentum_label = QLabel("Momentum:")
        momentum_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        momentum_layout.addWidget(momentum_label)
        
        self.momentum_indicator = QLabel("⬇️ DECLINING")
        self.momentum_indicator.setStyleSheet("color: #FF0000; font-weight: bold; font-family: 'Courier New'; font-size: 10px;")
        momentum_layout.addWidget(self.momentum_indicator)
        
        layout.addWidget(momentum_frame)
        layout.addStretch()

    def update_data(self, data):
        """Update trend system with new data"""
        if data and 'trend_direction' in data:
            self.trend_direction.setText(data['trend_direction'].upper())
        if data and 'trend_strength' in data:
            self.trend_strength.setText(f"Strength: {data['trend_strength']:.2f}")
        if data and 'momentum' in data:
            momentum_text = "⬆️ RISING" if data['momentum'] > 0 else "⬇️ DECLINING"
            self.momentum_indicator.setText(momentum_text)


class SignalScoringCompactPanel(QWidget):
    """Compact signal scoring with top 3 signals"""

    def __init__(self):
        super().__init__()
        self.setMinimumHeight(300)  # FIX: Panel minimum height
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)  # FIX: Size policy
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Section title
        title = QLabel("✅ SIGNAL SCORING")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # PROFESSIONAL SIGNAL VISUALIZATION
        signals_frame = QFrame()
        signals_layout = QVBoxLayout(signals_frame)

        # Signal data with confidence and weights
        signals = [
            ("MACD", 0.043, "WAIT", "#FFFF00", 65, 0.3),
            ("Volume", -0.056, "SHORT", "#FF0000", 78, 0.4),
            ("Price Action", 0.044, "LONG", "#00FF00", 72, 0.3)
        ]

        for signal_name, score, direction, color, confidence, weight in signals:
            signal_frame = QFrame()
            signal_frame.setFixedHeight(85)  # Consistent card height
            signal_frame.setStyleSheet("""
                QFrame {
                    background-color: #001100;
                    border: 1px solid #00AA00;
                    border-radius: 6px;
                    margin: 2px;
                }
            """)
            signal_layout = QVBoxLayout(signal_frame)
            signal_layout.setContentsMargins(10, 8, 10, 8)
            signal_layout.setSpacing(4)

            # Top row: Signal name and direction arrow
            top_row = QHBoxLayout()
            top_row.setContentsMargins(0, 0, 0, 0)

            # Signal name with weight
            name_label = QLabel(f"{signal_name} ({weight:.1f}x)")
            name_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
            top_row.addWidget(name_label)

            top_row.addStretch()

            # Visual direction indicator
            if direction == "LONG":
                arrow = "🟢 ⬆️"
            elif direction == "SHORT":
                arrow = "🔴 ⬇️"
            else:
                arrow = "🟡 ⏸️"

            direction_label = QLabel(f"{arrow} {direction}")
            direction_label.setStyleSheet(f"color: {color}; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
            top_row.addWidget(direction_label)

            signal_layout.addLayout(top_row)

            # Middle row: Score and confidence
            middle_row = QHBoxLayout()
            middle_row.setContentsMargins(0, 0, 0, 0)

            score_label = QLabel(f"Score: {score:.3f}")
            score_label.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 10px;")
            middle_row.addWidget(score_label)

            middle_row.addStretch()

            confidence_label = QLabel(f"Conf: {confidence}%")
            confidence_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 10px;")
            middle_row.addWidget(confidence_label)

            signal_layout.addLayout(middle_row)

            # Bottom row: Visual strength bar
            strength_bar = QProgressBar()
            strength_bar.setRange(0, 100)
            strength_bar.setValue(confidence)
            strength_bar.setMaximumHeight(12)
            strength_bar.setTextVisible(False)

            # Color-coded strength bar
            if direction == "LONG":
                bar_color = "#00AA00"
            elif direction == "SHORT":
                bar_color = "#AA0000"
            else:
                bar_color = "#AAAA00"

            strength_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 1px solid #00AA00;
                    border-radius: 3px;
                    background-color: #002200;
                }}
                QProgressBar::chunk {{
                    background-color: {bar_color};
                    border-radius: 2px;
                }}
            """)
            signal_layout.addWidget(strength_bar)

            signals_layout.addWidget(signal_frame)
        
        layout.addWidget(signals_frame)
        
        # PROFESSIONAL SIGNAL SCORE TOTAL
        total_frame = QFrame()
        total_frame.setStyleSheet("border: 2px solid #00AA00; border-radius: 8px; padding: 10px; background-color: #002200; margin-top: 10px;")
        total_layout = QVBoxLayout(total_frame)
        total_layout.setSpacing(8)

        # Header with signal count
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)

        total_label = QLabel("📊 SIGNAL SCORE TOTAL")
        total_label.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        header_layout.addWidget(total_label)

        header_layout.addStretch()

        signal_count = QLabel("3/7 Active")
        signal_count.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 10px;")
        header_layout.addWidget(signal_count)

        total_layout.addLayout(header_layout)

        # Combined score calculation
        combined_score = 0.031  # Example: weighted average
        confidence_pct = 72  # Example: combined confidence

        # Score display
        score_layout = QHBoxLayout()
        score_layout.setContentsMargins(0, 0, 0, 0)

        score_value = QLabel(f"Score: {combined_score:+.3f}")
        score_value.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 14px; font-weight: bold;")
        score_layout.addWidget(score_value)

        score_layout.addStretch()

        # Overall decision
        if combined_score > 0.05:
            decision = "🟢 LONG"
            decision_color = "#00FF00"
        elif combined_score < -0.05:
            decision = "🔴 SHORT"
            decision_color = "#FF0000"
        else:
            decision = "🟡 WAIT"
            decision_color = "#FFFF00"

        decision_label = QLabel(decision)
        decision_label.setStyleSheet(f"color: {decision_color}; font-family: 'Courier New'; font-size: 14px; font-weight: bold;")
        score_layout.addWidget(decision_label)

        total_layout.addLayout(score_layout)

        # Professional confidence bar with overlaid text
        confidence_container = QFrame()
        confidence_container.setMaximumHeight(30)
        confidence_container_layout = QVBoxLayout(confidence_container)
        confidence_container_layout.setContentsMargins(0, 0, 0, 0)

        conf_header = QLabel("Combined Confidence:")
        conf_header.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px;")
        confidence_container_layout.addWidget(conf_header)

        self.confidence_bar = QProgressBar()
        self.confidence_bar.setRange(0, 100)
        self.confidence_bar.setValue(confidence_pct)
        self.confidence_bar.setFormat(f"{confidence_pct}% Confidence")
        self.confidence_bar.setMaximumHeight(20)
        self.confidence_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 5px;
                background-color: #001100;
                text-align: center;
                color: #000000;
                font-family: 'Courier New';
                font-weight: bold;
                font-size: 11px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #AA0000, stop:0.3 #AAAA00, stop:0.7 #00AA00, stop:1 #00FF00);
                border-radius: 4px;
            }
        """)
        confidence_container_layout.addWidget(self.confidence_bar)

        total_layout.addWidget(confidence_container)
        
        layout.addWidget(total_frame)
        layout.addStretch()

    def update_data(self, data):
        """Update signal scoring with new data"""
        if data and 'confidence' in data:
            confidence = int(data['confidence'])
            self.confidence_bar.setValue(confidence)
            self.confidence_bar.setFormat(f"{confidence}%")


class MarketIntelligencePanel(QWidget):
    """Market regime and intelligence in compact card format"""

    def __init__(self):
        super().__init__()
        self.setMinimumHeight(350)  # FIX: Panel minimum height
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)  # FIX: Size policy
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Section title
        title = QLabel("🧩 MARKET INTELLIGENCE")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # PROFESSIONAL ENVIRONMENT CARD
        env_card = QFrame()
        env_card.setStyleSheet("""
            QFrame {
                background-color: #001100;
                border: 2px solid #00AA00;
                border-radius: 10px;
                padding: 0px;
            }
        """)
        env_layout = QVBoxLayout(env_card)
        env_layout.setContentsMargins(15, 12, 15, 12)
        env_layout.setSpacing(10)

        # Header with consistent styling
        header_frame = QFrame()
        header_frame.setStyleSheet("background-color: #002200; border-radius: 6px; padding: 8px;")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(8, 6, 8, 6)
        header_layout.setSpacing(4)

        regime_label = QLabel("CURRENT REGIME")
        regime_label.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        regime_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(regime_label)

        self.regime_state = QLabel("LOW VOLATILITY")
        self.regime_state.setStyleSheet("color: #FFFF00; font-weight: bold; font-family: 'Courier New'; font-size: 16px;")
        self.regime_state.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(self.regime_state)

        env_layout.addWidget(header_frame)
        
        # PROFESSIONAL GAUGES LAYOUT
        gauges_container = QFrame()
        gauges_container.setStyleSheet("""
            QFrame {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        gauges_layout = QHBoxLayout(gauges_container)
        gauges_layout.setContentsMargins(12, 10, 12, 10)
        gauges_layout.setSpacing(20)

        # Volatility gauge with professional styling
        vol_container = QFrame()
        vol_container.setStyleSheet("background-color: #001100; border-radius: 6px; padding: 8px;")
        vol_layout = QVBoxLayout(vol_container)
        vol_layout.setContentsMargins(8, 6, 8, 6)
        vol_layout.setSpacing(6)

        vol_label = QLabel("VOLATILITY")
        vol_label.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        vol_label.setAlignment(Qt.AlignCenter)
        vol_layout.addWidget(vol_label)

        self.vol_gauge = QProgressBar()
        self.vol_gauge.setRange(0, 100)
        self.vol_gauge.setValue(33)
        self.vol_gauge.setFormat("33%")
        self.vol_gauge.setOrientation(Qt.Vertical)
        self.vol_gauge.setFixedSize(40, 80)
        self.vol_gauge.setStyleSheet("""
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 4px;
                background-color: #000000;
                text-align: center;
                color: #00FF00;
                font-family: 'Courier New';
                font-size: 10px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #FFAA00;
                border-radius: 3px;
            }
        """)
        vol_layout.addWidget(self.vol_gauge, 0, Qt.AlignCenter)

        vol_value = QLabel("0.33%")
        vol_value.setStyleSheet("color: #FFAA00; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        vol_value.setAlignment(Qt.AlignCenter)
        vol_layout.addWidget(vol_value)

        gauges_layout.addWidget(vol_container)

        # Trend Strength gauge with professional styling
        trend_container = QFrame()
        trend_container.setStyleSheet("background-color: #001100; border-radius: 6px; padding: 8px;")
        trend_layout = QVBoxLayout(trend_container)
        trend_layout.setContentsMargins(8, 6, 8, 6)
        trend_layout.setSpacing(6)

        trend_label = QLabel("TREND")
        trend_label.setStyleSheet("color: #888888; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        trend_label.setAlignment(Qt.AlignCenter)
        trend_layout.addWidget(trend_label)

        self.trend_gauge = QProgressBar()
        self.trend_gauge.setRange(0, 100)
        self.trend_gauge.setValue(13)  # Convert -87 to 0-100 scale (13%)
        self.trend_gauge.setFormat("BEAR")
        self.trend_gauge.setOrientation(Qt.Vertical)
        self.trend_gauge.setFixedSize(40, 80)
        self.trend_gauge.setStyleSheet("""
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 4px;
                background-color: #000000;
                text-align: center;
                color: #00FF00;
                font-family: 'Courier New';
                font-size: 10px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #FF0000;
                border-radius: 3px;
            }
        """)
        trend_layout.addWidget(self.trend_gauge, 0, Qt.AlignCenter)

        trend_value = QLabel("-0.87")
        trend_value.setStyleSheet("color: #FF0000; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        trend_value.setAlignment(Qt.AlignCenter)
        trend_layout.addWidget(trend_value)

        gauges_layout.addWidget(trend_container)

        env_layout.addWidget(gauges_container)
        
        # Factor adjustments
        factors_label = QLabel("Risk Factors:")
        factors_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px; margin-top: 5px;")
        env_layout.addWidget(factors_label)
        
        factors_text = QLabel("Leverage: 0.79x | Stop: 1.2x | Position: 0.9x")
        factors_text.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 9px;")
        env_layout.addWidget(factors_text)
        
        layout.addWidget(env_card)
        
        # Market Stats (collapsible)
        stats_button = QPushButton("📉 Show History")
        stats_button.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 5px;
                border-radius: 3px;
                font-family: 'Courier New';
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #004400;
            }
        """)
        layout.addWidget(stats_button)
        
        layout.addStretch()


class RiskExecutionPanel(QWidget):
    """Compact risk management and execution controls"""

    def __init__(self):
        super().__init__()
        self.setMinimumHeight(400)  # FIX: Panel minimum height
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)  # FIX: Size policy
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Section title
        title = QLabel("💼 RISK & EXECUTION")
        title.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New'; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(title)

        # Position Sizing (compact)
        sizing_frame = QFrame()
        sizing_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 8px; background-color: #001100;")
        sizing_layout = QGridLayout(sizing_frame)
        sizing_layout.setSpacing(5)

        # Max Risk slider
        sizing_layout.addWidget(QLabel("Max Risk %:"), 0, 0)
        self.risk_slider = QSlider(Qt.Horizontal)
        self.risk_slider.setRange(1, 10)
        self.risk_slider.setValue(2)
        self.risk_slider.setStyleSheet("QSlider::groove:horizontal { background: #003300; } QSlider::handle:horizontal { background: #00FF00; }")
        sizing_layout.addWidget(self.risk_slider, 0, 1)
        self.risk_value = QLabel("2%")
        self.risk_value.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-weight: bold;")
        sizing_layout.addWidget(self.risk_value, 0, 2)

        # Account size
        sizing_layout.addWidget(QLabel("Account:"), 1, 0)
        self.account_label = QLabel("$500")
        self.account_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-weight: bold;")
        sizing_layout.addWidget(self.account_label, 1, 1)

        # Use Max checkbox
        self.use_max_check = QCheckBox("Use Max")
        self.use_max_check.setStyleSheet("color: #00FF00; font-family: 'Courier New';")
        sizing_layout.addWidget(self.use_max_check, 1, 2)

        # Auto size result
        sizing_layout.addWidget(QLabel("Auto Size:"), 2, 0)
        self.auto_size_label = QLabel("Qty: 308 | $10")
        self.auto_size_label.setStyleSheet("color: #00FFFF; font-family: 'Courier New'; font-size: 10px;")
        sizing_layout.addWidget(self.auto_size_label, 2, 1, 1, 2)

        layout.addWidget(sizing_frame)

        # Stop/Take Profit zones
        stp_frame = QFrame()
        stp_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 8px; background-color: #001100; margin-top: 5px;")
        stp_layout = QVBoxLayout(stp_frame)

        stp_title = QLabel("🧮 Adaptive Stop/Take Profit")
        stp_title.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        stp_layout.addWidget(stp_title)

        # ATR Stop
        atr_layout = QHBoxLayout()
        atr_layout.addWidget(QLabel("ATR Stop:"))
        self.atr_stop_label = QLabel("0.23%")
        self.atr_stop_label.setStyleSheet("color: #FF0000; font-family: 'Courier New'; font-weight: bold;")
        atr_layout.addWidget(self.atr_stop_label)
        atr_layout.addStretch()

        # Risk:Reward
        rr_label = QLabel("R:R 1:1.17")
        rr_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-weight: bold;")
        atr_layout.addWidget(rr_label)

        stp_layout.addLayout(atr_layout)

        # PROFESSIONAL VISUAL SL/TP DISPLAY
        visual_sltp_frame = QFrame()
        visual_sltp_frame.setStyleSheet("border: 1px solid #00AA00; border-radius: 5px; padding: 8px; background-color: #002200; margin-top: 5px;")
        visual_layout = QVBoxLayout(visual_sltp_frame)
        visual_layout.setSpacing(5)

        # Price levels header
        levels_header = QLabel("Price Levels:")
        levels_header.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        visual_layout.addWidget(levels_header)

        # Price level display
        current_price = 0.3245  # Example current price
        sl_price = current_price * 0.9977  # -0.23%
        tp_price = current_price * 1.0027  # +0.27%

        price_layout = QHBoxLayout()
        price_layout.setContentsMargins(0, 0, 0, 0)

        sl_price_label = QLabel(f"SL: ${sl_price:.4f}")
        sl_price_label.setStyleSheet("color: #FF0000; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        price_layout.addWidget(sl_price_label)

        price_layout.addStretch()

        current_price_label = QLabel(f"Entry: ${current_price:.4f}")
        current_price_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        price_layout.addWidget(current_price_label)

        price_layout.addStretch()

        tp_price_label = QLabel(f"TP: ${tp_price:.4f}")
        tp_price_label.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 10px; font-weight: bold;")
        price_layout.addWidget(tp_price_label)

        visual_layout.addLayout(price_layout)

        # Professional visual bar: |--SL--|--Entry--|----TP----|
        visual_bar_container = QFrame()
        visual_bar_container.setMaximumHeight(25)
        visual_bar_layout = QHBoxLayout(visual_bar_container)
        visual_bar_layout.setContentsMargins(0, 0, 0, 0)
        visual_bar_layout.setSpacing(0)

        # SL zone (23% of total range)
        sl_zone = QFrame()
        sl_zone.setStyleSheet("background-color: #AA0000; border: 1px solid #FF0000; border-radius: 3px;")
        sl_zone.setMaximumWidth(46)  # 23% of 200px
        sl_zone_layout = QVBoxLayout(sl_zone)
        sl_zone_layout.setContentsMargins(2, 2, 2, 2)
        sl_zone_label = QLabel("SL")
        sl_zone_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 8px; font-weight: bold;")
        sl_zone_label.setAlignment(Qt.AlignCenter)
        sl_zone_layout.addWidget(sl_zone_label)
        visual_bar_layout.addWidget(sl_zone)

        # Entry zone (small marker)
        entry_zone = QFrame()
        entry_zone.setStyleSheet("background-color: #FFFF00; border: 1px solid #FFFF00; border-radius: 2px;")
        entry_zone.setMaximumWidth(8)
        visual_bar_layout.addWidget(entry_zone)

        # TP zone (27% of total range)
        tp_zone = QFrame()
        tp_zone.setStyleSheet("background-color: #00AA00; border: 1px solid #00FF00; border-radius: 3px;")
        tp_zone.setMaximumWidth(54)  # 27% of 200px
        tp_zone_layout = QVBoxLayout(tp_zone)
        tp_zone_layout.setContentsMargins(2, 2, 2, 2)
        tp_zone_label = QLabel("TP")
        tp_zone_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 8px; font-weight: bold;")
        tp_zone_label.setAlignment(Qt.AlignCenter)
        tp_zone_layout.addWidget(tp_zone_label)
        visual_bar_layout.addWidget(tp_zone)

        # Remaining space
        visual_bar_layout.addStretch()

        visual_layout.addWidget(visual_bar_container)

        # Distance indicators
        distance_layout = QHBoxLayout()
        distance_layout.setContentsMargins(0, 0, 0, 0)

        sl_distance = QLabel("↓ 0.23%")
        sl_distance.setStyleSheet("color: #FF6666; font-family: 'Courier New'; font-size: 9px;")
        distance_layout.addWidget(sl_distance)

        distance_layout.addStretch()

        tp_distance = QLabel("↑ 0.27%")
        tp_distance.setStyleSheet("color: #66FF66; font-family: 'Courier New'; font-size: 9px;")
        distance_layout.addWidget(tp_distance)

        visual_layout.addLayout(distance_layout)

        stp_layout.addWidget(visual_sltp_frame)
        layout.addWidget(stp_frame)

        # PROFESSIONAL RISK SCORE & RULES
        risk_frame = QFrame()
        risk_frame.setStyleSheet("""
            QFrame {
                background-color: #002200;
                border: 2px solid #00AA00;
                border-radius: 8px;
                padding: 12px;
                margin-top: 8px;
            }
        """)
        risk_layout = QVBoxLayout(risk_frame)
        risk_layout.setContentsMargins(12, 10, 12, 10)
        risk_layout.setSpacing(8)

        # Header
        risk_header = QLabel("⚠️ RISK ASSESSMENT")
        risk_header.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        risk_header.setAlignment(Qt.AlignCenter)
        risk_layout.addWidget(risk_header)

        # Risk metrics grid
        metrics_layout = QGridLayout()
        metrics_layout.setSpacing(8)

        # Risk Score
        score_label = QLabel("Risk Score:")
        score_label.setStyleSheet("color: #CCCCCC; font-family: 'Courier New'; font-size: 11px;")
        metrics_layout.addWidget(score_label, 0, 0)

        self.risk_score_label = QLabel("7.2/10")
        self.risk_score_label.setStyleSheet("color: #FFFF00; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        metrics_layout.addWidget(self.risk_score_label, 0, 1)

        # Drawdown
        dd_label = QLabel("Drawdown:")
        dd_label.setStyleSheet("color: #CCCCCC; font-family: 'Courier New'; font-size: 11px;")
        metrics_layout.addWidget(dd_label, 1, 0)

        self.drawdown_label = QLabel("2.1%")
        self.drawdown_label.setStyleSheet("color: #FF6600; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        metrics_layout.addWidget(self.drawdown_label, 1, 1)

        # Win Rate
        wr_label = QLabel("Win Rate:")
        wr_label.setStyleSheet("color: #CCCCCC; font-family: 'Courier New'; font-size: 11px;")
        metrics_layout.addWidget(wr_label, 2, 0)

        win_rate_label = QLabel("68%")
        win_rate_label.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        metrics_layout.addWidget(win_rate_label, 2, 1)

        risk_layout.addLayout(metrics_layout)

        # Active Rules button
        rules_btn = QPushButton("📋 View Active Rules")
        rules_btn.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 8px 12px;
                border-radius: 5px;
                font-family: 'Courier New';
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #004400;
            }
        """)
        risk_layout.addWidget(rules_btn)

        layout.addWidget(risk_frame)
        layout.addStretch()


class LogAnalyticsPanel(QWidget):
    """Optional tab/modal for logs and analytics"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Tab widget for different log types
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #00AA00;
                background-color: #001100;
            }
            QTabBar::tab {
                background-color: #002200;
                color: #00FF00;
                padding: 5px 10px;
                border: 1px solid #00AA00;
                font-family: 'Courier New';
                font-size: 10px;
            }
            QTabBar::tab:selected {
                background-color: #00AA00;
                color: #000000;
            }
        """)

        # Signal Archive
        signal_tab = QTextEdit()
        signal_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        signal_tab.setPlainText("Signal Archive:\n[20:39:27] MACD: 0.043 | Volume: -0.056 | Price Action: 0.044\n[20:39:27] Total Score: 0.031 | Confidence: 51%\n[20:39:27] Decision: WAIT")
        tab_widget.addTab(signal_tab, "Signals")

        # Prompt/Response
        prompt_tab = QTextEdit()
        prompt_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        prompt_tab.setPlainText("LLM Prompt/Response:\nPrompt: Analyze DOGE/USDT market conditions...\nResponse: Given bearish momentum across timeframes...")
        tab_widget.addTab(prompt_tab, "AI Logs")

        # Trade Execution
        trade_tab = QTextEdit()
        trade_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        trade_tab.setPlainText("Trade Execution:\n[20:39:27] Analysis complete - SHORT signal\n[20:39:27] Risk management: TP 0.27%, SL 0.23%")
        tab_widget.addTab(trade_tab, "Trades")

        # Risk Flags
        risk_tab = QTextEdit()
        risk_tab.setStyleSheet("background-color: #000000; color: #00FF00; font-family: 'Courier New'; font-size: 10px;")
        risk_tab.setPlainText("Risk Flags:\n[20:39:27] Low volatility regime detected\n[20:39:27] Leverage factor reduced to 0.79x")
        tab_widget.addTab(risk_tab, "Risk")

        layout.addWidget(tab_widget)

        # Auto-save toggle
        save_frame = QFrame()
        save_layout = QHBoxLayout(save_frame)

        self.auto_save_check = QCheckBox("Auto-Save Logs to ./trades/")
        self.auto_save_check.setChecked(True)
        self.auto_save_check.setStyleSheet("color: #00FF00; font-family: 'Courier New';")
        save_layout.addWidget(self.auto_save_check)

        save_layout.addStretch()

        clear_btn = QPushButton("Clear All")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #330000;
                color: #FF0000;
                border: 1px solid #AA0000;
                padding: 5px 10px;
                border-radius: 3px;
                font-family: 'Courier New';
            }
        """)
        save_layout.addWidget(clear_btn)

        layout.addWidget(save_frame)
