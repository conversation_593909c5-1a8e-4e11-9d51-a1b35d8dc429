#!/usr/bin/env python3
"""
GUI Integration Script
Connects the trading system output to the GUI data manager
"""
import json
import time
import logging
import threading
import random
from datetime import datetime, timedelta
from pathlib import Path
import psutil
import os

logger = logging.getLogger(__name__)

class TradingSystemGUIIntegration:
    """
    Integrates the trading system with the GUI by monitoring system output
    and updating GUI data files
    """
    
    def __init__(self):
        self.running = False
        self.data_file = Path("gui_data.json")
        self.status_file = Path("system_status.json")
        self.log_file = Path("trading_system.log")
        
        # Initialize data
        self.system_start_time = datetime.now()
        self.last_analysis_time = None
        self.analysis_count = 0
        self.error_count = 0
        self.warning_count = 0
        
        logger.info("GUI Integration initialized")
    
    def start_monitoring(self):
        """Start monitoring the trading system"""
        self.running = True
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        logger.info("Started GUI integration monitoring")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False
        logger.info("Stopped GUI integration monitoring")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Update system status
                self._update_system_status()
                
                # Update performance data
                self._update_performance_data()
                
                # Generate sample market data (in real implementation, this would come from the trading system)
                self._update_sample_data()
                
                # Sleep for a short interval
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(5)
    
    def _update_system_status(self):
        """Update system status information"""
        try:
            # Calculate uptime
            uptime = datetime.now() - self.system_start_time
            uptime_str = str(uptime).split('.')[0]  # Remove microseconds
            
            # Get system resource usage
            process = psutil.Process(os.getpid())
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            cpu_usage = process.cpu_percent()
            
            # Check if trading system is running (simplified check)
            trading_running = self._check_trading_system_running()
            
            status_data = {
                'system_status': {
                    'connected': True,  # Assume connected if this script is running
                    'running': trading_running,
                    'uptime': uptime_str,
                    'last_analysis': self.last_analysis_time.strftime('%H:%M:%S') if self.last_analysis_time else '--',
                    'next_analysis': self._calculate_next_analysis_time(),
                    'errors': self.error_count,
                    'warnings': self.warning_count,
                    'memory_usage': memory_usage,
                    'cpu_usage': cpu_usage
                }
            }
            
            # Write status file
            with open(self.status_file, 'w') as f:
                json.dump(status_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error updating system status: {e}")
    
    def _update_performance_data(self):
        """Update performance metrics"""
        try:
            # Calculate runtime
            runtime = datetime.now() - self.system_start_time
            runtime_str = str(runtime).split('.')[0]
            
            # Calculate success rate (placeholder)
            successful_signals = max(0, self.analysis_count - self.error_count)
            success_rate = (successful_signals / max(1, self.analysis_count)) * 100
            
            performance_data = {
                'performance': {
                    'total_signals': self.analysis_count,
                    'successful_signals': successful_signals,
                    'success_rate': success_rate,
                    'avg_confidence': 65.0,  # Placeholder
                    'total_runtime': runtime_str,
                    'memory_usage': psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024,
                    'cpu_usage': psutil.Process(os.getpid()).cpu_percent()
                }
            }
            
            # Update main data file
            self._update_data_file(performance_data)
            
        except Exception as e:
            logger.error(f"Error updating performance data: {e}")
    
    def _update_sample_data(self):
        """Update sample trading data (for demonstration)"""
        try:
            
            # Generate sample market data
            base_price = 0.38  # DOGE/USDT approximate price
            price_change = random.uniform(-0.01, 0.01)
            current_price = base_price + price_change
            
            # Sample data structure
            sample_data = {
                'timestamp': datetime.now().isoformat(),
                'market_data': {
                    'symbol': 'DOGE/USDT',
                    'price': current_price,
                    'volume': random.uniform(1000000, 5000000),
                    'change_24h': price_change,
                    'change_percent': (price_change / base_price) * 100,
                    'high_24h': base_price + 0.02,
                    'low_24h': base_price - 0.02,
                    'order_book': {
                        'bids': [[current_price - 0.001, 1000], [current_price - 0.002, 1500]],
                        'asks': [[current_price + 0.001, 1200], [current_price + 0.002, 800]],
                        'spread': 0.002,
                        'imbalance': random.uniform(-10, 10)
                    },
                    'recent_trades': [
                        [datetime.now().strftime('%H:%M:%S'), current_price, random.uniform(100, 1000), 'buy' if random.random() > 0.5 else 'sell']
                        for _ in range(5)
                    ]
                },
                'timeframe_analysis': {
                    'timeframes': ['1m', '5m', '15m'],
                    'trends': {
                        '1m': {'direction': random.choice(['bullish', 'bearish', 'neutral']), 'strength': random.uniform(0, 1)},
                        '5m': {'direction': random.choice(['bullish', 'bearish', 'neutral']), 'strength': random.uniform(0, 1)},
                        '15m': {'direction': random.choice(['bullish', 'bearish', 'neutral']), 'strength': random.uniform(0, 1)}
                    },
                    'alignment_percentage': random.uniform(30, 90),
                    'overall_trend': random.choice(['bullish', 'bearish', 'neutral']),
                    'trend_strength': random.uniform(0, 1)
                },
                'signal_scoring': {
                    'individual_scores': {
                        'macd': random.uniform(-0.5, 0.5),
                        'order_book': random.uniform(-0.3, 0.3),
                        'volume': random.uniform(-0.4, 0.4),
                        'price_action': random.uniform(-0.3, 0.3),
                        'trend': random.uniform(-0.5, 0.5)
                    },
                    'total_score': random.uniform(-0.5, 0.5),
                    'confidence': random.uniform(40, 85),
                    'alignment_percentage': random.uniform(40, 80),
                    'signal_strength': random.choice(['weak', 'moderate', 'strong'])
                },
                'market_regime': {
                    'current_regime': random.choice(['low_volatility', 'high_volatility', 'trending', 'ranging']),
                    'volatility': random.uniform(0.01, 0.08),
                    'trend_strength': random.uniform(0, 1),
                    'adjustments': {
                        'leverage_factor': random.uniform(0.5, 1.5),
                        'position_size_factor': random.uniform(0.7, 1.3),
                        'stop_loss_factor': random.uniform(0.8, 1.2),
                        'take_profit_factor': random.uniform(0.8, 1.2),
                        'entry_confidence': random.uniform(0.4, 0.8)
                    }
                },
                'ai_analysis': {
                    'decision': random.choice(['LONG', 'SHORT', 'WAIT']),
                    'confidence': random.uniform(50, 90),
                    'reasoning': self._generate_sample_reasoning(),
                    'take_profit': random.uniform(1, 3),
                    'stop_loss': random.uniform(0.5, 2),
                    'model_info': 'Phi-3.1-Mini via LMStudio',
                    'analysis_timestamp': datetime.now().isoformat()
                },
                'risk_management': {
                    'adaptive_stop_loss': random.uniform(0.5, 2),
                    'adaptive_take_profit': random.uniform(1, 3),
                    'position_size': random.uniform(10, 100),
                    'atr_volatility': random.uniform(0.01, 0.05),
                    'risk_score': random.uniform(1, 8),
                    'max_position_size': 100.0
                }
            }
            
            # Update data file
            self._update_data_file(sample_data)
            
            # Simulate analysis completion
            self.last_analysis_time = datetime.now()
            self.analysis_count += 1
            
        except Exception as e:
            logger.error(f"Error updating sample data: {e}")
    
    def _generate_sample_reasoning(self):
        """Generate sample AI reasoning text"""
        reasonings = [
            "Market shows strong bullish momentum with increasing volume and positive MACD divergence. Order book indicates buying pressure.",
            "Bearish signals detected with declining volume and negative price action. Risk management suggests reducing exposure.",
            "Mixed signals across timeframes. Market regime indicates low volatility environment. Waiting for clearer direction.",
            "Strong trend confirmation across multiple timeframes. High confidence in directional move with favorable risk-reward ratio.",
            "Market consolidation phase detected. Range-bound trading with neutral sentiment. Position sizing reduced accordingly."
        ]
        return random.choice(reasonings)
    
    def _check_trading_system_running(self):
        """Check if trading system is running (simplified)"""
        # In real implementation, this would check for actual trading system process
        return True
    
    def _calculate_next_analysis_time(self):
        """Calculate next analysis time"""
        if self.last_analysis_time:
            next_time = self.last_analysis_time + timedelta(minutes=2)  # Assume 2-minute intervals
            return next_time.strftime('%H:%M:%S')
        return '--'
    
    def _update_data_file(self, new_data):
        """Update the main data file"""
        try:
            # Read existing data
            existing_data = {}
            if self.data_file.exists():
                with open(self.data_file, 'r') as f:
                    existing_data = json.load(f)
            
            # Merge new data
            existing_data.update(new_data)
            
            # Write updated data
            with open(self.data_file, 'w') as f:
                json.dump(existing_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error updating data file: {e}")


def main():
    """Main function to run the GUI integration"""
    print("🔗 Starting Epinnox GUI Integration...")
    
    integration = TradingSystemGUIIntegration()
    integration.start_monitoring()
    
    try:
        print("✅ GUI Integration running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping GUI Integration...")
        integration.stop_monitoring()
        print("✅ GUI Integration stopped.")


if __name__ == "__main__":
    main()
