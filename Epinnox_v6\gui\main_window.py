"""
Main Trading System GUI Window
Comprehensive interface displaying all trading analysis and system status
"""
import sys
import logging
from datetime import datetime
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QFrame, QTabWidget, QScrollArea, QSplitter,
    QApplication, QStatusBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

from .components.market_data_panel import MarketDataPanel
from .components.timeframe_analysis_panel import TimeframeAnalysisPanel
from .components.signal_scoring_panel import SignalScoringPanel
from .components.market_regime_panel import MarketRegimePanel
from .components.ai_analysis_panel import AIAnalysisPanel
from .components.risk_management_panel import RiskManagementPanel
from .components.system_status_panel import SystemStatusPanel
from .components.performance_panel import PerformancePanel
from .data_manager import GUIDataManager

logger = logging.getLogger(__name__)

class TradingSystemGUI(QMainWindow):
    """
    Main GUI window for the Epinnox Trading System
    Displays comprehensive trading analysis and system status
    """
    
    # Signals for data updates
    data_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.data_manager = GUIDataManager()
        self.update_timer = QTimer()

        self.init_ui()
        self.setup_connections()
        self.apply_dark_theme()

        # Start data updates
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second

        logger.info("Trading System GUI initialized")
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Epinnox Trading System - Live Analysis Dashboard")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create header
        self.create_header(main_layout)
        
        # Create main content area with splitter
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Market data and analysis
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - System status and performance
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions (70% left, 30% right)
        splitter.setSizes([1120, 480])
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self, layout):
        """Create the header section"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_frame.setMaximumHeight(80)
        header_layout = QHBoxLayout(header_frame)
        
        # Title and logo
        title_label = QLabel("🚀 EPINNOX TRADING SYSTEM")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #00ff88; padding: 10px;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        self.connection_status.setFont(QFont("Arial", 12))
        header_layout.addWidget(self.connection_status)
        
        # Current symbol
        self.current_symbol = QLabel("Symbol: --")
        self.current_symbol.setFont(QFont("Arial", 12))
        header_layout.addWidget(self.current_symbol)
        
        # Last update time
        self.last_update = QLabel("Last Update: --")
        self.last_update.setFont(QFont("Arial", 10))
        header_layout.addWidget(self.last_update)
        
        layout.addWidget(header_frame)
    
    def create_left_panel(self):
        """Create the left panel with market analysis"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Create tab widget for different analysis sections
        tab_widget = QTabWidget()
        
        # Market Data Tab
        market_tab = QScrollArea()
        market_content = QWidget()
        market_layout = QVBoxLayout(market_content)
        
        self.market_data_panel = MarketDataPanel()
        self.timeframe_panel = TimeframeAnalysisPanel()
        
        market_layout.addWidget(self.market_data_panel)
        market_layout.addWidget(self.timeframe_panel)
        market_layout.addStretch()
        
        market_tab.setWidget(market_content)
        market_tab.setWidgetResizable(True)
        tab_widget.addTab(market_tab, "📊 Market Data")
        
        # Signal Analysis Tab
        signal_tab = QScrollArea()
        signal_content = QWidget()
        signal_layout = QVBoxLayout(signal_content)
        
        self.signal_panel = SignalScoringPanel()
        self.regime_panel = MarketRegimePanel()
        
        signal_layout.addWidget(self.signal_panel)
        signal_layout.addWidget(self.regime_panel)
        signal_layout.addStretch()
        
        signal_tab.setWidget(signal_content)
        signal_tab.setWidgetResizable(True)
        tab_widget.addTab(signal_tab, "🎯 Signal Analysis")
        
        # AI Analysis Tab
        ai_tab = QScrollArea()
        ai_content = QWidget()
        ai_layout = QVBoxLayout(ai_content)
        
        self.ai_panel = AIAnalysisPanel()
        self.risk_panel = RiskManagementPanel()
        
        ai_layout.addWidget(self.ai_panel)
        ai_layout.addWidget(self.risk_panel)
        ai_layout.addStretch()
        
        ai_tab.setWidget(ai_content)
        ai_tab.setWidgetResizable(True)
        tab_widget.addTab(ai_tab, "🤖 AI Analysis")
        
        left_layout.addWidget(tab_widget)
        return left_widget
    
    def create_right_panel(self):
        """Create the right panel with system status"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # System Status Panel
        self.status_panel = SystemStatusPanel()
        right_layout.addWidget(self.status_panel)
        
        # Performance Panel
        self.performance_panel = PerformancePanel()
        right_layout.addWidget(self.performance_panel)
        
        return right_widget
    
    def create_status_bar(self):
        """Create the status bar"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # Add permanent widgets to status bar
        self.status_label = QLabel("Ready")
        status_bar.addWidget(self.status_label)
        
        status_bar.addPermanentWidget(QLabel("Epinnox v2.0"))
    
    def setup_connections(self):
        """Setup signal connections"""
        self.data_updated.connect(self.on_data_updated)
    
    def update_display(self):
        """Update the display with latest data"""
        try:
            # Get latest data from the data manager
            data = self.data_manager.get_latest_data()
            
            if data:
                self.data_updated.emit(data)
        except Exception as e:
            logger.error(f"Error updating display: {e}")
    
    def on_data_updated(self, data):
        """Handle data updates"""
        try:
            # Update header information
            self.update_header(data)

            # Update all panels
            self.market_data_panel.update_data(data.get('market_data', {}))
            self.timeframe_panel.update_data(data.get('timeframe_analysis', {}))
            self.signal_panel.update_data(data.get('signal_scoring', {}))
            self.regime_panel.update_data(data.get('market_regime', {}))
            self.ai_panel.update_data(data.get('ai_analysis', {}))
            self.risk_panel.update_data(data.get('risk_management', {}))
            self.status_panel.update_data(data.get('system_status', {}))
            self.performance_panel.update_data(data.get('performance', {}))

        except Exception as e:
            logger.error(f"Error handling data update: {e}")
    
    def update_header(self, data):
        """Update header information"""
        # Connection status
        if data.get('system_status', {}).get('connected', False):
            self.connection_status.setText("🟢 Connected")
            self.connection_status.setStyleSheet("color: #00ff88;")
        else:
            self.connection_status.setText("🔴 Disconnected")
            self.connection_status.setStyleSheet("color: #ff4444;")
        
        # Current symbol
        symbol = data.get('market_data', {}).get('symbol', '--')
        self.current_symbol.setText(f"Symbol: {symbol}")
        
        # Last update
        timestamp = data.get('timestamp', datetime.now())
        if isinstance(timestamp, str):
            self.last_update.setText(f"Last Update: {timestamp}")
        else:
            self.last_update.setText(f"Last Update: {timestamp.strftime('%H:%M:%S')}")
    
    def apply_dark_theme(self):
        """Apply dark theme to the application"""
        dark_palette = QPalette()
        
        # Window colors
        dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        
        # Base colors
        dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        
        # Text colors
        dark_palette.setColor(QPalette.Text, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        
        # Button colors
        dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        
        # Highlight colors
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        
        self.setPalette(dark_palette)
        
        # Additional stylesheet for specific components
        self.setStyleSheet("""
            QMainWindow {
                background-color: #353535;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #353535;
            }
            QTabBar::tab {
                background-color: #555555;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #2a82da;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def closeEvent(self, event):
        """Handle window close event"""
        logger.info("Trading System GUI closing")
        self.update_timer.stop()
        event.accept()


def main():
    """Main function to run the GUI"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Epinnox Trading System")
    app.setApplicationVersion("2.0.0")
    
    # Create and show main window
    window = TradingSystemGUI()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
