"""
Professional Trading System GUI Window
Modern, resizable, and customizable interface with professional styling
"""
import sys
import logging
from datetime import datetime
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QTabWidget, QApplication, QStatusBar,
    QDockWidget, QPushButton, QComboBox, QCheckBox, QSpinBox, QSlider,
    QSizePolicy, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSettings, QSize
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap

from .components.market_data_panel import MarketDataPanel
from .components.timeframe_analysis_panel import TimeframeAnalysisPanel
from .components.signal_scoring_panel import SignalScoringPanel
from .components.market_regime_panel import MarketRegimePanel
from .components.ai_analysis_panel import AIAnalysisPanel
from .components.risk_management_panel import RiskManagementPanel
from .components.system_status_panel import SystemStatusPanel
from .components.performance_panel import PerformancePanel
from .components.live_chart_widget import LiveChartWidget
from .data_manager import GUIDataManager

logger = logging.getLogger(__name__)

class TradingSystemGUI(QMainWindow):
    """
    Professional Trading System GUI with resizable and moveable components
    Modern interface with dockable panels and customizable layouts
    """

    # Signals for data updates
    data_updated = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.data_manager = GUIDataManager()
        self.update_timer = QTimer()
        self.settings = QSettings("Epinnox", "TradingSystemGUI")

        # Panel references for easy access
        self.dock_widgets = {}
        self.panels = {}

        self.init_ui()
        self.create_dockable_panels()
        self.setup_connections()
        # Set Matrix as default theme
        self.current_theme = "matrix"
        self.apply_matrix_theme()
        self.restore_layout()

        # Start data updates
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second

        logger.info("Professional Trading System GUI initialized")
    
    def init_ui(self):
        """Initialize the modern, professional user interface"""
        self.setWindowTitle("🚀 Epinnox Trading System - Professional Dashboard")
        self.setGeometry(100, 100, 1920, 1080)
        self.setMinimumSize(1200, 800)

        # Enable docking features
        self.setDockNestingEnabled(True)
        self.setTabPosition(Qt.AllDockWidgetAreas, QTabWidget.North)

        # Create central widget with main chart/analysis area
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create central layout
        central_layout = QVBoxLayout(central_widget)
        central_layout.setContentsMargins(5, 5, 5, 5)
        central_layout.setSpacing(5)

        # Create header toolbar
        self.create_header_toolbar()

        # Create main content area
        self.create_main_content_area(central_layout)

        # Create status bar
        self.create_status_bar()

    def create_header_toolbar(self):
        """Create top navigation bar with critical controls"""
        toolbar = self.addToolBar("Navigation")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # Main navigation widget
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(10, 5, 10, 5)
        nav_layout.setSpacing(5)

        # Top row - Title and Session State
        top_row = QHBoxLayout()

        # Title section (compact)
        title_label = QLabel("🔱 Epinnox v6")
        title_label.setFont(QFont("Courier New", 14, QFont.Bold))
        title_label.setStyleSheet("color: #00FF00; margin-right: 20px;")
        top_row.addWidget(title_label)

        # Session State indicators
        session_frame = QFrame()
        session_layout = QHBoxLayout(session_frame)
        session_layout.setContentsMargins(0, 0, 0, 0)
        session_layout.setSpacing(15)

        # Live/Sim indicator
        self.mode_indicator = QLabel("🔴 SIMULATION")
        self.mode_indicator.setStyleSheet("color: #FFFF00; font-weight: bold; font-family: 'Courier New';")
        session_layout.addWidget(self.mode_indicator)

        # Connection status
        self.connection_status = QLabel("🟢 CONNECTED")
        self.connection_status.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New';")
        session_layout.addWidget(self.connection_status)

        # Active symbol
        self.current_symbol = QLabel("📊 DOGE/USDT")
        self.current_symbol.setStyleSheet("color: #00FFFF; font-weight: bold; font-family: 'Courier New';")
        session_layout.addWidget(self.current_symbol)

        top_row.addWidget(session_frame)
        top_row.addStretch()

        # Model indicator
        self.model_indicator = QLabel("🤖 Phi-3.1-Mini")
        self.model_indicator.setStyleSheet("color: #FF00FF; font-weight: bold; font-family: 'Courier New';")
        top_row.addWidget(self.model_indicator)

        nav_layout.addLayout(top_row)

        # Bottom row - Controls
        bottom_row = QHBoxLayout()

        # Symbol selector
        bottom_row.addWidget(QLabel("Symbol:"))
        self.symbol_selector = QComboBox()
        self.symbol_selector.addItems(["DOGE/USDT", "BTC/USDT", "ETH/USDT", "SOL/USDT", "ADA/USDT"])
        self.symbol_selector.setCurrentText("DOGE/USDT")
        self.symbol_selector.setStyleSheet("font-family: 'Courier New'; background-color: #002200; color: #00FF00; border: 1px solid #00AA00;")
        bottom_row.addWidget(self.symbol_selector)

        bottom_row.addWidget(QLabel("  "))  # Spacer

        # Live toggle
        self.live_toggle = QCheckBox("Live Data")
        self.live_toggle.setStyleSheet("color: #00FF00; font-family: 'Courier New';")
        bottom_row.addWidget(self.live_toggle)

        # Auto-scroll
        self.auto_scroll_toggle = QCheckBox("Auto-scroll")
        self.auto_scroll_toggle.setChecked(True)
        self.auto_scroll_toggle.setStyleSheet("color: #00FF00; font-family: 'Courier New';")
        bottom_row.addWidget(self.auto_scroll_toggle)

        bottom_row.addWidget(QLabel("  "))  # Spacer

        # Timeframe selector
        bottom_row.addWidget(QLabel("Timeframe:"))
        self.timeframe_selector = QComboBox()
        self.timeframe_selector.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_selector.setCurrentText("5m")
        self.timeframe_selector.setStyleSheet("font-family: 'Courier New'; background-color: #002200; color: #00FF00; border: 1px solid #00AA00;")
        bottom_row.addWidget(self.timeframe_selector)

        # Candle count
        bottom_row.addWidget(QLabel("Candles:"))
        self.candle_count_spin = QSpinBox()
        self.candle_count_spin.setRange(50, 500)
        self.candle_count_spin.setValue(100)
        self.candle_count_spin.setStyleSheet("font-family: 'Courier New'; background-color: #002200; color: #00FF00; border: 1px solid #00AA00;")
        bottom_row.addWidget(self.candle_count_spin)

        bottom_row.addStretch()

        # Theme switcher
        bottom_row.addWidget(QLabel("Theme:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Matrix", "Dark Professional", "Light", "High Contrast"])
        self.theme_combo.setCurrentText("Matrix")
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        self.theme_combo.setStyleSheet("font-family: 'Courier New'; background-color: #002200; color: #00FF00; border: 1px solid #00AA00;")
        bottom_row.addWidget(self.theme_combo)

        # Reset layout button
        reset_btn = QPushButton("🔄 Reset")
        reset_btn.clicked.connect(self.reset_layout)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 4px 8px;
                border-radius: 3px;
                font-weight: bold;
                font-family: 'Courier New';
            }
            QPushButton:hover {
                background-color: #004400;
            }
        """)
        bottom_row.addWidget(reset_btn)

        # Last update
        self.last_update = QLabel("Last Update: --")
        self.last_update.setFont(QFont("Courier New", 8))
        self.last_update.setStyleSheet("color: #888888; padding: 0 10px;")
        bottom_row.addWidget(self.last_update)

        nav_layout.addLayout(bottom_row)
        toolbar.addWidget(nav_widget)

    def create_main_content_area(self, layout):
        """Create the main content area with live OHLCV chart and compact market info"""
        # Create main chart container
        chart_container = QWidget()
        chart_layout = QVBoxLayout(chart_container)
        chart_layout.setContentsMargins(0, 0, 0, 0)
        chart_layout.setSpacing(0)

        # PROFESSIONAL CHART CONTROLS (OPTIMIZED LAYOUT)
        controls_frame = QFrame()
        controls_frame.setFixedHeight(75)
        controls_frame.setStyleSheet("""
            QFrame {
                background-color: #001100;
                border-bottom: 2px solid #00AA00;
                border-top: 1px solid #003300;
            }
        """)
        controls_layout = QGridLayout(controls_frame)
        controls_layout.setContentsMargins(20, 10, 20, 10)
        controls_layout.setHorizontalSpacing(25)
        controls_layout.setVerticalSpacing(8)

        # Row 1: Chart Configuration
        row = 0

        # Timeframe Group
        tf_group = QFrame()
        tf_layout = QHBoxLayout(tf_group)
        tf_layout.setContentsMargins(0, 0, 0, 0)
        tf_layout.setSpacing(5)

        tf_label = QLabel("Timeframe:")
        tf_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 13px; font-weight: bold;")
        tf_layout.addWidget(tf_label)

        self.chart_timeframe_combo = QComboBox()
        self.chart_timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.chart_timeframe_combo.setCurrentText("5m")
        self.chart_timeframe_combo.setStyleSheet("""
            QComboBox {
                font-family: 'Courier New';
                background-color: #002200;
                color: #00FF00;
                border: 1px solid #00AA00;
                font-size: 13px;
                padding: 4px 8px;
                min-width: 60px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #003300;
            }
            QComboBox::down-arrow {
                border: 2px solid #00AA00;
                border-radius: 2px;
            }
        """)
        tf_layout.addWidget(self.chart_timeframe_combo)
        controls_layout.addWidget(tf_group, row, 0)

        # Candles Group
        candles_group = QFrame()
        candles_layout = QHBoxLayout(candles_group)
        candles_layout.setContentsMargins(0, 0, 0, 0)
        candles_layout.setSpacing(5)

        candles_label = QLabel("Candles:")
        candles_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        candles_layout.addWidget(candles_label)

        self.chart_candle_count = QSpinBox()
        self.chart_candle_count.setRange(50, 500)
        self.chart_candle_count.setValue(100)
        self.chart_candle_count.setStyleSheet("""
            QSpinBox {
                font-family: 'Courier New';
                background-color: #002200;
                color: #00FF00;
                border: 1px solid #00AA00;
                font-size: 12px;
                padding: 4px;
                min-width: 70px;
            }
        """)
        candles_layout.addWidget(self.chart_candle_count)
        controls_layout.addWidget(candles_group, row, 1)

        # Overlays Group
        overlays_group = QFrame()
        overlays_layout = QHBoxLayout(overlays_group)
        overlays_layout.setContentsMargins(0, 0, 0, 0)
        overlays_layout.setSpacing(5)

        overlays_label = QLabel("Overlays:")
        overlays_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        overlays_layout.addWidget(overlays_label)

        self.overlays_combo = QComboBox()
        self.overlays_combo.addItems(["None", "Liquidity Zones", "Support/Resistance", "Fibonacci"])
        self.overlays_combo.setStyleSheet("""
            QComboBox {
                font-family: 'Courier New';
                background-color: #002200;
                color: #00FF00;
                border: 1px solid #00AA00;
                font-size: 12px;
                padding: 4px 8px;
                min-width: 120px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #003300;
            }
        """)
        overlays_layout.addWidget(self.overlays_combo)
        controls_layout.addWidget(overlays_group, row, 2)

        # Chart Options (Auto-scroll, Refresh)
        options_group = QFrame()
        options_layout = QHBoxLayout(options_group)
        options_layout.setContentsMargins(0, 0, 0, 0)
        options_layout.setSpacing(10)

        self.chart_auto_scroll = QCheckBox("Auto-scroll")
        self.chart_auto_scroll.setChecked(True)
        self.chart_auto_scroll.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        options_layout.addWidget(self.chart_auto_scroll)

        refresh_btn = QPushButton("🔄")
        refresh_btn.setMaximumWidth(30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 4px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #004400;
            }
        """)
        options_layout.addWidget(refresh_btn)
        controls_layout.addWidget(options_group, row, 3)

        # Row 2: Indicators
        row = 1

        # Indicators Group
        indicators_group = QFrame()
        indicators_layout = QHBoxLayout(indicators_group)
        indicators_layout.setContentsMargins(0, 0, 0, 0)
        indicators_layout.setSpacing(15)

        indicators_label = QLabel("Indicators:")
        indicators_label.setStyleSheet("color: #FFFFFF; font-family: 'Courier New'; font-size: 12px; font-weight: bold;")
        indicators_layout.addWidget(indicators_label)

        # Professional indicator toggles
        indicators = ["RSI", "MACD", "EMA", "Bollinger", "Volume", "Signals", "SL/TP"]
        for indicator in indicators:
            indicator_check = QCheckBox(indicator)
            indicator_check.setStyleSheet("""
                QCheckBox {
                    color: #00FF00;
                    font-family: 'Courier New';
                    font-size: 12px;
                    font-weight: bold;
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    border: 1px solid #00AA00;
                    background-color: #002200;
                }
                QCheckBox::indicator:checked {
                    background-color: #00AA00;
                    border: 1px solid #00FF00;
                }
            """)
            if indicator in ["RSI", "MACD", "Volume"]:  # Default enabled
                indicator_check.setChecked(True)
            indicators_layout.addWidget(indicator_check)

        controls_layout.addWidget(indicators_group, row, 0, 1, 4)  # Span all columns

        chart_layout.addWidget(controls_frame)

        # PROFESSIONAL CHART SPLITTER IMPLEMENTATION
        # Create vertical splitter for chart and market info
        chart_splitter = QSplitter(Qt.Vertical)
        chart_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #00AA00;
                height: 3px;
            }
            QSplitter::handle:hover {
                background-color: #00FF00;
            }
        """)

        # Create live chart widget with proper size policy
        self.live_chart = LiveChartWidget()
        self.live_chart.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.live_chart.setMinimumHeight(300)  # Ensure minimum chart height
        chart_splitter.addWidget(self.live_chart)

        # Compact market info panel with collapsible functionality
        from gui.components.reorganized_panels import CompactMarketInfoPanel
        market_info_container = QFrame()
        market_info_container.setStyleSheet("background-color: #001100; border-top: 1px solid #00AA00;")
        market_info_layout = QVBoxLayout(market_info_container)
        market_info_layout.setContentsMargins(0, 0, 0, 0)

        # Collapsible header for market info
        info_header = QFrame()
        info_header.setMaximumHeight(25)
        info_header.setStyleSheet("background-color: #002200; border-bottom: 1px solid #00AA00;")
        info_header_layout = QHBoxLayout(info_header)
        info_header_layout.setContentsMargins(10, 2, 10, 2)

        info_title = QLabel("📊 Market Info")
        info_title.setStyleSheet("color: #00FF00; font-family: 'Courier New'; font-size: 11px; font-weight: bold;")
        info_header_layout.addWidget(info_title)

        info_header_layout.addStretch()

        # Collapse/expand button
        self.info_collapse_btn = QPushButton("▼")
        self.info_collapse_btn.setMaximumWidth(20)
        self.info_collapse_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #00FF00;
                border: none;
                font-family: 'Courier New';
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                color: #00FFFF;
            }
        """)
        self.info_collapse_btn.clicked.connect(self.toggle_market_info)
        info_header_layout.addWidget(self.info_collapse_btn)

        market_info_layout.addWidget(info_header)

        # Market info panel
        self.market_info_panel = CompactMarketInfoPanel()
        self.market_info_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        market_info_layout.addWidget(self.market_info_panel)

        chart_splitter.addWidget(market_info_container)

        # Set splitter proportions (80% chart, 20% market info)
        chart_splitter.setSizes([800, 200])
        chart_splitter.setCollapsible(0, False)  # Chart cannot be collapsed
        chart_splitter.setCollapsible(1, True)   # Market info can be collapsed

        chart_layout.addWidget(chart_splitter)

        # Add chart container to main layout
        layout.addWidget(chart_container)

    def toggle_market_info(self):
        """Toggle market info panel visibility"""
        if self.market_info_panel.isVisible():
            self.market_info_panel.hide()
            self.info_collapse_btn.setText("▲")
        else:
            self.market_info_panel.show()
            self.info_collapse_btn.setText("▼")

    def create_dockable_panels(self):
        """Create reorganized dockable panels following UX strategy"""
        # Import reorganized panels
        from gui.components.reorganized_panels import (
            CompactMarketInfoPanel, TrendSystemPanel, SignalScoringCompactPanel,
            MarketIntelligencePanel, RiskExecutionPanel, LogAnalyticsPanel
        )

        # Left Column - Signals + Analysis
        self.create_dock_panel("Trend System", "✳️", TrendSystemPanel(), Qt.LeftDockWidgetArea)
        self.create_dock_panel("Signal Scoring", "✅", SignalScoringCompactPanel(), Qt.LeftDockWidgetArea)

        # Right Column - Market Intelligence
        self.create_dock_panel("Market Intelligence", "🧩", MarketIntelligencePanel(), Qt.RightDockWidgetArea)

        # Bottom - Risk & Execution
        self.create_dock_panel("Risk & Execution", "💼", RiskExecutionPanel(), Qt.BottomDockWidgetArea)

        # Optional - Logs & Analytics (can be hidden by default)
        self.create_dock_panel("Logs & Analytics", "📚", LogAnalyticsPanel(), Qt.BottomDockWidgetArea)

        # Organize panels in the new layout
        self.organize_reorganized_dock_tabs()

    def create_dock_panel(self, title, icon, panel_widget, area):
        """Create a dockable panel"""
        dock = QDockWidget(f"{icon} {title}", self)
        dock.setWidget(panel_widget)
        dock.setAllowedAreas(Qt.AllDockWidgetAreas)
        dock.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable | QDockWidget.DockWidgetClosable)

        # Store references
        self.dock_widgets[title] = dock
        self.panels[title] = panel_widget

        # Add to main window
        self.addDockWidget(area, dock)

        return dock

    def organize_reorganized_dock_tabs(self):
        """Organize dock widgets following the new UX strategy"""
        # Left Column - Signals + Analysis (tabbed)
        if "Trend System" in self.dock_widgets and "Signal Scoring" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["Trend System"], self.dock_widgets["Signal Scoring"])

        # Bottom - Risk & Execution with Logs (tabbed)
        if "Risk & Execution" in self.dock_widgets and "Logs & Analytics" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["Risk & Execution"], self.dock_widgets["Logs & Analytics"])

        # Ensure primary tabs are active by default
        if "Trend System" in self.dock_widgets:
            self.dock_widgets["Trend System"].raise_()
        if "Market Intelligence" in self.dock_widgets:
            self.dock_widgets["Market Intelligence"].raise_()
        if "Risk & Execution" in self.dock_widgets:
            self.dock_widgets["Risk & Execution"].raise_()

        # Hide logs panel by default (can be opened when needed)
        if "Logs & Analytics" in self.dock_widgets:
            self.dock_widgets["Logs & Analytics"].hide()


    def apply_professional_theme(self):
        """Apply professional dark theme with modern styling"""
        # Set professional color palette
        palette = QPalette()

        # Main colors - Professional dark theme
        palette.setColor(QPalette.Window, QColor(35, 35, 35))           # Main background
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))    # Main text
        palette.setColor(QPalette.Base, QColor(25, 25, 25))             # Input backgrounds
        palette.setColor(QPalette.AlternateBase, QColor(45, 45, 45))    # Alternate rows
        palette.setColor(QPalette.Text, QColor(255, 255, 255))          # Input text
        palette.setColor(QPalette.Button, QColor(55, 55, 55))           # Button background
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))    # Button text
        palette.setColor(QPalette.Highlight, QColor(0, 212, 170))       # Selection color
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))     # Selected text

        self.setPalette(palette)

        # Professional stylesheet
        self.setStyleSheet("""
            /* Main Window */
            QMainWindow {
                background-color: #232323;
                color: #FFFFFF;
            }

            /* Dock Widgets */
            QDockWidget {
                background-color: #2B2B2B;
                border: 1px solid #555555;
                titlebar-close-icon: url(close.png);
                titlebar-normal-icon: url(float.png);
            }

            QDockWidget::title {
                background-color: #3C3C3C;
                color: #FFFFFF;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
                font-size: 11px;
            }

            QDockWidget::close-button, QDockWidget::float-button {
                background-color: #555555;
                border: 1px solid #777777;
                border-radius: 3px;
                width: 16px;
                height: 16px;
            }

            QDockWidget::close-button:hover, QDockWidget::float-button:hover {
                background-color: #777777;
            }

            /* Tab Widgets */
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2B2B2B;
                border-radius: 4px;
            }

            QTabBar::tab {
                background-color: #3C3C3C;
                color: #FFFFFF;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #555555;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
            }

            QTabBar::tab:selected {
                background-color: #00D4AA;
                color: #000000;
            }

            QTabBar::tab:hover {
                background-color: #4A4A4A;
            }

            /* Group Boxes */
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #2B2B2B;
                color: #FFFFFF;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #00D4AA;
                font-size: 12px;
                font-weight: bold;
            }

            /* Labels */
            QLabel {
                color: #FFFFFF;
                background-color: transparent;
            }

            /* Buttons */
            QPushButton {
                background-color: #3C3C3C;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px 16px;
                color: #FFFFFF;
                font-weight: bold;
                min-height: 20px;
            }

            QPushButton:hover {
                background-color: #4A4A4A;
                border-color: #777777;
            }

            QPushButton:pressed {
                background-color: #2A2A2A;
            }

            /* ComboBox */
            QComboBox {
                background-color: #3C3C3C;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 4px 8px;
                color: #FFFFFF;
                min-width: 100px;
            }

            QComboBox:hover {
                border-color: #777777;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }

            /* Scroll Areas */
            QScrollArea {
                background-color: #2B2B2B;
                border: 1px solid #555555;
                border-radius: 4px;
            }

            /* Scroll Bars */
            QScrollBar:vertical {
                background-color: #3C3C3C;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }

            /* Status Bar */
            QStatusBar {
                background-color: #3C3C3C;
                border-top: 1px solid #555555;
                color: #FFFFFF;
            }

            /* Tool Bar */
            QToolBar {
                background-color: #3C3C3C;
                border: 1px solid #555555;
                spacing: 3px;
                padding: 4px;
            }

            /* Progress Bars */
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #2B2B2B;
                color: #FFFFFF;
            }

            QProgressBar::chunk {
                background-color: #00D4AA;
                border-radius: 3px;
            }
        """)

        # Notify all panels to update their theme
        self.notify_panels_theme_change("dark_professional")

    def change_theme(self, theme_name):
        """Change the application theme"""
        self.current_theme = theme_name.lower().replace(" ", "_")

        if theme_name == "Dark Professional":
            self.apply_professional_theme()
        elif theme_name == "Light":
            self.apply_light_theme()
        elif theme_name == "Matrix":
            self.apply_matrix_theme()
        elif theme_name == "High Contrast":
            self.apply_high_contrast_theme()

        # Save theme preference
        self.settings.setValue("theme", theme_name)

    def apply_light_theme(self):
        """Apply light theme"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(225, 225, 225))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.Highlight, QColor(0, 120, 215))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        self.setPalette(palette)
        self.setStyleSheet("")  # Reset to default

        # Update header colors for Light theme
        self.connection_status.setStyleSheet("color: #007ACC; font-weight: bold;")
        self.current_symbol.setStyleSheet("color: #D83B01; padding: 0 10px; font-weight: bold;")
        self.last_update.setStyleSheet("color: #666666; padding: 0 10px;")

        # Notify all panels to update their theme
        self.notify_panels_theme_change("light")

    def apply_matrix_theme(self):
        """Apply Matrix-style green theme"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(0, 0, 0))
        palette.setColor(QPalette.WindowText, QColor(0, 255, 0))
        palette.setColor(QPalette.Base, QColor(5, 15, 5))
        palette.setColor(QPalette.AlternateBase, QColor(10, 25, 10))
        palette.setColor(QPalette.Text, QColor(0, 255, 0))
        palette.setColor(QPalette.Button, QColor(15, 30, 15))
        palette.setColor(QPalette.ButtonText, QColor(0, 255, 0))
        palette.setColor(QPalette.Highlight, QColor(0, 200, 0))
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        self.setPalette(palette)

        # Matrix-specific stylesheet
        self.setStyleSheet("""
            /* Main Window */
            QMainWindow {
                background-color: #000000;
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }

            /* Dock Widgets */
            QDockWidget {
                background-color: #001100;
                border: 1px solid #00AA00;
                color: #00FF00;
            }

            QDockWidget::title {
                background-color: #002200;
                color: #00FF00;
                padding: 8px;
                border: 1px solid #00AA00;
                font-weight: bold;
                font-size: 11px;
                font-family: 'Courier New', monospace;
            }

            QDockWidget::close-button, QDockWidget::float-button {
                background-color: #003300;
                border: 1px solid #00AA00;
                border-radius: 3px;
                width: 16px;
                height: 16px;
            }

            QDockWidget::close-button:hover, QDockWidget::float-button:hover {
                background-color: #004400;
            }

            /* Tab Widgets */
            QTabWidget::pane {
                border: 1px solid #00AA00;
                background-color: #001100;
                border-radius: 4px;
            }

            QTabBar::tab {
                background-color: #002200;
                color: #00FF00;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #00AA00;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
            }

            QTabBar::tab:selected {
                background-color: #00AA00;
                color: #000000;
            }

            QTabBar::tab:hover {
                background-color: #003300;
            }

            /* Group Boxes */
            QGroupBox {
                font-weight: bold;
                border: 2px solid #00AA00;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #001100;
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #00FF00;
                font-size: 12px;
                font-weight: bold;
            }

            /* Labels */
            QLabel {
                color: #00FF00;
                background-color: transparent;
                font-family: 'Courier New', monospace;
            }

            /* Buttons */
            QPushButton {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px 16px;
                color: #00FF00;
                font-weight: bold;
                min-height: 20px;
                font-family: 'Courier New', monospace;
            }

            QPushButton:hover {
                background-color: #003300;
                border-color: #00CC00;
            }

            QPushButton:pressed {
                background-color: #001100;
            }

            /* ComboBox */
            QComboBox {
                background-color: #002200;
                border: 1px solid #00AA00;
                border-radius: 4px;
                padding: 4px 8px;
                color: #00FF00;
                min-width: 100px;
                font-family: 'Courier New', monospace;
            }

            QComboBox:hover {
                border-color: #00CC00;
            }

            /* Text Edit */
            QTextEdit {
                background-color: #000000;
                color: #00FF00;
                border: 1px solid #00AA00;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                line-height: 1.4;
            }

            /* Progress Bars */
            QProgressBar {
                border: 1px solid #00AA00;
                border-radius: 4px;
                text-align: center;
                background-color: #001100;
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }

            QProgressBar::chunk {
                background-color: #00AA00;
                border-radius: 3px;
            }

            /* Status Bar */
            QStatusBar {
                background-color: #002200;
                border-top: 1px solid #00AA00;
                color: #00FF00;
                font-family: 'Courier New', monospace;
            }

            /* Tool Bar */
            QToolBar {
                background-color: #002200;
                border: 1px solid #00AA00;
                spacing: 3px;
                padding: 4px;
            }

            /* Scroll Bars */
            QScrollBar:vertical {
                background-color: #002200;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #00AA00;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #00CC00;
            }

            /* Table Widgets - Matrix Style */
            QTableWidget {
                background-color: #000000;
                color: #00FF00;
                border: 1px solid #00AA00;
                border-radius: 4px;
                gridline-color: #003300;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                selection-background-color: #004400;
            }

            QTableWidget::item {
                background-color: #000000;
                color: #00FF00;
                border: none;
                padding: 4px;
            }

            QTableWidget::item:selected {
                background-color: #004400;
                color: #00FF00;
            }

            QTableWidget::item:hover {
                background-color: #002200;
            }

            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 6px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
            }

            QHeaderView::section:hover {
                background-color: #004400;
            }

            /* Table Widget Items with Matrix Colors */
            QTableWidgetItem {
                background-color: #000000;
                color: #00FF00;
            }
        """)

        # Update header colors for Matrix theme
        self.connection_status.setStyleSheet("color: #00FF00; font-weight: bold; font-family: 'Courier New', monospace;")
        self.current_symbol.setStyleSheet("color: #00FF00; padding: 0 10px; font-family: 'Courier New', monospace;")
        self.last_update.setStyleSheet("color: #00AA00; padding: 0 10px; font-family: 'Courier New', monospace;")

        # Notify all panels to update their theme
        self.notify_panels_theme_change("matrix")

    def notify_panels_theme_change(self, theme_name):
        """Notify all panels about theme change"""
        for panel_name, panel in self.panels.items():
            if hasattr(panel, 'apply_theme'):
                panel.apply_theme(theme_name)

        # Also update the live chart
        if hasattr(self, 'live_chart') and hasattr(self.live_chart, 'apply_theme'):
            self.live_chart.apply_theme(theme_name)

    def reset_layout(self):
        """Reset the layout to default configuration"""
        # Clear all settings
        self.settings.clear()

        # Remove all dock widgets
        for dock in self.dock_widgets.values():
            self.removeDockWidget(dock)

        # Clear references
        self.dock_widgets.clear()
        self.panels.clear()

        # Recreate dockable panels with default layout
        self.create_dockable_panels()

        # Apply current theme to new panels
        if hasattr(self, 'current_theme'):
            self.notify_panels_theme_change(self.current_theme)

        # Show success message in status bar
        self.status_label.setText("🔄 Layout reset to default configuration")

    def apply_high_contrast_theme(self):
        """Apply high contrast theme"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(0, 0, 0))
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        palette.setColor(QPalette.Base, QColor(0, 0, 0))
        palette.setColor(QPalette.AlternateBase, QColor(40, 40, 40))
        palette.setColor(QPalette.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.Button, QColor(80, 80, 80))
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        palette.setColor(QPalette.Highlight, QColor(255, 255, 0))
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        self.setPalette(palette)

        # Update header colors for High Contrast theme
        self.connection_status.setStyleSheet("color: #FFFFFF; font-weight: bold; background-color: #000000; padding: 2px;")
        self.current_symbol.setStyleSheet("color: #FFFF00; padding: 0 10px; font-weight: bold; background-color: #000000;")
        self.last_update.setStyleSheet("color: #FFFFFF; padding: 0 10px; background-color: #000000;")

        # Notify all panels to update their theme
        self.notify_panels_theme_change("high_contrast")

    def save_layout(self):
        """Save the current layout configuration"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())

        # Save dock widget states
        for name, dock in self.dock_widgets.items():
            self.settings.setValue(f"dock_{name}_visible", dock.isVisible())
            self.settings.setValue(f"dock_{name}_floating", dock.isFloating())

    def restore_layout(self):
        """Restore the saved layout configuration"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

        # Restore theme
        saved_theme = self.settings.value("theme", "Dark Professional")
        self.change_theme(saved_theme)

        # Restore dock widget states
        for name, dock in self.dock_widgets.items():
            visible = self.settings.value(f"dock_{name}_visible", True, type=bool)
            floating = self.settings.value(f"dock_{name}_floating", False, type=bool)
            dock.setVisible(visible)
            dock.setFloating(floating)

    def setup_connections(self):
        """Setup signal connections"""
        self.data_updated.connect(self.on_data_updated)

    def update_display(self):
        """Update the display with latest data"""
        try:
            # Get latest data from the data manager
            data = self.data_manager.get_latest_data()

            if data:
                self.data_updated.emit(data)
        except Exception as e:
            logger.error(f"Error updating display: {e}")

    def on_data_updated(self, data):
        """Handle data updates"""
        try:
            # Update header information
            self.update_header(data)

            # Update all panels
            if "Market Data" in self.panels:
                self.panels["Market Data"].update_data(data.get('market_data', {}))
            if "Timeframe Analysis" in self.panels:
                self.panels["Timeframe Analysis"].update_data(data.get('timeframe_analysis', {}))
            if "Signal Scoring" in self.panels:
                self.panels["Signal Scoring"].update_data(data.get('signal_scoring', {}))
            if "Market Regime" in self.panels:
                self.panels["Market Regime"].update_data(data.get('market_regime', {}))
            if "AI Analysis" in self.panels:
                self.panels["AI Analysis"].update_data(data.get('ai_analysis', {}))
            if "Risk Management" in self.panels:
                self.panels["Risk Management"].update_data(data.get('risk_management', {}))
            if "System Status" in self.panels:
                self.panels["System Status"].update_data(data.get('system_status', {}))
            if "Performance" in self.panels:
                self.panels["Performance"].update_data(data.get('performance', {}))

        except Exception as e:
            logger.error(f"Error handling data update: {e}")

    def update_header(self, data):
        """Update header information"""
        # Connection status
        if data.get('system_status', {}).get('connected', True):  # Default to connected for demo
            self.connection_status.setText("🟢 Connected")
            self.connection_status.setStyleSheet("color: #00D4AA; font-weight: bold;")
        else:
            self.connection_status.setText("🔴 Disconnected")
            self.connection_status.setStyleSheet("color: #FF4444; font-weight: bold;")

        # Current symbol
        symbol = data.get('market_data', {}).get('symbol', 'DOGE/USDT')
        self.current_symbol.setText(f"{symbol}")

        # Last update
        timestamp = data.get('timestamp', datetime.now())
        if isinstance(timestamp, str):
            try:
                # Try to parse ISO format
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                self.last_update.setText(f"Last Update: {dt.strftime('%H:%M:%S')}")
            except:
                self.last_update.setText(f"Last Update: {timestamp}")
        else:
            self.last_update.setText(f"Last Update: {timestamp.strftime('%H:%M:%S')}")

    def create_status_bar(self):
        """Create the status bar"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # Add permanent widgets to status bar
        self.status_label = QLabel("🚀 Ready - Professional Trading Interface")
        self.status_label.setStyleSheet("color: #00D4AA; font-weight: bold;")
        status_bar.addWidget(self.status_label)

        status_bar.addPermanentWidget(QLabel("Epinnox v2.0 Professional"))

    def closeEvent(self, event):
        """Handle window close event"""
        logger.info("Professional Trading System GUI closing")

        # Save layout before closing
        self.save_layout()

        # Stop timer
        self.update_timer.stop()

        event.accept()


def main():
    """Main function to run the professional GUI"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Epinnox Trading System Professional")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("Epinnox")
    app.setOrganizationDomain("epinnox.com")

    # Set application icon (if available)
    try:
        app.setWindowIcon(QIcon("icon.png"))
    except:
        pass

    # Create and show main window
    window = TradingSystemGUI()
    window.show()

    # Center window on screen
    screen = app.primaryScreen().geometry()
    window.move((screen.width() - window.width()) // 2,
                (screen.height() - window.height()) // 2)

    logger.info("Professional Trading System GUI started")

    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
