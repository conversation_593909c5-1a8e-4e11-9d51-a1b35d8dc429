"""
Professional Trading System GUI Window
Modern, resizable, and customizable interface with professional styling
"""
import sys
import logging
from datetime import datetime
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QFrame, QTabWidget, QApplication, QStatusBar,
    QDockWidget, QPushButton, QComboBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSettings, QSize
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap

from .components.market_data_panel import MarketDataPanel
from .components.timeframe_analysis_panel import TimeframeAnalysisPanel
from .components.signal_scoring_panel import SignalScoringPanel
from .components.market_regime_panel import MarketRegimePanel
from .components.ai_analysis_panel import AIAnalysisPanel
from .components.risk_management_panel import RiskManagementPanel
from .components.system_status_panel import SystemStatusPanel
from .components.performance_panel import PerformancePanel
from .data_manager import GUIDataManager

logger = logging.getLogger(__name__)

class TradingSystemGUI(QMainWindow):
    """
    Professional Trading System GUI with resizable and moveable components
    Modern interface with dockable panels and customizable layouts
    """

    # Signals for data updates
    data_updated = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.data_manager = GUIDataManager()
        self.update_timer = QTimer()
        self.settings = QSettings("Epinnox", "TradingSystemGUI")

        # Panel references for easy access
        self.dock_widgets = {}
        self.panels = {}

        self.init_ui()
        self.create_dockable_panels()
        self.setup_connections()
        self.apply_professional_theme()
        self.restore_layout()

        # Start data updates
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second

        logger.info("Professional Trading System GUI initialized")
    
    def init_ui(self):
        """Initialize the modern, professional user interface"""
        self.setWindowTitle("🚀 Epinnox Trading System - Professional Dashboard")
        self.setGeometry(100, 100, 1920, 1080)
        self.setMinimumSize(1200, 800)

        # Enable docking features
        self.setDockNestingEnabled(True)
        self.setTabPosition(Qt.AllDockWidgetAreas, QTabWidget.North)

        # Create central widget with main chart/analysis area
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create central layout
        central_layout = QVBoxLayout(central_widget)
        central_layout.setContentsMargins(5, 5, 5, 5)
        central_layout.setSpacing(5)

        # Create header toolbar
        self.create_header_toolbar()

        # Create main content area
        self.create_main_content_area(central_layout)

        # Create status bar
        self.create_status_bar()

    def create_header_toolbar(self):
        """Create modern header toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # Title section
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(10, 5, 10, 5)

        title_label = QLabel("🚀 EPINNOX TRADING SYSTEM")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("color: #00D4AA; padding: 5px;")
        title_layout.addWidget(title_label)

        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        self.connection_status.setFont(QFont("Segoe UI", 10))
        title_layout.addWidget(self.connection_status)

        # Symbol info
        self.current_symbol = QLabel("DOGE/USDT")
        self.current_symbol.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.current_symbol.setStyleSheet("color: #FFD700; padding: 0 10px;")
        title_layout.addWidget(self.current_symbol)

        title_layout.addStretch()

        # Theme selector
        theme_combo = QComboBox()
        theme_combo.addItems(["Dark Professional", "Light", "Matrix", "High Contrast"])
        theme_combo.currentTextChanged.connect(self.change_theme)
        title_layout.addWidget(QLabel("Theme:"))
        title_layout.addWidget(theme_combo)

        # Last update
        self.last_update = QLabel("Last Update: --")
        self.last_update.setFont(QFont("Segoe UI", 9))
        self.last_update.setStyleSheet("color: #888888; padding: 0 10px;")
        title_layout.addWidget(self.last_update)

        toolbar.addWidget(title_widget)

    def create_main_content_area(self, layout):
        """Create the main content area with central chart"""
        # Main content frame
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.StyledPanel)
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #2B2B2B;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 5px;
            }
        """)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # Central analysis display
        central_label = QLabel("📊 Main Analysis Dashboard")
        central_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        central_label.setAlignment(Qt.AlignCenter)
        central_label.setStyleSheet("color: #FFFFFF; padding: 20px;")
        content_layout.addWidget(central_label)

        # Placeholder for main chart/analysis
        chart_placeholder = QLabel("🔄 Live Trading Analysis\n\n📈 Charts and real-time data will be displayed here")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("""
            QLabel {
                background-color: #1E1E1E;
                border: 2px dashed #555555;
                border-radius: 8px;
                color: #CCCCCC;
                font-size: 14px;
                padding: 40px;
            }
        """)
        content_layout.addWidget(chart_placeholder)

        layout.addWidget(content_frame)

    def create_dockable_panels(self):
        """Create all dockable panels for the interface"""
        # Market Data Panel
        self.create_dock_panel("Market Data", "📊", MarketDataPanel(), Qt.LeftDockWidgetArea)

        # Timeframe Analysis Panel
        self.create_dock_panel("Timeframe Analysis", "📈", TimeframeAnalysisPanel(), Qt.LeftDockWidgetArea)

        # Signal Scoring Panel
        self.create_dock_panel("Signal Scoring", "🎯", SignalScoringPanel(), Qt.RightDockWidgetArea)

        # Market Regime Panel
        self.create_dock_panel("Market Regime", "⚡", MarketRegimePanel(), Qt.RightDockWidgetArea)

        # AI Analysis Panel
        self.create_dock_panel("AI Analysis", "🤖", AIAnalysisPanel(), Qt.BottomDockWidgetArea)

        # Risk Management Panel
        self.create_dock_panel("Risk Management", "🛡️", RiskManagementPanel(), Qt.BottomDockWidgetArea)

        # System Status Panel
        self.create_dock_panel("System Status", "⚙️", SystemStatusPanel(), Qt.RightDockWidgetArea)

        # Performance Panel
        self.create_dock_panel("Performance", "📊", PerformancePanel(), Qt.RightDockWidgetArea)

        # Organize panels in tabs
        self.organize_dock_tabs()

    def create_dock_panel(self, title, icon, panel_widget, area):
        """Create a dockable panel"""
        dock = QDockWidget(f"{icon} {title}", self)
        dock.setWidget(panel_widget)
        dock.setAllowedAreas(Qt.AllDockWidgetAreas)
        dock.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable | QDockWidget.DockWidgetClosable)

        # Store references
        self.dock_widgets[title] = dock
        self.panels[title] = panel_widget

        # Add to main window
        self.addDockWidget(area, dock)

        return dock

    def organize_dock_tabs(self):
        """Organize dock widgets into logical tab groups"""
        # Group market analysis panels on the left
        if "Market Data" in self.dock_widgets and "Timeframe Analysis" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["Market Data"], self.dock_widgets["Timeframe Analysis"])

        # Group signal analysis panels on the right
        if "Signal Scoring" in self.dock_widgets and "Market Regime" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["Signal Scoring"], self.dock_widgets["Market Regime"])

        if "System Status" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["Market Regime"], self.dock_widgets["System Status"])

        if "Performance" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["System Status"], self.dock_widgets["Performance"])

        # Group AI and risk panels at the bottom
        if "AI Analysis" in self.dock_widgets and "Risk Management" in self.dock_widgets:
            self.tabifyDockWidget(self.dock_widgets["AI Analysis"], self.dock_widgets["Risk Management"])

        # Ensure Market Data tab is active by default
        if "Market Data" in self.dock_widgets:
            self.dock_widgets["Market Data"].raise_()
        if "Signal Scoring" in self.dock_widgets:
            self.dock_widgets["Signal Scoring"].raise_()
        if "AI Analysis" in self.dock_widgets:
            self.dock_widgets["AI Analysis"].raise_()


    def apply_professional_theme(self):
        """Apply professional dark theme with modern styling"""
        # Set professional color palette
        palette = QPalette()

        # Main colors - Professional dark theme
        palette.setColor(QPalette.Window, QColor(35, 35, 35))           # Main background
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))    # Main text
        palette.setColor(QPalette.Base, QColor(25, 25, 25))             # Input backgrounds
        palette.setColor(QPalette.AlternateBase, QColor(45, 45, 45))    # Alternate rows
        palette.setColor(QPalette.Text, QColor(255, 255, 255))          # Input text
        palette.setColor(QPalette.Button, QColor(55, 55, 55))           # Button background
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))    # Button text
        palette.setColor(QPalette.Highlight, QColor(0, 212, 170))       # Selection color
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))     # Selected text

        self.setPalette(palette)

        # Professional stylesheet
        self.setStyleSheet("""
            /* Main Window */
            QMainWindow {
                background-color: #232323;
                color: #FFFFFF;
            }

            /* Dock Widgets */
            QDockWidget {
                background-color: #2B2B2B;
                border: 1px solid #555555;
                titlebar-close-icon: url(close.png);
                titlebar-normal-icon: url(float.png);
            }

            QDockWidget::title {
                background-color: #3C3C3C;
                color: #FFFFFF;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
                font-size: 11px;
            }

            QDockWidget::close-button, QDockWidget::float-button {
                background-color: #555555;
                border: 1px solid #777777;
                border-radius: 3px;
                width: 16px;
                height: 16px;
            }

            QDockWidget::close-button:hover, QDockWidget::float-button:hover {
                background-color: #777777;
            }

            /* Tab Widgets */
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2B2B2B;
                border-radius: 4px;
            }

            QTabBar::tab {
                background-color: #3C3C3C;
                color: #FFFFFF;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #555555;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
            }

            QTabBar::tab:selected {
                background-color: #00D4AA;
                color: #000000;
            }

            QTabBar::tab:hover {
                background-color: #4A4A4A;
            }

            /* Group Boxes */
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #2B2B2B;
                color: #FFFFFF;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #00D4AA;
                font-size: 12px;
                font-weight: bold;
            }

            /* Labels */
            QLabel {
                color: #FFFFFF;
                background-color: transparent;
            }

            /* Buttons */
            QPushButton {
                background-color: #3C3C3C;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px 16px;
                color: #FFFFFF;
                font-weight: bold;
                min-height: 20px;
            }

            QPushButton:hover {
                background-color: #4A4A4A;
                border-color: #777777;
            }

            QPushButton:pressed {
                background-color: #2A2A2A;
            }

            /* ComboBox */
            QComboBox {
                background-color: #3C3C3C;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 4px 8px;
                color: #FFFFFF;
                min-width: 100px;
            }

            QComboBox:hover {
                border-color: #777777;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }

            /* Scroll Areas */
            QScrollArea {
                background-color: #2B2B2B;
                border: 1px solid #555555;
                border-radius: 4px;
            }

            /* Scroll Bars */
            QScrollBar:vertical {
                background-color: #3C3C3C;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }

            /* Status Bar */
            QStatusBar {
                background-color: #3C3C3C;
                border-top: 1px solid #555555;
                color: #FFFFFF;
            }

            /* Tool Bar */
            QToolBar {
                background-color: #3C3C3C;
                border: 1px solid #555555;
                spacing: 3px;
                padding: 4px;
            }

            /* Progress Bars */
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #2B2B2B;
                color: #FFFFFF;
            }

            QProgressBar::chunk {
                background-color: #00D4AA;
                border-radius: 3px;
            }
        """)

    def change_theme(self, theme_name):
        """Change the application theme"""
        if theme_name == "Dark Professional":
            self.apply_professional_theme()
        elif theme_name == "Light":
            self.apply_light_theme()
        elif theme_name == "Matrix":
            self.apply_matrix_theme()
        elif theme_name == "High Contrast":
            self.apply_high_contrast_theme()

    def apply_light_theme(self):
        """Apply light theme"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(225, 225, 225))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.Highlight, QColor(0, 120, 215))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        self.setPalette(palette)
        self.setStyleSheet("")  # Reset to default

    def apply_matrix_theme(self):
        """Apply Matrix-style green theme"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(0, 0, 0))
        palette.setColor(QPalette.WindowText, QColor(0, 255, 0))
        palette.setColor(QPalette.Base, QColor(10, 10, 10))
        palette.setColor(QPalette.AlternateBase, QColor(20, 20, 20))
        palette.setColor(QPalette.Text, QColor(0, 255, 0))
        palette.setColor(QPalette.Button, QColor(30, 30, 30))
        palette.setColor(QPalette.ButtonText, QColor(0, 255, 0))
        palette.setColor(QPalette.Highlight, QColor(0, 150, 0))
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        self.setPalette(palette)

    def apply_high_contrast_theme(self):
        """Apply high contrast theme"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(0, 0, 0))
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        palette.setColor(QPalette.Base, QColor(0, 0, 0))
        palette.setColor(QPalette.AlternateBase, QColor(40, 40, 40))
        palette.setColor(QPalette.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.Button, QColor(80, 80, 80))
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        palette.setColor(QPalette.Highlight, QColor(255, 255, 0))
        palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        self.setPalette(palette)

    def save_layout(self):
        """Save the current layout configuration"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())

        # Save dock widget states
        for name, dock in self.dock_widgets.items():
            self.settings.setValue(f"dock_{name}_visible", dock.isVisible())
            self.settings.setValue(f"dock_{name}_floating", dock.isFloating())

    def restore_layout(self):
        """Restore the saved layout configuration"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

        # Restore dock widget states
        for name, dock in self.dock_widgets.items():
            visible = self.settings.value(f"dock_{name}_visible", True, type=bool)
            floating = self.settings.value(f"dock_{name}_floating", False, type=bool)
            dock.setVisible(visible)
            dock.setFloating(floating)

    def setup_connections(self):
        """Setup signal connections"""
        self.data_updated.connect(self.on_data_updated)

    def update_display(self):
        """Update the display with latest data"""
        try:
            # Get latest data from the data manager
            data = self.data_manager.get_latest_data()

            if data:
                self.data_updated.emit(data)
        except Exception as e:
            logger.error(f"Error updating display: {e}")

    def on_data_updated(self, data):
        """Handle data updates"""
        try:
            # Update header information
            self.update_header(data)

            # Update all panels
            if "Market Data" in self.panels:
                self.panels["Market Data"].update_data(data.get('market_data', {}))
            if "Timeframe Analysis" in self.panels:
                self.panels["Timeframe Analysis"].update_data(data.get('timeframe_analysis', {}))
            if "Signal Scoring" in self.panels:
                self.panels["Signal Scoring"].update_data(data.get('signal_scoring', {}))
            if "Market Regime" in self.panels:
                self.panels["Market Regime"].update_data(data.get('market_regime', {}))
            if "AI Analysis" in self.panels:
                self.panels["AI Analysis"].update_data(data.get('ai_analysis', {}))
            if "Risk Management" in self.panels:
                self.panels["Risk Management"].update_data(data.get('risk_management', {}))
            if "System Status" in self.panels:
                self.panels["System Status"].update_data(data.get('system_status', {}))
            if "Performance" in self.panels:
                self.panels["Performance"].update_data(data.get('performance', {}))

        except Exception as e:
            logger.error(f"Error handling data update: {e}")

    def update_header(self, data):
        """Update header information"""
        # Connection status
        if data.get('system_status', {}).get('connected', True):  # Default to connected for demo
            self.connection_status.setText("🟢 Connected")
            self.connection_status.setStyleSheet("color: #00D4AA; font-weight: bold;")
        else:
            self.connection_status.setText("🔴 Disconnected")
            self.connection_status.setStyleSheet("color: #FF4444; font-weight: bold;")

        # Current symbol
        symbol = data.get('market_data', {}).get('symbol', 'DOGE/USDT')
        self.current_symbol.setText(f"{symbol}")

        # Last update
        timestamp = data.get('timestamp', datetime.now())
        if isinstance(timestamp, str):
            try:
                # Try to parse ISO format
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                self.last_update.setText(f"Last Update: {dt.strftime('%H:%M:%S')}")
            except:
                self.last_update.setText(f"Last Update: {timestamp}")
        else:
            self.last_update.setText(f"Last Update: {timestamp.strftime('%H:%M:%S')}")

    def create_status_bar(self):
        """Create the status bar"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # Add permanent widgets to status bar
        self.status_label = QLabel("🚀 Ready - Professional Trading Interface")
        self.status_label.setStyleSheet("color: #00D4AA; font-weight: bold;")
        status_bar.addWidget(self.status_label)

        status_bar.addPermanentWidget(QLabel("Epinnox v2.0 Professional"))

    def closeEvent(self, event):
        """Handle window close event"""
        logger.info("Professional Trading System GUI closing")

        # Save layout before closing
        self.save_layout()

        # Stop timer
        self.update_timer.stop()

        event.accept()


def main():
    """Main function to run the professional GUI"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Epinnox Trading System Professional")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("Epinnox")
    app.setOrganizationDomain("epinnox.com")

    # Set application icon (if available)
    try:
        app.setWindowIcon(QIcon("icon.png"))
    except:
        pass

    # Create and show main window
    window = TradingSystemGUI()
    window.show()

    # Center window on screen
    screen = app.primaryScreen().geometry()
    window.move((screen.width() - window.width()) // 2,
                (screen.height() - window.height()) // 2)

    logger.info("Professional Trading System GUI started")

    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
