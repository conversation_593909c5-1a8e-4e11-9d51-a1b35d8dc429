2025-04-20 09:00:35,765 - __main__ - INFO - Starting GPU integration
2025-04-20 09:00:35,766 - __main__ - INFO - Setting up GPU environment for Epinnox Trading System
2025-04-20 09:00:37,396 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:00:37,396 - __main__ - INFO - GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:00:37,396 - __main__ - INFO - Optimizing model loading for GPU acceleration
2025-04-20 09:00:37,397 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-04-20 09:00:37,397 - __main__ - INFO - Model loading optimized for GPU acceleration
2025-04-20 09:00:37,398 - __main__ - INFO - GPU integration completed
2025-06-15 15:44:42,717 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 15:44:42,717 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 15:44:42,717 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 15:44:42,717 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 15:44:44,130 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 15:44:44,130 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 15:44:44,137 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 15:44:44,137 - models.model_cache - INFO - Model cache initialized
2025-06-15 15:44:44,137 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 15:44:44,137 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 15:44:44,137 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 15:44:45,210 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 15:44:45,220 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 15:44:45,224 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 15:44:45,225 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 15:44:45,232 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 15:44:45,232 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-15 15:44:45,232 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 15:44:45,232 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-15 15:54:22,813 - gpu_integration - INFO - Setting up GPU environment for Epinnox Trading System
2025-06-15 15:54:22,813 - gpu_integration - INFO - Loaded GPU configuration from C:\Users\<USER>\Documents\dev\Epinnox_v6\config\gpu_config.yaml
2025-06-15 15:54:22,813 - gpu_integration - INFO - GPU acceleration disabled in configuration
2025-06-15 15:54:22,813 - gpu_integration - INFO - Optimizing model loading for GPU acceleration
2025-06-15 15:54:24,221 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 15:54:24,221 - gpu_utils - INFO - PyTorch GPU acceleration enabled: NVIDIA GeForce RTX 4070 Laptop GPU (CUDA 12.1)
2025-06-15 15:54:24,221 - models.model_cache - INFO - Model cache cleanup thread started
2025-06-15 15:54:24,221 - models.model_cache - INFO - Model cache initialized
2025-06-15 15:54:24,221 - gpu_integration - INFO - Model cache initialized for GPU acceleration
2025-06-15 15:54:24,221 - gpu_integration - INFO - Model loading optimized for GPU acceleration
2025-06-15 15:54:24,221 - startup - INFO - GPU acceleration not available. Using CPU only.
2025-06-15 15:54:25,336 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-06-15 15:54:25,336 - core.multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 15:54:25,347 - core.market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 15:54:25,348 - core.adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-06-15 15:54:25,352 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-15 15:54:25,352 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-15 15:54:25,352 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-15 15:54:25,352 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
