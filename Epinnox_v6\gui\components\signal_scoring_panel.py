"""
Signal Scoring Panel
Displays individual signal scores and overall signal analysis
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QFrame, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class SignalScoringPanel(QWidget):
    """Panel displaying signal scoring system details"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Overall Signal Summary
        summary_group = QGroupBox("🎯 Signal Summary")
        summary_layout = QGridLayout(summary_group)
        
        # Total score and confidence
        self.total_score_label = QLabel("0.00")
        self.total_score_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.total_score_label.setAlignment(Qt.AlignCenter)
        
        self.confidence_label = QLabel("0%")
        self.confidence_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.confidence_label.setAlignment(Qt.AlignCenter)
        
        self.signal_strength_label = QLabel("WEAK")
        self.signal_strength_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.signal_strength_label.setAlignment(Qt.AlignCenter)
        
        # Confidence progress bar
        self.confidence_bar = QProgressBar()
        self.confidence_bar.setRange(0, 100)
        self.confidence_bar.setValue(0)
        self.confidence_bar.setTextVisible(True)
        
        summary_layout.addWidget(QLabel("Total Score:"), 0, 0)
        summary_layout.addWidget(self.total_score_label, 0, 1)
        summary_layout.addWidget(QLabel("Confidence:"), 0, 2)
        summary_layout.addWidget(self.confidence_label, 0, 3)
        
        summary_layout.addWidget(QLabel("Signal Strength:"), 1, 0)
        summary_layout.addWidget(self.signal_strength_label, 1, 1)
        summary_layout.addWidget(self.confidence_bar, 1, 2, 1, 2)
        
        layout.addWidget(summary_group)
        
        # Individual Signal Scores
        signals_group = QGroupBox("📊 Individual Signal Scores")
        signals_layout = QVBoxLayout(signals_group)
        
        # Signals table
        self.signals_table = QTableWidget(5, 4)
        self.signals_table.setHorizontalHeaderLabels([
            "Signal", "Score", "Weight", "Contribution"
        ])
        self.signals_table.horizontalHeader().setStretchLastSection(True)
        self.signals_table.setMaximumHeight(200)
        
        # Initialize signal rows
        signals = ['MACD', 'Order Book', 'Volume', 'Price Action', 'Trend']
        for i, signal in enumerate(signals):
            self.signals_table.setItem(i, 0, QTableWidgetItem(signal))
            self.signals_table.setItem(i, 1, QTableWidgetItem("0.00"))
            self.signals_table.setItem(i, 2, QTableWidgetItem("1.0"))
            self.signals_table.setItem(i, 3, QTableWidgetItem("0.00"))
        
        signals_layout.addWidget(self.signals_table)
        layout.addWidget(signals_group)
        
        # Signal Analysis Details
        analysis_group = QGroupBox("🔍 Signal Analysis Details")
        analysis_layout = QGridLayout(analysis_group)
        
        # Signal metrics
        self.alignment_percentage = QLabel("0%")
        self.strongest_signal = QLabel("--")
        self.weakest_signal = QLabel("--")
        self.signal_consistency = QLabel("--")
        
        analysis_layout.addWidget(QLabel("Signal Alignment:"), 0, 0)
        analysis_layout.addWidget(self.alignment_percentage, 0, 1)
        analysis_layout.addWidget(QLabel("Strongest Signal:"), 0, 2)
        analysis_layout.addWidget(self.strongest_signal, 0, 3)
        
        analysis_layout.addWidget(QLabel("Weakest Signal:"), 1, 0)
        analysis_layout.addWidget(self.weakest_signal, 1, 1)
        analysis_layout.addWidget(QLabel("Consistency:"), 1, 2)
        analysis_layout.addWidget(self.signal_consistency, 1, 3)
        
        layout.addWidget(analysis_group)
        
        # Signal History and Performance
        history_group = QGroupBox("📈 Signal Performance")
        history_layout = QGridLayout(history_group)
        
        self.recent_signals = QLabel("Recent signals: 0")
        self.successful_signals = QLabel("Successful: 0")
        self.success_rate = QLabel("Success rate: 0%")
        self.avg_confidence = QLabel("Avg confidence: 0%")
        
        history_layout.addWidget(self.recent_signals, 0, 0)
        history_layout.addWidget(self.successful_signals, 0, 1)
        history_layout.addWidget(self.success_rate, 1, 0)
        history_layout.addWidget(self.avg_confidence, 1, 1)
        
        layout.addWidget(history_group)

        # Apply Matrix theme to table
        self.apply_matrix_theme_to_table()

    def apply_matrix_theme_to_table(self):
        """Apply Matrix theme to the signals table"""
        matrix_table_style = """
            QTableWidget {
                background-color: #000000;
                color: #00FF00;
                border: 1px solid #00AA00;
                gridline-color: #003300;
                font-family: 'Courier New', monospace;
                font-size: 9px;
                selection-background-color: #004400;
            }
            QTableWidget::item {
                background-color: #000000;
                color: #00FF00;
                border: none;
                padding: 2px;
            }
            QTableWidget::item:selected {
                background-color: #004400;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
                border: 1px solid #00AA00;
                padding: 4px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
                font-size: 9px;
            }
        """
        self.signals_table.setStyleSheet(matrix_table_style)

    def apply_theme(self, theme_name):
        """Apply theme to the panel"""
        if theme_name == "matrix":
            self.apply_matrix_theme_to_table()
            # Update labels to Matrix colors
            matrix_label_style = "color: #00FF00; font-family: 'Courier New', monospace;"
            self.total_score_label.setStyleSheet(matrix_label_style)
            self.confidence_label.setStyleSheet(matrix_label_style)
            self.signal_strength_label.setStyleSheet(matrix_label_style)

    def update_data(self, data):
        """Update the panel with new signal scoring data"""
        try:
            # Update overall signal summary
            total_score = data.get('total_score', 0.0)
            confidence = data.get('confidence', 0.0)
            signal_strength = data.get('signal_strength', 'weak').upper()
            
            self.total_score_label.setText(f"{total_score:.2f}")
            self.confidence_label.setText(f"{confidence:.0f}%")
            self.signal_strength_label.setText(signal_strength)
            
            # Color code total score
            if total_score > 0.5:
                self.total_score_label.setStyleSheet("color: #00ff88;")
            elif total_score < -0.5:
                self.total_score_label.setStyleSheet("color: #ff4444;")
            else:
                self.total_score_label.setStyleSheet("color: #ffaa00;")
            
            # Color code signal strength
            strength_colors = {
                'VERY_STRONG': '#00ff88',
                'STRONG': '#88ff00',
                'MODERATE': '#ffaa00',
                'WEAK': '#ff8800',
                'VERY_WEAK': '#ff4444'
            }
            color = strength_colors.get(signal_strength, '#ffffff')
            self.signal_strength_label.setStyleSheet(f"color: {color};")
            
            # Update confidence bar
            self.confidence_bar.setValue(int(confidence))
            if confidence >= 80:
                self.confidence_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            elif confidence >= 60:
                self.confidence_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.confidence_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            
            # Update individual signals
            individual_scores = data.get('individual_scores', {})
            self.update_signals_table(individual_scores)
            
            # Update analysis details
            self.update_analysis_details(data)
            
        except Exception as e:
            print(f"Error updating signal scoring panel: {e}")
    
    def update_signals_table(self, scores):
        """Update the individual signals table"""
        try:
            signal_names = ['macd', 'order_book', 'volume', 'price_action', 'trend']
            display_names = ['MACD', 'Order Book', 'Volume', 'Price Action', 'Trend']
            
            # Default weights (could be made configurable)
            weights = {'macd': 1.2, 'order_book': 1.5, 'volume': 1.0, 'price_action': 1.1, 'trend': 1.3}
            
            for i, (signal_key, display_name) in enumerate(zip(signal_names, display_names)):
                if i < self.signals_table.rowCount():
                    score = scores.get(signal_key, 0.0)
                    weight = weights.get(signal_key, 1.0)
                    contribution = score * weight
                    
                    # Update score with Matrix colors
                    score_item = QTableWidgetItem(f"{score:.3f}")
                    score_item.setForeground(QColor(0, 255, 0))  # Matrix green text
                    if score > 0.3:
                        score_item.setBackground(QColor(0, 170, 0, 60))  # Matrix green
                    elif score < -0.3:
                        score_item.setBackground(QColor(170, 0, 0, 60))  # Matrix red
                    else:
                        score_item.setBackground(QColor(170, 170, 0, 40))  # Matrix yellow
                    
                    # Update weight with Matrix colors
                    weight_item = QTableWidgetItem(f"{weight:.1f}")
                    weight_item.setForeground(QColor(0, 255, 0))

                    # Update contribution with Matrix colors
                    contribution_item = QTableWidgetItem(f"{contribution:.3f}")
                    contribution_item.setForeground(QColor(0, 255, 0))
                    if contribution > 0.3:
                        contribution_item.setBackground(QColor(0, 170, 0, 40))  # Matrix green
                    elif contribution < -0.3:
                        contribution_item.setBackground(QColor(170, 0, 0, 40))  # Matrix red
                    
                    self.signals_table.setItem(i, 1, score_item)
                    self.signals_table.setItem(i, 2, weight_item)
                    self.signals_table.setItem(i, 3, contribution_item)
                    
        except Exception as e:
            print(f"Error updating signals table: {e}")
    
    def update_analysis_details(self, data):
        """Update the signal analysis details"""
        try:
            # Calculate signal alignment
            individual_scores = data.get('individual_scores', {})
            alignment = data.get('alignment_percentage', 0.0)
            
            self.alignment_percentage.setText(f"{alignment:.1f}%")
            
            # Color code alignment
            if alignment >= 80:
                self.alignment_percentage.setStyleSheet("color: #00ff88;")
            elif alignment >= 60:
                self.alignment_percentage.setStyleSheet("color: #ffaa00;")
            else:
                self.alignment_percentage.setStyleSheet("color: #ff4444;")
            
            # Find strongest and weakest signals
            if individual_scores:
                strongest = max(individual_scores.items(), key=lambda x: abs(x[1]))
                weakest = min(individual_scores.items(), key=lambda x: abs(x[1]))
                
                self.strongest_signal.setText(f"{strongest[0].replace('_', ' ').title()}: {strongest[1]:.3f}")
                self.weakest_signal.setText(f"{weakest[0].replace('_', ' ').title()}: {weakest[1]:.3f}")
                
                # Calculate consistency (how close signals are to each other)
                scores_list = list(individual_scores.values())
                if len(scores_list) > 1:
                    avg_score = sum(scores_list) / len(scores_list)
                    variance = sum((s - avg_score) ** 2 for s in scores_list) / len(scores_list)
                    consistency = max(0, 100 - (variance * 100))
                    
                    self.signal_consistency.setText(f"{consistency:.1f}%")
                    
                    if consistency >= 80:
                        self.signal_consistency.setStyleSheet("color: #00ff88;")
                    elif consistency >= 60:
                        self.signal_consistency.setStyleSheet("color: #ffaa00;")
                    else:
                        self.signal_consistency.setStyleSheet("color: #ff4444;")
            
        except Exception as e:
            print(f"Error updating analysis details: {e}")

    def update_performance_metrics(self, performance_data):
        """Update signal performance metrics"""
        try:
            total_signals = performance_data.get('total_signals', 0)
            successful_signals = performance_data.get('successful_signals', 0)
            success_rate = performance_data.get('success_rate', 0.0)
            avg_confidence = performance_data.get('avg_confidence', 0.0)

            self.recent_signals.setText(f"Recent signals: {total_signals}")
            self.successful_signals.setText(f"Successful: {successful_signals}")
            self.success_rate.setText(f"Success rate: {success_rate:.1f}%")
            self.avg_confidence.setText(f"Avg confidence: {avg_confidence:.1f}%")

            # Color code success rate
            if success_rate >= 70:
                self.success_rate.setStyleSheet("color: #00ff88;")
            elif success_rate >= 50:
                self.success_rate.setStyleSheet("color: #ffaa00;")
            else:
                self.success_rate.setStyleSheet("color: #ff4444;")

        except Exception as e:
            print(f"Error updating performance metrics: {e}")
