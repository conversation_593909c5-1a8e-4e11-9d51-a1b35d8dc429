#!/usr/bin/env python3
"""
Test script for Dynamic Leverage & Position Sizing System

This script demonstrates the enhanced Epinnox v6 trading system with:
- Dynamic leverage fetching from exchange APIs
- Intelligent leverage scaling based on market conditions
- Smart position sizing with risk management
- Comprehensive leverage analysis and reporting
"""

import sys
import logging
from datetime import datetime
from main import run_trading_system

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

def test_leverage_system():
    """Test the dynamic leverage and position sizing system"""
    
    print("=" * 80)
    print("🚀 EPINNOX v6 DYNAMIC LEVERAGE & POSITION SIZING TEST")
    print("=" * 80)
    print()
    
    # Test different symbols to demonstrate leverage capabilities
    test_symbols = [
        'DOGE/USDT',      # Spot trading (lower leverage)
        'DOGE/USDT:USDT', # Futures trading (higher leverage)
        'BTC/USDT',       # Major cryptocurrency
        'ETH/USDT:USDT'   # Ethereum futures
    ]
    
    results = []
    
    for i, symbol in enumerate(test_symbols, 1):
        print(f"\n📊 TEST {i}/4: {symbol}")
        print("-" * 50)
        
        try:
            # Run the trading system
            decision, explanation, parsed_response = run_trading_system(
                symbol=symbol,
                use_live_data=False
            )
            
            # Extract leverage information
            leverage_info = parsed_response.get('leverage_position_sizing', {})
            
            if leverage_info:
                result = {
                    'symbol': symbol,
                    'decision': decision,
                    'max_leverage': leverage_info.get('max_leverage', 0),
                    'recommended_leverage': leverage_info.get('recommended_leverage', 0),
                    'effective_leverage': leverage_info.get('effective_leverage', 0),
                    'position_units': leverage_info.get('position_units', 0),
                    'position_usd': leverage_info.get('position_usd', 0),
                    'risk_per_trade': leverage_info.get('risk_per_trade_usd', 0),
                    'leverage_reasoning': leverage_info.get('leverage_reasoning', 'N/A'),
                    'risk_warnings': leverage_info.get('risk_warnings', [])
                }
                
                results.append(result)
                
                # Display key metrics
                print(f"✅ Decision: {decision}")
                print(f"📈 Max Leverage: {result['max_leverage']:.1f}x")
                print(f"🎯 Recommended: {result['recommended_leverage']:.1f}x")
                print(f"⚡ Effective: {result['effective_leverage']:.1f}x")
                print(f"💰 Position: {result['position_units']:.2f} units (${result['position_usd']:.2f})")
                print(f"⚠️  Risk: ${result['risk_per_trade']:.2f}")
                
                if result['risk_warnings']:
                    print(f"🚨 Warnings: {len(result['risk_warnings'])} warnings")
                    for warning in result['risk_warnings']:
                        print(f"   - {warning}")
                
            else:
                print(f"❌ No leverage information available for {symbol}")
                
        except Exception as e:
            print(f"❌ Error testing {symbol}: {e}")
            continue
    
    # Generate comprehensive report
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE LEVERAGE ANALYSIS REPORT")
    print("=" * 80)
    
    if results:
        print(f"\n🎯 TESTED SYMBOLS: {len(results)}")
        print("-" * 40)
        
        for result in results:
            print(f"\n📈 {result['symbol']}:")
            print(f"   Max Leverage: {result['max_leverage']:.1f}x")
            print(f"   Recommended: {result['recommended_leverage']:.1f}x")
            print(f"   Effective: {result['effective_leverage']:.1f}x")
            print(f"   Decision: {result['decision']}")
            print(f"   Position: ${result['position_usd']:.2f}")
            print(f"   Risk: ${result['risk_per_trade']:.2f}")
        
        # Calculate statistics
        max_leverages = [r['max_leverage'] for r in results]
        recommended_leverages = [r['recommended_leverage'] for r in results]
        effective_leverages = [r['effective_leverage'] for r in results]
        
        print(f"\n📊 LEVERAGE STATISTICS:")
        print(f"   Max Leverage Range: {min(max_leverages):.1f}x - {max(max_leverages):.1f}x")
        print(f"   Avg Recommended: {sum(recommended_leverages)/len(recommended_leverages):.1f}x")
        print(f"   Avg Effective: {sum(effective_leverages)/len(effective_leverages):.1f}x")
        
        # Risk analysis
        total_risk = sum(r['risk_per_trade'] for r in results)
        total_position_value = sum(r['position_usd'] for r in results)
        
        print(f"\n💰 RISK ANALYSIS:")
        print(f"   Total Risk Exposure: ${total_risk:.2f}")
        print(f"   Total Position Value: ${total_position_value:.2f}")
        print(f"   Risk as % of $50 Balance: {(total_risk/50)*100:.1f}%")
        
        # Decision distribution
        decisions = [r['decision'] for r in results]
        decision_counts = {d: decisions.count(d) for d in set(decisions)}
        
        print(f"\n🎯 DECISION DISTRIBUTION:")
        for decision, count in decision_counts.items():
            percentage = (count / len(results)) * 100
            print(f"   {decision}: {count} ({percentage:.1f}%)")
        
        # Risk warnings summary
        all_warnings = []
        for result in results:
            all_warnings.extend(result['risk_warnings'])
        
        if all_warnings:
            print(f"\n⚠️  RISK WARNINGS SUMMARY:")
            unique_warnings = list(set(all_warnings))
            for warning in unique_warnings:
                count = all_warnings.count(warning)
                print(f"   {warning} ({count} occurrences)")
        
        print(f"\n✅ SYSTEM PERFORMANCE:")
        print(f"   Successfully tested: {len(results)}/{len(test_symbols)} symbols")
        print(f"   Leverage detection: {'✅ Working' if any(r['max_leverage'] > 1 for r in results) else '⚠️ Limited'}")
        print(f"   Position sizing: {'✅ Active' if any(r['position_usd'] > 0 for r in results) else '⚠️ Conservative'}")
        print(f"   Risk management: {'✅ Active' if all_warnings else '✅ No warnings'}")
        
    else:
        print("❌ No results to analyze")
    
    print("\n" + "=" * 80)
    print("🎉 DYNAMIC LEVERAGE SYSTEM TEST COMPLETED")
    print("=" * 80)

def demonstrate_leverage_scenarios():
    """Demonstrate different leverage scenarios"""
    
    print("\n" + "=" * 80)
    print("🎭 LEVERAGE SCENARIO DEMONSTRATIONS")
    print("=" * 80)
    
    scenarios = [
        {
            'name': 'High Confidence Scenario',
            'description': 'Testing with DOGE/USDT (should get moderate leverage)',
            'symbol': 'DOGE/USDT'
        },
        {
            'name': 'Futures Trading Scenario', 
            'description': 'Testing with DOGE/USDT:USDT (should get higher leverage)',
            'symbol': 'DOGE/USDT:USDT'
        },
        {
            'name': 'Major Crypto Scenario',
            'description': 'Testing with BTC/USDT (should be more conservative)',
            'symbol': 'BTC/USDT'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🎬 SCENARIO {i}: {scenario['name']}")
        print(f"📝 {scenario['description']}")
        print("-" * 60)
        
        try:
            decision, explanation, parsed_response = run_trading_system(
                symbol=scenario['symbol'],
                use_live_data=False
            )
            
            leverage_info = parsed_response.get('leverage_position_sizing', {})
            
            if leverage_info:
                print(f"🎯 Result: {decision}")
                print(f"📊 Leverage: {leverage_info.get('max_leverage', 0):.1f}x max → "
                      f"{leverage_info.get('recommended_leverage', 0):.1f}x recommended → "
                      f"{leverage_info.get('effective_leverage', 0):.1f}x effective")
                print(f"💰 Position: {leverage_info.get('position_units', 0):.2f} units "
                      f"(${leverage_info.get('position_usd', 0):.2f})")
                print(f"⚠️  Risk: ${leverage_info.get('risk_per_trade_usd', 0):.2f}")
                print(f"🧠 Reasoning: {leverage_info.get('leverage_reasoning', 'N/A')[:100]}...")
            else:
                print("❌ No leverage information available")
                
        except Exception as e:
            print(f"❌ Error in scenario: {e}")

if __name__ == "__main__":
    print(f"🚀 Starting Dynamic Leverage System Tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Run main leverage system test
        test_leverage_system()
        
        # Demonstrate specific scenarios
        demonstrate_leverage_scenarios()
        
        print(f"\n🎉 All tests completed successfully at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()
