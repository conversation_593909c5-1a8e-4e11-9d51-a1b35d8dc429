"""
Main Application
Initializes and runs the Epinnox Trading System with the new GUI structure
"""
import sys
import logging
import time
import threading
import uuid
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QSplashScreen, QMainWindow, QTabWidget, QWidget, QVBoxLayout
from PyQt5.QtCore import Qt, QTimer, QSettings
from PyQt5.QtGui import QPixmap, QFont

# Import window classes
from gui.dashboard.dashboard_window import DashboardWindow
from gui.live_worker.live_worker_window import LiveWorkerWindow
from gui.simulation_worker.simulation_worker_window import SimulationWorkerWindow
from gui.settings.settings_window import SettingsWindow

# Import common components
from gui.common.theme_manager import ThemeManager
from gui.common.notification_manager import NotificationManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("epinnox.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MainApplication(QMainWindow):
    """Main application window that manages all other windows"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox Trading System")
        
        # Initialize managers
        self.theme_manager = ThemeManager()
        self.notification_manager = NotificationManager(self)
        
        # Initialize window tracking
        self.dashboard_window = None
        self.live_workers = {}
        self.simulation_workers = {}
        self.settings_window = None
        
        # Initialize settings
        self.settings = QSettings("Epinnox", "MainApplication")
        
        # Initialize UI
        self.init_ui()
        
        # Restore window geometry
        self.restore_geometry()
        
        # Create initial windows
        self.create_dashboard()
        self.create_simulation_worker()

    def init_ui(self):
        """Initialize the user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create tab widget for window management
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        
        main_layout.addWidget(self.tab_widget)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create status bar
        self.statusBar().showMessage("Ready")
        
        # Apply theme
        self.apply_theme()

    def create_menu_bar(self):
        """Create the application menu bar"""
        menu_bar = self.menuBar()
        
        # File menu
        file_menu = menu_bar.addMenu("&File")
        
        # New dashboard action
        new_dashboard_action = file_menu.addAction("New &Dashboard")
        new_dashboard_action.triggered.connect(self.create_dashboard)
        
        # New simulation worker action
        new_sim_action = file_menu.addAction("New &Simulation Worker")
        new_sim_action.triggered.connect(self.create_simulation_worker)
        
        # New live worker action
        new_live_action = file_menu.addAction("New &Live Worker")
        new_live_action.triggered.connect(self.create_live_worker)
        
        file_menu.addSeparator()
        
        # Settings action
        settings_action = file_menu.addAction("&Settings")
        settings_action.triggered.connect(self.show_settings)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = file_menu.addAction("E&xit")
        exit_action.triggered.connect(self.close)
        
        # View menu
        view_menu = menu_bar.addMenu("&View")
        
        # Theme submenu
        theme_menu = view_menu.addMenu("&Theme")
        
        # Dark theme action
        dark_theme_action = theme_menu.addAction("&Dark")
        dark_theme_action.triggered.connect(lambda: self.change_theme("dark"))
        
        # Light theme action
        light_theme_action = theme_menu.addAction("&Light")
        light_theme_action.triggered.connect(lambda: self.change_theme("light"))
        
        # Help menu
        help_menu = menu_bar.addMenu("&Help")
        
        # About action
        about_action = help_menu.addAction("&About")
        about_action.triggered.connect(self.show_about)

    def apply_theme(self):
        """Apply the current theme to the application"""
        # Apply palette
        self.setPalette(self.theme_manager.get_palette())
        
        # Apply stylesheet
        self.setStyleSheet(self.theme_manager.get_stylesheet())

    def change_theme(self, theme_name):
        """Change the application theme"""
        self.theme_manager.current_theme = theme_name
        self.apply_theme()
        
        # Update all windows
        if self.dashboard_window:
            self.dashboard_window.apply_theme(theme_name)
        
        for worker_id, worker in self.live_workers.items():
            worker.apply_theme(theme_name)
        
        for worker_id, worker in self.simulation_workers.items():
            worker.apply_theme(theme_name)

    def restore_geometry(self):
        """Restore window geometry from settings"""
        if self.settings.contains("geometry"):
            self.restoreGeometry(self.settings.value("geometry"))
        else:
            # Default position and size
            self.setGeometry(100, 100, 1200, 800)

    def closeEvent(self, event):
        """Handle window close event"""
        # Save window geometry
        self.settings.setValue("geometry", self.saveGeometry())
        
        # Close all windows
        if self.dashboard_window:
            self.dashboard_window.close()
        
        for worker_id, worker in self.live_workers.items():
            worker.close()
        
        for worker_id, worker in self.simulation_workers.items():
            worker.close()
        
        if self.settings_window:
            self.settings_window.close()
        
        # Accept the event
        event.accept()

    def create_dashboard(self):
        """Create a new dashboard window"""
        if not self.dashboard_window:
            self.dashboard_window = DashboardWindow()
            
            # Connect signals
            self.dashboard_window.new_simulation_requested.connect(self.create_simulation_worker)
            self.dashboard_window.new_live_worker_requested.connect(self.create_live_worker)
            self.dashboard_window.settings_requested.connect(self.show_settings)
            
            # Show the window
            self.dashboard_window.show()
            
            # Log
            logger.info("Created dashboard window")

    def create_simulation_worker(self):
        """Create a new simulation worker window"""
        # Generate unique ID
        worker_id = str(uuid.uuid4())
        worker_name = f"Simulation {len(self.simulation_workers) + 1}"
        
        # Create window
        worker = SimulationWorkerWindow(worker_id, worker_name)
        
        # Connect signals
        worker.worker_closed.connect(self.remove_simulation_worker)
        
        # Store reference
        self.simulation_workers[worker_id] = worker
        
        # Show the window
        worker.show()
        
        # Log
        logger.info(f"Created simulation worker: {worker_name} (ID: {worker_id})")
        
        return worker

    def create_live_worker(self):
        """Create a new live worker window"""
        # Generate unique ID
        worker_id = str(uuid.uuid4())
        worker_name = f"Live Worker {len(self.live_workers) + 1}"
        
        # Create window
        worker = LiveWorkerWindow(worker_id, worker_name)
        
        # Connect signals
        worker.worker_closed.connect(self.remove_live_worker)
        
        # Store reference
        self.live_workers[worker_id] = worker
        
        # Show the window
        worker.show()
        
        # Log
        logger.info(f"Created live worker: {worker_name} (ID: {worker_id})")
        
        return worker

    def remove_simulation_worker(self, worker_id):
        """Remove a simulation worker from tracking"""
        if worker_id in self.simulation_workers:
            del self.simulation_workers[worker_id]
            logger.info(f"Removed simulation worker (ID: {worker_id})")

    def remove_live_worker(self, worker_id):
        """Remove a live worker from tracking"""
        if worker_id in self.live_workers:
            del self.live_workers[worker_id]
            logger.info(f"Removed live worker (ID: {worker_id})")

    def show_settings(self):
        """Show the settings window"""
        if not self.settings_window:
            self.settings_window = SettingsWindow(self)
            
            # Connect signals
            self.settings_window.settings_changed.connect(self.apply_settings)
        
        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()

    def apply_settings(self, settings_data):
        """Apply settings from the settings window"""
        # Apply theme if changed
        if 'theme' in settings_data:
            self.change_theme(settings_data['theme'])
        
        # Apply other settings as needed
        
        # Log
        logger.info("Applied settings changes")

    def show_about(self):
        """Show the about dialog"""
        # This would be implemented with a QMessageBox or custom dialog
        self.notification_manager.show_notification(
            "About Epinnox Trading System",
            "Epinnox Trading System v6.0\nDeveloped by Epinnox Team",
            NotificationManager.INFO
        )

    def close_tab(self, index):
        """Close a tab in the tab widget"""
        # This would be implemented if using a tab-based interface
        pass


def main():
    """Main application entry point"""
    try:
        # Create application
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox Trading System")
        app.setApplicationVersion("6.0.0")
        
        # Create splash screen
        splash_pixmap = QPixmap("assets/wolf.png")
        if splash_pixmap.isNull():
            # Create a default splash screen if image not found
            splash_pixmap = QPixmap(600, 400)
            splash_pixmap.fill(Qt.black)
        
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        
        # Process events to display splash
        app.processEvents()
        
        # Create main application
        main_app = MainApplication()
        
        # Show main window and close splash
        main_app.show()
        splash.finish(main_app)
        
        # Start event loop
        sys.exit(app.exec_())
    
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
