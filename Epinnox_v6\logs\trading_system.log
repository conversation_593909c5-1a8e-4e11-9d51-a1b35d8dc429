2025-04-16 08:28:25,379 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:28:28,165 - __main__ - ERROR - Fatal error: name 'QGridLayout' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 40, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 27, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 50, in init_ui
    self.create_panels()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 94, in create_panels
    self.position_panel = PositionTrackingPanel()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\position_tracking_panel.py", line 22, in __init__
    self.initUI()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\position_tracking_panel.py", line 90, in initUI
    summary_layout = QGridLayout()
NameError: name 'QGridLayout' is not defined
2025-04-16 08:29:00,825 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:29:01,647 - __main__ - ERROR - Fatal error: 'LogConsolePanel' object has no attribute 'add_message'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 40, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 27, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 56, in init_ui
    self.connect_signals()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 127, in connect_signals
    self.trading_interface.log_message.connect(self.log_panel.add_message)
AttributeError: 'LogConsolePanel' object has no attribute 'add_message'
2025-04-16 08:29:16,486 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:29:17,275 - __main__ - ERROR - Fatal error: 'LogConsolePanel' object has no attribute 'add_message'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 40, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 27, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 56, in init_ui
    self.connect_signals()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 127, in connect_signals
    self.trading_interface.log_message.connect(self.log_panel.add_message)
AttributeError: 'LogConsolePanel' object has no attribute 'add_message'
2025-04-16 08:29:41,631 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:30:28,919 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:31:35,282 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:32:21,826 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:33:25,247 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:36:05,475 - __main__ - INFO - Application closed normally
2025-04-16 08:41:00,653 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:55:03,318 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 08:56:40,206 - __main__ - INFO - Application closed normally
2025-04-16 08:56:51,566 - __main__ - INFO - Application closed normally
2025-04-16 09:00:02,394 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 09:01:20,894 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 09:05:07,167 - __main__ - INFO - Application closed normally
2025-04-16 14:00:33,363 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 14:00:34,055 - __main__ - INFO - Initializing components...
2025-04-16 14:00:34,578 - __main__ - INFO - Loading configuration...
2025-04-16 14:00:35,091 - __main__ - INFO - Connecting to data sources...
2025-04-16 14:00:35,614 - __main__ - INFO - Creating main window...
2025-04-16 14:00:37,614 - multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 14:00:37,614 - market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 14:00:37,618 - adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 14:00:37,622 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-16 17:24:08,166 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 17:24:09,455 - __main__ - INFO - Initializing components...
2025-04-16 17:24:09,966 - __main__ - INFO - Loading configuration...
2025-04-16 17:24:10,474 - __main__ - INFO - Connecting to data sources...
2025-04-16 17:24:10,982 - __main__ - INFO - Creating main window...
2025-04-16 17:24:10,982 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 17:24:10,982 - main_window - INFO - Initializing MainWindow
2025-04-16 17:24:10,982 - main_window - INFO - Creating QSettings
2025-04-16 17:24:10,982 - main_window - INFO - Creating ThemeManager
2025-04-16 17:24:10,982 - main_window - INFO - Creating NotificationManager
2025-04-16 17:24:11,005 - main_window - INFO - Calling init_ui
2025-04-16 17:24:11,005 - main_window - INFO - Setting window title and geometry
2025-04-16 17:24:11,005 - main_window - INFO - Creating menu bar
2025-04-16 17:24:11,005 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 17:24:11,015 - main_window - INFO - Creating central widget
2025-04-16 17:24:11,016 - main_window - INFO - Creating control toolbar
2025-04-16 17:24:11,016 - main_window - INFO - Adding bot buttons
2025-04-16 17:24:11,016 - main_window - INFO - Creating bot tab widget
2025-04-16 17:24:11,016 - main_window - INFO - Creating status bar
2025-04-16 17:24:11,027 - main_window - INFO - Applying theme
2025-04-16 17:24:11,077 - main_window - INFO - Restoring window geometry
2025-04-16 17:24:11,077 - main_window - INFO - Adding initial simulation bot
2025-04-16 17:24:11,077 - main_window - INFO - Adding new simulation bot
2025-04-16 17:24:11,077 - main_window - INFO - Getting bot name from user
2025-04-16 18:53:56,091 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 18:53:57,055 - __main__ - INFO - Initializing components...
2025-04-16 18:53:57,569 - __main__ - INFO - Loading configuration...
2025-04-16 18:53:58,085 - __main__ - INFO - Connecting to data sources...
2025-04-16 18:53:58,587 - __main__ - INFO - Creating main window...
2025-04-16 18:53:58,589 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 18:53:58,589 - main_window - INFO - Initializing MainWindow
2025-04-16 18:53:58,589 - main_window - INFO - Creating QSettings
2025-04-16 18:53:58,590 - main_window - INFO - Creating ThemeManager
2025-04-16 18:53:58,590 - main_window - INFO - Creating NotificationManager
2025-04-16 18:53:58,590 - main_window - INFO - Calling init_ui
2025-04-16 18:53:58,590 - main_window - INFO - Setting window title and geometry
2025-04-16 18:53:58,590 - main_window - INFO - Creating menu bar
2025-04-16 18:53:58,590 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 18:53:58,590 - main_window - INFO - Creating central widget
2025-04-16 18:53:58,598 - main_window - INFO - Creating control toolbar
2025-04-16 18:53:58,598 - main_window - INFO - Adding bot buttons
2025-04-16 18:53:58,598 - main_window - INFO - Creating bot tab widget
2025-04-16 18:53:58,598 - main_window - INFO - Creating status bar
2025-04-16 18:53:58,598 - main_window - INFO - Applying theme
2025-04-16 18:53:58,606 - main_window - INFO - Restoring window geometry
2025-04-16 18:53:58,606 - main_window - INFO - Adding initial simulation bot
2025-04-16 18:53:58,606 - main_window - INFO - Adding new simulation bot
2025-04-16 18:53:58,606 - main_window - INFO - Using default bot name
2025-04-16 18:53:58,606 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 18:53:58,606 - main_window - INFO - Generating unique ID
2025-04-16 18:53:58,606 - main_window - INFO - Creating BotTabWidget with ID 34ff6039-aaf7-466b-9748-975743d65083 and name Simulation Bot 1
2025-04-16 18:54:01,029 - multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 18:54:01,031 - market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 18:54:01,032 - adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 18:54:01,039 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-16 18:54:12,962 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:54:12,964 - trade_lifecycle - INFO - Initialized trade lifecycle manager
2025-04-16 18:54:19,731 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:54:19,731 - dynamic_targets - INFO - Initialized dynamic target calculator with htx exchange
2025-04-16 18:54:19,731 - dynamic_targets - INFO - Fee rate: 0.0400%
2025-04-16 18:54:19,731 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:54:22,433 - data.exchange - INFO - Fetched 2000 5m candles for DOGE/USDT:USDT (spot)
2025-04-16 18:54:22,435 - dynamic_targets - INFO - Calculated dynamic targets for DOGE/USDT:USDT with 20.00x leverage:
2025-04-16 18:54:22,435 - dynamic_targets - INFO - Take profit: 3.92%, Stop loss: 0.62%
2025-04-16 18:54:22,496 - main_window - INFO - BotTabWidget created successfully
2025-04-16 18:54:22,496 - main_window - INFO - Adding to tab widget
2025-04-16 18:54:22,521 - main_window - INFO - Updating status bar
2025-04-16 18:54:22,521 - __main__ - INFO - MainWindow initialized successfully
2025-04-16 18:55:18,921 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:55:18,921 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 18:55:18,921 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 18:55:19,837 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:20,226 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:55:20,566 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:55:20,974 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:55:22,398 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:55:22,795 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:55:22,802 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:55:22,807 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 18:55:22,807 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:55:22,807 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:55:23,219 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:23,219 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 18:55:24,298 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 18:55:24,298 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 18:55:24,732 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 18:55:24,732 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 18:55:24,732 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 18:55:24,742 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 18:55:24,750 - main - INFO - Adding 26 exchange features
2025-04-16 18:55:24,750 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:55:24,750 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:55:24,755 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 18:55:24,759 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 18:55:24,762 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 18:55:24,764 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:55:24,764 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:55:24,764 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 18:55:24,764 - main - INFO - Multi-timeframe analysis: Trend direction: bearish, Strength: -0.33, Alignment: 0.67
2025-04-16 18:55:24,765 - market_regime - INFO - Initialized market regime detector
2025-04-16 18:55:24,765 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 18:55:24,765 - market_regime - INFO - Regime metrics - Trend strength: -0.33, Volatility: 0.30%, Alignment: 0.67
2025-04-16 18:55:24,765 - main - INFO - Detected market regime: low_volatility
2025-04-16 18:55:24,765 - main - INFO - Regime adjustments: {'leverage_factor': 0.7319368972891835, 'position_size_factor': 0.8150964279014943, 'stop_loss_factor': 1.1078747953827495, 'take_profit_factor': 0.9095175571831455, 'entry_confidence': 0.670777867079064}
2025-04-16 18:55:24,765 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:55:24,765 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.008621534725925454, 'orderbook_score': 0.0, 'volume_score': 0.07496725938597201, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.08358879411189747, 'confidence': 52.6121498159968, 'alignment': 62.5}
2025-04-16 18:55:24,765 - main - INFO - Signal-based decision: WAIT with confidence 52.61% and alignment 62.50%
2025-04-16 18:55:24,765 - main - INFO - Calculated signal scores: {'macd_score': 0.008621534725925454, 'orderbook_score': 0.0, 'volume_score': 0.07496725938597201, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.08358879411189747, 'confidence': 52.6121498159968, 'alignment': 62.5}
2025-04-16 18:55:24,766 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 18:55:24,766 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 18:55:24,766 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 18:55:24,766 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 18:55:24,766 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 18:55:24,766 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 18:55:24,767 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 18:55:24,767 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 18:55:24,767 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 18:55:25,892 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:26,560 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:55:27,566 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:55:27,982 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:55:29,220 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:55:29,803 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:55:29,806 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:55:30,216 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:30,634 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:55:31,007 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:55:31,423 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:55:32,016 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:55:33,554 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:55:33,568 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:55:38,769 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:55:38,769 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 18:55:38,769 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 18:55:39,803 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:40,193 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:55:40,871 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:55:41,295 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:55:42,258 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:55:42,668 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:55:42,673 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:55:42,673 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 18:55:42,673 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:55:42,673 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:55:43,073 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:43,073 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 18:55:43,489 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 18:55:43,489 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 18:55:43,903 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 18:55:43,903 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 18:55:43,903 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 18:55:43,903 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 18:55:43,922 - main - INFO - Adding 26 exchange features
2025-04-16 18:55:43,922 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:55:43,922 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:55:43,922 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 18:55:43,932 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 18:55:43,936 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 18:55:43,936 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:55:43,937 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:55:43,938 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 18:55:43,938 - main - INFO - Multi-timeframe analysis: Trend direction: bearish, Strength: -0.33, Alignment: 0.67
2025-04-16 18:55:43,938 - market_regime - INFO - Initialized market regime detector
2025-04-16 18:55:43,938 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 18:55:43,938 - market_regime - INFO - Regime metrics - Trend strength: -0.33, Volatility: 0.30%, Alignment: 0.67
2025-04-16 18:55:43,939 - main - INFO - Detected market regime: low_volatility
2025-04-16 18:55:43,939 - main - INFO - Regime adjustments: {'leverage_factor': 0.7319368972891835, 'position_size_factor': 0.8150964279014943, 'stop_loss_factor': 1.1078747953827495, 'take_profit_factor': 0.9095175571831455, 'entry_confidence': 0.670777867079064}
2025-04-16 18:55:43,939 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:55:43,939 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.008621534725925454, 'orderbook_score': 0.0, 'volume_score': 0.07496725938597201, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.08358879411189747, 'confidence': 52.6121498159968, 'alignment': 62.5}
2025-04-16 18:55:43,939 - main - INFO - Signal-based decision: WAIT with confidence 52.61% and alignment 62.50%
2025-04-16 18:55:43,939 - main - INFO - Calculated signal scores: {'macd_score': 0.008621534725925454, 'orderbook_score': 0.0, 'volume_score': 0.07496725938597201, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.08358879411189747, 'confidence': 52.6121498159968, 'alignment': 62.5}
2025-04-16 18:55:43,939 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 18:55:43,939 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 18:55:43,939 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 18:55:43,943 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 18:55:43,943 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 18:55:43,943 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 18:55:43,943 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 18:55:43,943 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 18:55:43,944 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 18:55:48,919 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:49,352 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:55:50,458 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:55:51,376 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:55:51,967 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:55:52,373 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:55:52,379 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:55:58,576 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:55:59,457 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:00,829 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:01,251 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:01,865 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:02,272 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:02,272 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:04,927 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:05,321 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:05,670 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:06,069 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:07,308 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:10,646 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:10,646 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:13,416 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:13,809 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:14,157 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:14,721 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:15,304 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:15,904 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:15,904 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:28,522 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:56:28,522 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 18:56:28,522 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 18:56:28,931 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:29,325 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:29,692 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:30,091 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:30,721 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:31,837 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:31,837 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:31,844 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 18:56:31,846 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:56:31,846 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:56:32,765 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:32,766 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 18:56:33,822 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 18:56:33,822 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 18:56:34,223 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 18:56:34,223 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 18:56:34,223 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 18:56:34,238 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 18:56:34,247 - main - INFO - Adding 26 exchange features
2025-04-16 18:56:34,247 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:56:34,247 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:56:34,253 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 18:56:34,257 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 18:56:34,260 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 18:56:34,260 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:56:34,260 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:56:34,260 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 18:56:34,260 - main - INFO - Multi-timeframe analysis: Trend direction: bearish, Strength: -0.33, Alignment: 0.67
2025-04-16 18:56:34,260 - market_regime - INFO - Initialized market regime detector
2025-04-16 18:56:34,263 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 18:56:34,263 - market_regime - INFO - Regime metrics - Trend strength: -0.33, Volatility: 0.30%, Alignment: 0.67
2025-04-16 18:56:34,263 - main - INFO - Detected market regime: low_volatility
2025-04-16 18:56:34,263 - main - INFO - Regime adjustments: {'leverage_factor': 0.7321358972892502, 'position_size_factor': 0.8152068758080552, 'stop_loss_factor': 1.107520393240469, 'take_profit_factor': 0.9093869573778438, 'entry_confidence': 0.6706892181014295}
2025-04-16 18:56:34,263 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:56:34,265 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.006959993663612294, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.006959993663612294, 'confidence': 50.217499801987884, 'alignment': 50.0}
2025-04-16 18:56:34,266 - main - INFO - Signal-based decision: WAIT with confidence 50.22% and alignment 50.00%
2025-04-16 18:56:34,266 - main - INFO - Calculated signal scores: {'macd_score': 0.006959993663612294, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.006959993663612294, 'confidence': 50.217499801987884, 'alignment': 50.0}
2025-04-16 18:56:34,266 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 18:56:34,266 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 18:56:34,268 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 18:56:34,268 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 18:56:34,268 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 18:56:34,268 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 18:56:34,268 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 18:56:34,268 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 18:56:34,268 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 18:56:34,627 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:35,331 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:35,691 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:36,087 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:36,671 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:37,073 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:37,073 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:44,107 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:44,532 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:44,894 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:45,298 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:45,887 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:46,287 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:46,289 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:48,431 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:48,848 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:49,206 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:49,607 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:50,199 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:50,595 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:50,602 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:56:53,546 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:56:53,970 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:56:54,979 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:56:55,389 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:56:56,004 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:56:56,406 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:56:56,421 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:03,581 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:04,505 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:04,914 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:05,436 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:06,044 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:06,536 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:06,536 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:13,434 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:13,865 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:14,222 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:14,619 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:15,570 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:15,977 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:15,981 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:19,954 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:20,388 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:20,886 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:21,322 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:22,565 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:22,967 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:22,975 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:27,475 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:57:27,475 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 18:57:27,475 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 18:57:28,649 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:29,035 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:29,793 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:30,186 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:30,885 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:31,437 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:31,437 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:31,437 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 18:57:31,437 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:57:31,437 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:57:31,836 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:31,836 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 18:57:32,199 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 18:57:32,199 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 18:57:32,568 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 18:57:32,568 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 18:57:32,568 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 18:57:32,578 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 18:57:32,589 - main - INFO - Adding 26 exchange features
2025-04-16 18:57:32,589 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:57:32,589 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:57:32,594 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 18:57:32,596 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 18:57:32,601 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 18:57:32,601 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:57:32,601 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bearish (score: -1.00)
2025-04-16 18:57:32,601 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 18:57:32,601 - main - INFO - Multi-timeframe analysis: Trend direction: bearish, Strength: -0.33, Alignment: 0.67
2025-04-16 18:57:32,602 - market_regime - INFO - Initialized market regime detector
2025-04-16 18:57:32,602 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 18:57:32,602 - market_regime - INFO - Regime metrics - Trend strength: -0.33, Volatility: 0.30%, Alignment: 0.67
2025-04-16 18:57:32,602 - main - INFO - Detected market regime: low_volatility
2025-04-16 18:57:32,602 - main - INFO - Regime adjustments: {'leverage_factor': 0.7324414823437189, 'position_size_factor': 0.8153764799765528, 'stop_loss_factor': 1.1069761721454117, 'take_profit_factor': 0.9091864078873741, 'entry_confidence': 0.6705530884398719}
2025-04-16 18:57:32,602 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:57:32,603 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.005934765661877596, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.005934765661877596, 'confidence': 50.18546142693368, 'alignment': 50.0}
2025-04-16 18:57:32,603 - main - INFO - Signal-based decision: WAIT with confidence 50.19% and alignment 50.00%
2025-04-16 18:57:32,603 - main - INFO - Calculated signal scores: {'macd_score': 0.005934765661877596, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.005934765661877596, 'confidence': 50.18546142693368, 'alignment': 50.0}
2025-04-16 18:57:32,604 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 18:57:32,605 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 18:57:32,605 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 18:57:32,605 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 18:57:32,605 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 18:57:32,605 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 18:57:32,605 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 18:57:32,605 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 18:57:32,605 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 18:57:33,470 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:33,891 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:34,253 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:34,708 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:35,293 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:35,694 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:35,704 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:43,521 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:43,922 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:44,285 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:44,744 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:45,362 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:45,768 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:45,771 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:54,471 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:54,883 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:55,870 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:56,267 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:57:56,854 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:57:57,253 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:57:57,261 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:57:58,437 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:57:58,970 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:57:59,335 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:57:59,800 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:00,418 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:00,930 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:00,933 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:03,486 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:04,354 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:04,712 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:05,103 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:06,344 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:06,732 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:06,743 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:08,438 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:08,836 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:09,195 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:09,600 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:10,187 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:10,591 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:10,591 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:18,547 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:19,057 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:19,412 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:19,973 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:20,568 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:20,964 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:20,967 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:26,531 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:58:26,534 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 18:58:26,534 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 18:58:26,896 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:27,870 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:28,226 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:28,942 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:29,536 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:29,915 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:29,931 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:29,932 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 18:58:29,932 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:58:29,932 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:58:30,286 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:30,286 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 18:58:30,666 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 18:58:30,667 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 18:58:31,035 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 18:58:31,036 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 18:58:31,036 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 18:58:31,037 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 18:58:31,049 - main - INFO - Adding 26 exchange features
2025-04-16 18:58:31,049 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:58:31,049 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:58:31,054 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 18:58:31,057 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 18:58:31,060 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 18:58:31,060 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 18:58:31,060 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 18:58:31,066 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 18:58:31,066 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 18:58:31,067 - market_regime - INFO - Initialized market regime detector
2025-04-16 18:58:31,067 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 18:58:31,067 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.30%, Alignment: 0.67
2025-04-16 18:58:31,068 - main - INFO - Detected market regime: low_volatility
2025-04-16 18:58:31,068 - main - INFO - Regime adjustments: {'leverage_factor': 0.7262868820061432, 'position_size_factor': 0.809795209763463, 'stop_loss_factor': 1.120370001988999, 'take_profit_factor': 0.8948320110364226, 'entry_confidence': 0.6766766732371797}
2025-04-16 18:58:31,068 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:58:31,069 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.007099131555775013, 'orderbook_score': 0.0, 'volume_score': -0.07422520489509966, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06712607333932465, 'confidence': 47.902310208146105, 'alignment': 62.5}
2025-04-16 18:58:31,070 - main - INFO - Signal-based decision: WAIT with confidence 47.90% and alignment 62.50%
2025-04-16 18:58:31,070 - main - INFO - Calculated signal scores: {'macd_score': 0.007099131555775013, 'orderbook_score': 0.0, 'volume_score': -0.07422520489509966, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06712607333932465, 'confidence': 47.902310208146105, 'alignment': 62.5}
2025-04-16 18:58:31,071 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 18:58:31,071 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 18:58:31,072 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 18:58:31,072 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 18:58:31,072 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 18:58:31,072 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 18:58:31,073 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 18:58:31,073 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 18:58:31,073 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 18:58:31,446 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:31,955 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:32,335 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:32,725 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:33,681 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:34,111 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:34,116 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:38,507 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:39,534 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:40,041 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:40,427 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:41,070 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:41,462 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:41,462 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:43,518 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:43,933 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:44,298 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:44,738 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:45,576 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:45,982 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:45,985 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:48,788 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:49,186 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:49,893 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:50,306 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:50,907 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:51,308 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:51,308 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:53,453 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:53,848 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:54,252 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:54,647 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:58:55,304 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:58:55,714 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:58:55,720 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:58:58,470 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:58:58,864 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:58:59,293 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:58:59,673 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:00,262 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:00,662 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:00,662 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:03,470 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:03,864 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:04,220 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:04,620 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:05,850 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:06,606 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:06,606 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:09,100 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:09,535 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:10,351 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:10,754 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:11,397 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:11,799 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:11,802 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:13,529 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:14,036 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:14,444 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:14,962 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:16,191 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:16,707 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:16,707 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:18,487 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:18,883 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:19,246 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:19,635 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:20,869 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:21,620 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:21,623 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:27,425 - data.exchange - INFO - Connected to htx exchange
2025-04-16 18:59:27,425 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 18:59:27,425 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 18:59:28,442 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:28,990 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:29,500 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:30,015 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:30,603 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:31,037 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:31,037 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:31,037 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 18:59:31,037 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:59:31,037 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:59:32,171 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:32,171 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 18:59:32,531 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 18:59:32,531 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 18:59:32,898 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 18:59:32,898 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 18:59:32,898 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 18:59:32,903 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 18:59:32,913 - main - INFO - Adding 26 exchange features
2025-04-16 18:59:32,913 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 18:59:32,913 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 18:59:32,918 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 18:59:32,921 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 18:59:32,922 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 18:59:32,922 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 18:59:32,922 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 18:59:32,931 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 18:59:32,931 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 18:59:32,931 - market_regime - INFO - Initialized market regime detector
2025-04-16 18:59:32,931 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 18:59:32,931 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.30%, Alignment: 0.67
2025-04-16 18:59:32,931 - main - INFO - Detected market regime: low_volatility
2025-04-16 18:59:32,932 - main - INFO - Regime adjustments: {'leverage_factor': 0.7259415580466049, 'position_size_factor': 0.8096005434197605, 'stop_loss_factor': 1.1209883728002656, 'take_profit_factor': 0.8950331020119305, 'entry_confidence': 0.6768352010269771}
2025-04-16 18:59:32,932 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 18:59:32,933 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.008118368170554568, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.008118368170554568, 'confidence': 50.253699005329835, 'alignment': 50.0}
2025-04-16 18:59:32,933 - main - INFO - Signal-based decision: WAIT with confidence 50.25% and alignment 50.00%
2025-04-16 18:59:32,933 - main - INFO - Calculated signal scores: {'macd_score': 0.008118368170554568, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.008118368170554568, 'confidence': 50.253699005329835, 'alignment': 50.0}
2025-04-16 18:59:32,935 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 18:59:32,935 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 18:59:32,935 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 18:59:32,935 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 18:59:32,936 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 18:59:32,936 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 18:59:32,937 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 18:59:32,937 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.11%, TP: 0.13% (ATR: 0.07%)
2025-04-16 18:59:32,937 - main - INFO - Using adaptive take profit: 0.13%
2025-04-16 18:59:32,937 - main - INFO - Using adaptive stop loss: 0.11%
2025-04-16 18:59:32,937 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 18:59:32,937 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 18:59:32,938 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 18:59:32,938 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Recent trades show buying activity. These factors suggest upward price movement.
2025-04-16 18:59:32,939 - main - INFO - Take Profit: 0.1325114795671584%
2025-04-16 18:59:32,939 - main - INFO - Stop Loss: 0.11484328229152771%
2025-04-16 18:59:33,310 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:33,711 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:34,063 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:34,521 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:35,105 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:35,534 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:35,549 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:38,522 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:38,924 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:39,278 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:39,669 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:40,273 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:40,661 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:40,673 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:43,534 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:44,253 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:44,619 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:44,996 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:45,586 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:45,969 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:45,969 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:48,646 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:49,060 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:49,413 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:49,805 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:50,417 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:50,913 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:50,913 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 18:59:53,671 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 18:59:54,594 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 18:59:54,960 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 18:59:55,409 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 18:59:56,035 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 18:59:56,534 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 18:59:56,534 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:03,542 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:03,955 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:04,328 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:05,241 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:05,820 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:06,567 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:06,567 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:08,865 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:09,608 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:10,618 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:11,030 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:11,696 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:12,114 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:12,119 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:14,254 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:14,653 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:15,020 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:15,432 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:16,669 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:17,086 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:17,089 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:18,597 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:19,066 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:19,421 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:19,678 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 19:00:19,884 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:20,134 - __main__ - INFO - Initializing components...
2025-04-16 19:00:20,479 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:20,652 - __main__ - INFO - Loading configuration...
2025-04-16 19:00:20,954 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:20,954 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:21,163 - __main__ - INFO - Connecting to data sources...
2025-04-16 19:00:21,669 - __main__ - INFO - Creating main window...
2025-04-16 19:00:21,673 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 19:00:21,673 - main_window - INFO - Initializing MainWindow
2025-04-16 19:00:21,673 - main_window - INFO - Creating QSettings
2025-04-16 19:00:21,673 - main_window - INFO - Creating ThemeManager
2025-04-16 19:00:21,673 - main_window - INFO - Creating NotificationManager
2025-04-16 19:00:21,679 - main_window - INFO - Calling init_ui
2025-04-16 19:00:21,679 - main_window - INFO - Setting window title and geometry
2025-04-16 19:00:21,679 - main_window - INFO - Creating menu bar
2025-04-16 19:00:21,679 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 19:00:21,679 - main_window - INFO - Creating central widget
2025-04-16 19:00:21,679 - main_window - INFO - Creating control toolbar
2025-04-16 19:00:21,679 - main_window - INFO - Adding bot buttons
2025-04-16 19:00:21,679 - main_window - INFO - Creating bot tab widget
2025-04-16 19:00:21,679 - main_window - INFO - Creating status bar
2025-04-16 19:00:21,679 - main_window - INFO - Applying theme
2025-04-16 19:00:21,692 - main_window - INFO - Restoring window geometry
2025-04-16 19:00:21,692 - main_window - INFO - Adding initial simulation bot
2025-04-16 19:00:21,692 - main_window - INFO - Adding new simulation bot
2025-04-16 19:00:21,692 - main_window - INFO - Using default bot name
2025-04-16 19:00:21,692 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 19:00:21,692 - main_window - INFO - Generating unique ID
2025-04-16 19:00:21,694 - main_window - INFO - Creating BotTabWidget with ID 995510bd-ee76-458a-8009-ddb84e05343e and name Simulation Bot 1
2025-04-16 19:00:23,707 - multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:00:23,707 - market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:00:23,707 - adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:00:23,720 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-16 19:00:27,954 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:00:27,954 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:00:27,954 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:00:28,328 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:29,156 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:29,506 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:29,906 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:30,106 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:00:30,107 - trade_lifecycle - INFO - Initialized trade lifecycle manager
2025-04-16 19:00:30,483 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:30,906 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:30,910 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:30,910 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:00:30,910 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:00:30,910 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:00:31,301 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:31,301 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:00:31,664 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:00:31,666 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:00:32,667 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:00:32,667 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:00:32,673 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:00:32,673 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:00:32,686 - main - INFO - Adding 26 exchange features
2025-04-16 19:00:32,686 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:00:32,686 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:00:32,690 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:00:32,694 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:00:32,698 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:00:32,698 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 19:00:32,699 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:00:32,699 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:00:32,699 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 19:00:32,699 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:00:32,699 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:00:32,699 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:00:32,700 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:00:32,700 - main - INFO - Regime adjustments: {'leverage_factor': 0.7288755584819381, 'position_size_factor': 0.8112545008744693, 'stop_loss_factor': 1.1157344650439713, 'take_profit_factor': 0.8933245585026108, 'entry_confidence': 0.6754882901294544}
2025-04-16 19:00:32,700 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:00:32,700 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.0074144532563755385, 'orderbook_score': 0.0, 'volume_score': 0.06033983754957124, 'price_action_score': -0.06006504181166991, 'trend_score': 0.0, 'total_score': 0.007689248994276865, 'confidence': 50.24028903107115, 'alignment': 50.0}
2025-04-16 19:00:32,701 - main - INFO - Signal-based decision: WAIT with confidence 50.24% and alignment 50.00%
2025-04-16 19:00:32,701 - main - INFO - Calculated signal scores: {'macd_score': 0.0074144532563755385, 'orderbook_score': 0.0, 'volume_score': 0.06033983754957124, 'price_action_score': -0.06006504181166991, 'trend_score': 0.0, 'total_score': 0.007689248994276865, 'confidence': 50.24028903107115, 'alignment': 50.0}
2025-04-16 19:00:32,701 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:00:32,701 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:00:32,702 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:00:32,702 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:00:32,702 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:00:32,702 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:00:32,702 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:00:32,703 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.11%, TP: 0.12% (ATR: 0.06%)
2025-04-16 19:00:32,703 - main - INFO - Using adaptive take profit: 0.12%
2025-04-16 19:00:32,703 - main - INFO - Using adaptive stop loss: 0.11%
2025-04-16 19:00:32,703 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:00:32,703 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:00:32,703 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 19:00:32,703 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Order book shows buying pressure. These factors suggest upward price movement.
2025-04-16 19:00:32,703 - main - INFO - Take Profit: 0.12327104982159175%
2025-04-16 19:00:32,703 - main - INFO - Stop Loss: 0.10683490984538548%
2025-04-16 19:00:33,069 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:33,489 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:33,840 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:34,131 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:00:34,134 - dynamic_targets - INFO - Initialized dynamic target calculator with htx exchange
2025-04-16 19:00:34,134 - dynamic_targets - INFO - Fee rate: 0.0400%
2025-04-16 19:00:34,134 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:00:34,256 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:34,835 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:35,066 - data.exchange - INFO - Fetched 2000 5m candles for DOGE/USDT:USDT (spot)
2025-04-16 19:00:35,074 - dynamic_targets - INFO - Calculated dynamic targets for DOGE/USDT:USDT with 20.00x leverage:
2025-04-16 19:00:35,074 - dynamic_targets - INFO - Take profit: 3.92%, Stop loss: 0.62%
2025-04-16 19:00:35,128 - main_window - INFO - BotTabWidget created successfully
2025-04-16 19:00:35,128 - main_window - INFO - Adding to tab widget
2025-04-16 19:00:35,154 - main_window - INFO - Updating status bar
2025-04-16 19:00:35,154 - __main__ - INFO - MainWindow initialized successfully
2025-04-16 19:00:35,268 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:35,268 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:38,619 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:39,135 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:39,538 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:39,963 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:40,542 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:41,085 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:41,087 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:48,595 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:49,481 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:49,878 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:50,270 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:50,902 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:51,321 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:51,331 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:53,673 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:54,186 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:54,596 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:00:55,001 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:00:55,613 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:00:56,041 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:00:56,041 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:00:58,687 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:00:59,081 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:00:59,622 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:00,119 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:00,707 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:01,155 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:01,155 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:08,719 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:09,151 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:09,547 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:10,060 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:10,734 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:11,186 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:11,186 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:13,632 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:14,156 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:15,278 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:15,692 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:16,313 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:16,716 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:16,716 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:18,657 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:19,079 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:19,438 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:19,889 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:20,481 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:20,899 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:20,905 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:27,367 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:01:27,367 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:01:27,367 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:01:28,390 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:28,800 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:29,301 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:29,720 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:30,292 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:30,725 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:30,725 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:30,725 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:01:30,733 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:01:30,733 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:01:31,720 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:31,720 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:01:32,085 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:01:32,085 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:01:32,862 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:01:32,862 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:01:32,862 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:01:32,876 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:01:32,885 - main - INFO - Adding 26 exchange features
2025-04-16 19:01:32,885 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:01:32,885 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:01:32,893 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:01:32,897 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:01:32,900 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:01:32,901 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 19:01:32,901 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:01:32,901 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:01:32,901 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 19:01:32,903 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:01:32,903 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:01:32,903 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:01:32,903 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:01:32,903 - main - INFO - Regime adjustments: {'leverage_factor': 0.7283224730123379, 'position_size_factor': 0.810942715019048, 'stop_loss_factor': 1.116724873908139, 'take_profit_factor': 0.8936466343202571, 'entry_confidence': 0.6757421949473591}
2025-04-16 19:01:32,903 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:01:32,904 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.00505205124319931, 'orderbook_score': 0.0, 'volume_score': -0.06595704415890902, 'price_action_score': 0.05004273371696484, 'trend_score': 0.0, 'total_score': -0.010862259198744867, 'confidence': 49.660554400039224, 'alignment': 50.0}
2025-04-16 19:01:32,904 - main - INFO - Signal-based decision: WAIT with confidence 49.66% and alignment 50.00%
2025-04-16 19:01:32,904 - main - INFO - Calculated signal scores: {'macd_score': 0.00505205124319931, 'orderbook_score': 0.0, 'volume_score': -0.06595704415890902, 'price_action_score': 0.05004273371696484, 'trend_score': 0.0, 'total_score': -0.010862259198744867, 'confidence': 49.660554400039224, 'alignment': 50.0}
2025-04-16 19:01:32,905 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:01:32,906 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:01:32,906 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:01:32,906 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:01:32,906 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:01:32,906 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:01:32,906 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:01:32,909 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.12%, TP: 0.14% (ATR: 0.07%)
2025-04-16 19:01:32,909 - main - INFO - Using adaptive take profit: 0.14%
2025-04-16 19:01:32,909 - main - INFO - Using adaptive stop loss: 0.12%
2025-04-16 19:01:32,909 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:01:32,909 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:01:32,909 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 19:01:32,909 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Spot market has higher buy ratio than futures, suggesting potential upward movement. These factors suggest upward price movement.
2025-04-16 19:01:32,909 - main - INFO - Take Profit: 0.13516229617323625%
2025-04-16 19:01:32,909 - main - INFO - Stop Loss: 0.11714065668346424%
2025-04-16 19:01:33,636 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:34,020 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:34,381 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:34,753 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:35,343 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:35,736 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:35,751 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:38,687 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:39,103 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:39,462 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:40,322 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:40,990 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:41,402 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:41,404 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:43,752 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:44,670 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:45,072 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:45,487 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:46,101 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:46,619 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:46,619 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:48,667 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:49,075 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:49,436 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:49,898 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:01:50,512 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:01:50,922 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:01:50,922 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:01:58,649 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:01:59,044 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:01:59,398 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:01:59,784 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:00,381 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:01,149 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:01,165 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:09,302 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:09,697 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:11,049 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:11,452 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:12,054 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:12,452 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:12,463 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:13,666 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:14,058 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:15,057 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:15,440 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:16,026 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:16,430 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:16,430 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:27,395 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:02:27,396 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:02:27,396 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:02:27,756 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:28,153 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:28,502 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:28,905 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:29,913 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:30,298 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:30,298 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:30,315 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:02:30,315 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:02:30,315 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:02:30,668 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:30,668 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:02:32,000 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:02:32,000 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:02:32,361 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:02:32,361 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:02:32,361 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:02:32,368 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:02:32,377 - main - INFO - Adding 26 exchange features
2025-04-16 19:02:32,378 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:02:32,378 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:02:32,383 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:02:32,388 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:02:32,392 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:02:32,392 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 19:02:32,392 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:02:32,392 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:02:32,392 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 19:02:32,392 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:02:32,392 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:02:32,393 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:02:32,393 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:02:32,393 - main - INFO - Regime adjustments: {'leverage_factor': 0.727674703313952, 'position_size_factor': 0.8105775536820976, 'stop_loss_factor': 1.1178848336005975, 'take_profit_factor': 0.8940238471864802, 'entry_confidence': 0.6760395664321531}
2025-04-16 19:02:32,393 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:02:32,393 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.002535768568000657, 'orderbook_score': 0.0, 'volume_score': -0.0745249432340622, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07198917466606154, 'confidence': 47.75033829168558, 'alignment': 62.5}
2025-04-16 19:02:32,394 - main - INFO - Signal-based decision: WAIT with confidence 47.75% and alignment 62.50%
2025-04-16 19:02:32,394 - main - INFO - Calculated signal scores: {'macd_score': 0.002535768568000657, 'orderbook_score': 0.0, 'volume_score': -0.0745249432340622, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07198917466606154, 'confidence': 47.75033829168558, 'alignment': 62.5}
2025-04-16 19:02:32,394 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:02:32,394 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:02:32,395 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:02:32,395 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:02:32,395 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:02:32,395 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:02:32,395 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:02:32,397 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.12%, TP: 0.14% (ATR: 0.07%)
2025-04-16 19:02:32,397 - main - INFO - Using adaptive take profit: 0.14%
2025-04-16 19:02:32,397 - main - INFO - Using adaptive stop loss: 0.12%
2025-04-16 19:02:32,397 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:02:32,398 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:02:32,398 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 19:02:32,398 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Spot market has higher buy ratio than futures, suggesting potential upward movement. These factors suggest upward price movement.
2025-04-16 19:02:32,398 - main - INFO - Take Profit: 0.1408976693792005%
2025-04-16 19:02:32,398 - main - INFO - Stop Loss: 0.12211131346197376%
2025-04-16 19:02:32,775 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:33,487 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:34,164 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:35,038 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:35,623 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:36,025 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:36,027 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:38,737 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:39,131 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:39,549 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:40,419 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:41,002 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:41,513 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:41,513 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:43,753 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:44,138 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:44,570 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:44,986 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:45,601 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:46,020 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:46,036 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:53,684 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:54,064 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:02:54,433 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:02:54,818 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:02:55,404 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:02:56,190 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:02:56,190 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:02:58,684 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:02:59,099 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:00,104 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:00,550 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:01,786 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:02,295 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:02,295 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:03,818 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:04,243 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:04,606 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:05,003 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:05,597 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:06,081 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:06,092 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:08,850 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:09,255 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:09,661 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:10,070 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:10,667 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:11,070 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:11,071 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:13,750 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:14,148 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:14,573 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:14,991 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:16,219 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:16,932 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:16,932 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:27,385 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:03:27,385 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:03:27,385 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:03:27,745 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:28,188 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:29,220 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:29,644 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:30,253 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:30,653 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:30,669 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:30,674 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:03:30,674 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:03:30,674 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:03:30,942 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 19:03:31,385 - __main__ - INFO - Initializing components...
2025-04-16 19:03:31,685 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:31,685 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:03:31,916 - __main__ - INFO - Loading configuration...
2025-04-16 19:03:32,053 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:03:32,053 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:03:32,421 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:03:32,421 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:03:32,421 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:03:32,433 - __main__ - INFO - Connecting to data sources...
2025-04-16 19:03:32,433 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:03:32,449 - main - INFO - Adding 26 exchange features
2025-04-16 19:03:32,449 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:03:32,449 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:03:32,456 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:03:32,463 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:03:32,463 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:03:32,463 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 19:03:32,463 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:03:32,463 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:03:32,463 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 19:03:32,463 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:03:32,463 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:03:32,470 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:03:32,470 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:03:32,470 - main - INFO - Regime adjustments: {'leverage_factor': 0.7277051841284061, 'position_size_factor': 0.810594736355176, 'stop_loss_factor': 1.1178302516770404, 'take_profit_factor': 0.8940060974284817, 'entry_confidence': 0.6760255736117503}
2025-04-16 19:03:32,470 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:03:32,471 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 6.984114599798036e-05, 'orderbook_score': 0.0, 'volume_score': 0.07414376256682716, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.07421360371282514, 'confidence': 52.31917511602578, 'alignment': 62.5}
2025-04-16 19:03:32,471 - main - INFO - Signal-based decision: WAIT with confidence 52.32% and alignment 62.50%
2025-04-16 19:03:32,471 - main - INFO - Calculated signal scores: {'macd_score': 6.984114599798036e-05, 'orderbook_score': 0.0, 'volume_score': 0.07414376256682716, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.07421360371282514, 'confidence': 52.31917511602578, 'alignment': 62.5}
2025-04-16 19:03:32,472 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:03:32,472 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:03:32,472 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:03:32,472 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:03:32,474 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:03:32,474 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:03:32,474 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:03:32,475 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.13%, TP: 0.15% (ATR: 0.07%)
2025-04-16 19:03:32,475 - main - INFO - Using adaptive take profit: 0.15%
2025-04-16 19:03:32,476 - main - INFO - Using adaptive stop loss: 0.13%
2025-04-16 19:03:32,476 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:03:32,476 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:03:32,476 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 19:03:32,476 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Spot market has higher buy ratio than futures, suggesting potential upward movement. These factors suggest upward price movement.
2025-04-16 19:03:32,476 - main - INFO - Take Profit: 0.1479060635027301%
2025-04-16 19:03:32,476 - main - INFO - Stop Loss: 0.12818525503570063%
2025-04-16 19:03:32,887 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:32,935 - __main__ - INFO - Creating main window...
2025-04-16 19:03:32,936 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 19:03:32,936 - main_window - INFO - Initializing MainWindow
2025-04-16 19:03:32,936 - main_window - INFO - Creating QSettings
2025-04-16 19:03:32,936 - main_window - INFO - Creating ThemeManager
2025-04-16 19:03:32,936 - main_window - INFO - Creating NotificationManager
2025-04-16 19:03:32,936 - main_window - INFO - Calling init_ui
2025-04-16 19:03:32,936 - main_window - INFO - Setting window title and geometry
2025-04-16 19:03:32,936 - main_window - INFO - Creating menu bar
2025-04-16 19:03:32,936 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 19:03:32,936 - main_window - INFO - Creating central widget
2025-04-16 19:03:32,936 - main_window - INFO - Creating control toolbar
2025-04-16 19:03:32,936 - main_window - INFO - Adding bot buttons
2025-04-16 19:03:32,936 - main_window - INFO - Creating bot tab widget
2025-04-16 19:03:32,936 - main_window - INFO - Creating status bar
2025-04-16 19:03:32,936 - main_window - INFO - Applying theme
2025-04-16 19:03:32,949 - main_window - INFO - Restoring window geometry
2025-04-16 19:03:32,949 - main_window - INFO - Adding initial simulation bot
2025-04-16 19:03:32,952 - main_window - INFO - Adding new simulation bot
2025-04-16 19:03:32,952 - main_window - INFO - Using default bot name
2025-04-16 19:03:32,952 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 19:03:32,952 - main_window - INFO - Generating unique ID
2025-04-16 19:03:32,952 - main_window - INFO - Creating BotTabWidget with ID 88dedc2a-1a85-4162-be32-310be164f50d and name Simulation Bot 1
2025-04-16 19:03:33,280 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:33,636 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:34,885 - multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:03:34,885 - market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:03:34,885 - adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:03:34,885 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-16 19:03:35,119 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:35,718 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:36,128 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:36,134 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:38,735 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:39,178 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:39,207 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:03:39,209 - trade_lifecycle - INFO - Initialized trade lifecycle manager
2025-04-16 19:03:39,559 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:40,005 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:40,594 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:41,002 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:41,005 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:43,756 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:44,140 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:44,227 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:03:44,227 - dynamic_targets - INFO - Initialized dynamic target calculator with htx exchange
2025-04-16 19:03:44,227 - dynamic_targets - INFO - Fee rate: 0.0400%
2025-04-16 19:03:44,227 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:03:44,516 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:44,945 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:45,278 - data.exchange - INFO - Fetched 2000 5m candles for DOGE/USDT:USDT (spot)
2025-04-16 19:03:45,280 - dynamic_targets - INFO - Calculated dynamic targets for DOGE/USDT:USDT with 20.00x leverage:
2025-04-16 19:03:45,280 - dynamic_targets - INFO - Take profit: 3.92%, Stop loss: 0.62%
2025-04-16 19:03:45,342 - main_window - INFO - BotTabWidget created successfully
2025-04-16 19:03:45,342 - main_window - INFO - Adding to tab widget
2025-04-16 19:03:45,364 - main_window - INFO - Updating status bar
2025-04-16 19:03:45,365 - __main__ - INFO - MainWindow initialized successfully
2025-04-16 19:03:45,579 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:46,106 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:46,111 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:54,452 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:54,901 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:03:55,921 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:03:56,322 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:03:56,919 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:03:57,992 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:03:57,992 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:03:59,372 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:03:59,774 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:00,557 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:01,280 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:01,877 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:02,269 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:02,269 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:03,772 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:04,520 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:04,870 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:05,268 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:05,867 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:06,276 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:06,276 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:08,768 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:09,165 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:09,531 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:09,923 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:10,501 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:10,902 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:10,911 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:18,784 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:19,167 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:19,535 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:19,936 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:20,633 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:21,033 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:21,033 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:27,179 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:04:27,179 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:04:27,179 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:04:27,590 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:28,000 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:28,402 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:28,820 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:29,417 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:29,819 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:29,819 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:29,819 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:04:29,819 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:04:29,819 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:04:30,876 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:30,876 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:04:31,233 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:04:31,233 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:04:31,692 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:04:31,692 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:04:31,694 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:04:31,704 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:04:31,721 - main - INFO - Adding 26 exchange features
2025-04-16 19:04:31,721 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:04:31,721 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:04:31,731 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:04:31,739 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:04:31,746 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:04:31,747 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bullish (score: 0.33)
2025-04-16 19:04:31,747 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:04:31,748 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:04:31,748 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.13, Alignment: 0.67
2025-04-16 19:04:31,749 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:04:31,749 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:04:31,749 - market_regime - INFO - Regime metrics - Trend strength: 0.13, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:04:31,749 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:04:31,750 - main - INFO - Regime adjustments: {'leverage_factor': 0.7280053992123856, 'position_size_factor': 0.8107639738815866, 'stop_loss_factor': 1.1172926572243327, 'take_profit_factor': 0.8938312745051595, 'entry_confidence': 0.6758877539429652}
2025-04-16 19:04:31,750 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:04:31,750 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.0016795130521784073, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.0016795130521784073, 'confidence': 49.94751521711942, 'alignment': 50.0}
2025-04-16 19:04:31,751 - main - INFO - Signal-based decision: WAIT with confidence 49.95% and alignment 50.00%
2025-04-16 19:04:31,751 - main - INFO - Calculated signal scores: {'macd_score': -0.0016795130521784073, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.0016795130521784073, 'confidence': 49.94751521711942, 'alignment': 50.0}
2025-04-16 19:04:31,753 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:04:31,753 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:04:31,753 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:04:31,753 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:04:31,754 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:04:31,754 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:04:31,754 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:04:31,755 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.12%, TP: 0.14% (ATR: 0.07%)
2025-04-16 19:04:31,756 - main - INFO - Using adaptive take profit: 0.14%
2025-04-16 19:04:31,756 - main - INFO - Using adaptive stop loss: 0.12%
2025-04-16 19:04:31,756 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:04:31,756 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:04:31,756 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 19:04:31,756 - main - INFO - Explanation: Multiple bullish signals detected: Recent trades show buying activity. Spot market has higher buy ratio than futures, suggesting potential upward movement. These factors suggest upward price movement.
2025-04-16 19:04:31,756 - main - INFO - Take Profit: 0.14200089315702621%
2025-04-16 19:04:31,756 - main - INFO - Stop Loss: 0.12306744073608937%
2025-04-16 19:04:32,137 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:32,553 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:32,902 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:33,324 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:33,939 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:34,414 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:34,414 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:38,948 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:39,467 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:39,881 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:40,284 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:41,009 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:41,412 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:41,412 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:43,870 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:44,381 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:44,795 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:45,410 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:46,000 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:46,411 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:46,411 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:49,511 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:49,932 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:50,288 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:04:50,737 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:04:51,971 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:04:52,404 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:04:52,404 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:04:58,858 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:04:59,318 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:04:59,744 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:00,143 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:00,735 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:01,136 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:01,138 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:03,874 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:04,275 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:05,003 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:05,409 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:06,001 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:06,406 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:06,406 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:09,517 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:09,988 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:10,346 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:10,799 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:11,422 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:11,836 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:11,846 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:13,896 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:14,291 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:14,662 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:15,041 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:15,639 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:16,039 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:16,042 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:19,001 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:19,423 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:19,805 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:20,223 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:20,816 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:21,220 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:21,226 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:27,648 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:05:27,648 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:05:27,648 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:05:28,007 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:28,422 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:28,788 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:29,236 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:29,856 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:30,779 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:30,779 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:30,793 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:05:30,794 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:05:30,794 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:05:31,891 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:31,891 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:05:32,925 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:05:32,925 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:05:33,286 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:05:33,286 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:05:33,287 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:05:33,293 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:05:33,301 - main - INFO - Adding 26 exchange features
2025-04-16 19:05:33,301 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:05:33,302 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:05:33,308 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:05:33,312 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:05:33,319 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:05:33,319 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:05:33,319 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:05:33,320 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:05:33,320 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.27, Alignment: 0.33
2025-04-16 19:05:33,320 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:05:33,320 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:05:33,321 - market_regime - INFO - Regime metrics - Trend strength: 0.27, Volatility: 0.29%, Alignment: 0.33
2025-04-16 19:05:33,321 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:05:33,321 - main - INFO - Regime adjustments: {'leverage_factor': 0.7184516068052522, 'position_size_factor': 0.7993328306363843, 'stop_loss_factor': 1.129849169367479, 'take_profit_factor': 0.8934185770230981, 'entry_confidence': 0.6840433608526075}
2025-04-16 19:05:33,321 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:05:33,322 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -9.300371729917328e-05, 'orderbook_score': 0.0, 'volume_score': -0.044606656739072714, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.04469966045637189, 'confidence': 48.60313561073838, 'alignment': 50.0}
2025-04-16 19:05:33,322 - main - INFO - Signal-based decision: WAIT with confidence 48.60% and alignment 50.00%
2025-04-16 19:05:33,322 - main - INFO - Calculated signal scores: {'macd_score': -9.300371729917328e-05, 'orderbook_score': 0.0, 'volume_score': -0.044606656739072714, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.04469966045637189, 'confidence': 48.60313561073838, 'alignment': 50.0}
2025-04-16 19:05:33,322 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:05:33,322 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:05:33,323 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:05:33,323 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:05:33,323 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:05:33,323 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:05:33,323 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:05:33,323 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 19:05:33,324 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 19:05:33,707 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:34,120 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:34,471 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:34,892 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:35,492 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:35,900 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:35,900 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:38,965 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:39,469 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:39,839 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:40,300 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:41,117 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:41,631 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:41,631 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:49,712 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:50,130 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:51,236 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:51,635 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:52,389 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:52,796 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:52,796 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:05:54,601 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:05:55,034 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:05:55,405 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:05:55,823 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:05:57,065 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:05:57,498 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:05:57,498 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:03,949 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:04,354 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:04,768 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:05,170 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:06,524 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:06,940 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:06,946 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:13,989 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:14,493 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:14,906 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:15,318 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:15,921 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:16,331 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:16,331 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:19,204 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:21,473 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:21,871 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:22,279 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:24,666 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:25,091 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:25,091 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:29,964 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:06:29,964 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:06:29,964 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:06:30,370 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:30,768 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:31,777 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:32,226 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:32,812 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:33,226 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:33,232 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:33,232 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:06:33,232 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:06:33,232 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:06:33,650 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:33,650 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:06:34,003 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:06:34,003 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:06:34,469 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:06:34,469 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:06:34,469 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:06:34,474 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:06:34,482 - main - INFO - Adding 26 exchange features
2025-04-16 19:06:34,482 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:06:34,483 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:06:34,488 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:06:34,493 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:06:34,497 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:06:34,497 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:06:34,498 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:06:34,499 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:06:34,500 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.27, Alignment: 0.33
2025-04-16 19:06:34,500 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:06:34,501 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:06:34,501 - market_regime - INFO - Regime metrics - Trend strength: 0.27, Volatility: 0.29%, Alignment: 0.33
2025-04-16 19:06:34,501 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:06:34,501 - main - INFO - Regime adjustments: {'leverage_factor': 0.7187017259091485, 'position_size_factor': 0.7994797219769167, 'stop_loss_factor': 1.129405719049068, 'take_profit_factor': 0.8932787526199112, 'entry_confidence': 0.6839248634567657}
2025-04-16 19:06:34,501 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:06:34,502 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.0005627641086177424, 'orderbook_score': 0.0, 'volume_score': -0.0028643377022632517, 'price_action_score': 0.06007179928365415, 'trend_score': 0.0, 'total_score': 0.056644697472773155, 'confidence': 51.770146796024164, 'alignment': 62.5}
2025-04-16 19:06:34,502 - main - INFO - Signal-based decision: WAIT with confidence 51.77% and alignment 62.50%
2025-04-16 19:06:34,503 - main - INFO - Calculated signal scores: {'macd_score': -0.0005627641086177424, 'orderbook_score': 0.0, 'volume_score': -0.0028643377022632517, 'price_action_score': 0.06007179928365415, 'trend_score': 0.0, 'total_score': 0.056644697472773155, 'confidence': 51.770146796024164, 'alignment': 62.5}
2025-04-16 19:06:34,504 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:06:34,504 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:06:34,505 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:06:34,505 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:06:34,505 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:06:34,506 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:06:34,506 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:06:34,507 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.12%, TP: 0.14% (ATR: 0.07%)
2025-04-16 19:06:34,508 - main - INFO - Using adaptive take profit: 0.14%
2025-04-16 19:06:34,508 - main - INFO - Using adaptive stop loss: 0.12%
2025-04-16 19:06:34,508 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:06:34,508 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:06:34,509 - main - INFO - Decision for DOGE/USDT:USDT: SHORT
2025-04-16 19:06:34,509 - main - INFO - Explanation: Multiple bearish signals detected: MACD shows bearish momentum. Order book shows selling pressure. These factors suggest downward price movement.
2025-04-16 19:06:34,509 - main - INFO - Take Profit: 0.14147825910864192%
2025-04-16 19:06:34,509 - main - INFO - Stop Loss: 0.12261449122749324%
2025-04-16 19:06:38,965 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:39,372 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:39,731 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:40,121 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:40,925 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:41,337 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:41,337 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:43,997 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:44,517 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:44,955 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:45,417 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:46,249 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:46,657 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:46,659 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:06:54,642 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:06:55,053 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:06:56,075 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:06:56,585 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:06:57,207 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:06:57,714 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:06:57,721 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:03,953 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:04,352 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:04,723 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:05,118 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:05,712 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:06,708 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:06,709 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:08,968 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:09,383 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:10,611 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:11,018 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:11,643 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:12,153 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:12,155 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:13,914 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:14,308 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:14,671 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:15,304 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:15,943 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:16,352 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:16,352 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:27,486 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:07:27,486 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:07:27,486 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:07:27,865 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:28,334 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:28,731 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:29,148 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:29,725 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:30,180 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:30,180 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:30,180 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:07:30,180 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:07:30,180 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:07:31,296 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:31,296 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:07:32,332 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:07:32,332 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:07:32,735 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:07:32,735 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:07:32,735 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:07:32,738 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:07:32,757 - main - INFO - Adding 26 exchange features
2025-04-16 19:07:32,757 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:07:32,757 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:07:32,763 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:07:32,767 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:07:32,771 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:07:32,772 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:07:32,772 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 19:07:32,773 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:07:32,773 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.27, Alignment: 0.33
2025-04-16 19:07:32,773 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:07:32,773 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:07:32,774 - market_regime - INFO - Regime metrics - Trend strength: 0.27, Volatility: 0.30%, Alignment: 0.33
2025-04-16 19:07:32,774 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:07:32,775 - main - INFO - Regime adjustments: {'leverage_factor': 0.7182255606234229, 'position_size_factor': 0.7992000769756129, 'stop_loss_factor': 1.1302499394396026, 'take_profit_factor': 0.8935449439098123, 'entry_confidence': 0.6841504533676117}
2025-04-16 19:07:32,775 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:07:32,776 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.0011927149222355205, 'orderbook_score': 0.0, 'volume_score': -0.07489452490194531, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07608723982418084, 'confidence': 47.622273755494355, 'alignment': 62.5}
2025-04-16 19:07:32,776 - main - INFO - Signal-based decision: WAIT with confidence 47.62% and alignment 62.50%
2025-04-16 19:07:32,776 - main - INFO - Calculated signal scores: {'macd_score': -0.0011927149222355205, 'orderbook_score': 0.0, 'volume_score': -0.07489452490194531, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07608723982418084, 'confidence': 47.622273755494355, 'alignment': 62.5}
2025-04-16 19:07:32,778 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:07:32,778 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:07:32,779 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:07:32,779 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:07:32,779 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:07:32,779 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:07:32,779 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:07:32,780 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 19:07:32,780 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 19:07:33,191 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:33,654 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:34,050 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:34,464 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:35,094 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:35,497 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:35,497 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:38,913 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:39,390 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:39,896 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:40,411 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:41,334 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:41,854 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:41,861 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:49,008 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:49,417 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:49,832 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:50,243 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:50,969 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:51,378 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:51,382 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:07:54,635 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:07:55,042 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:07:56,076 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:07:56,493 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:07:57,091 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:07:57,517 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:07:57,522 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:04,064 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:04,582 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:05,578 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:05,985 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:06,624 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:07,049 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:07,060 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:08,953 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:09,496 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:09,854 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:10,316 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:10,931 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:11,447 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:11,447 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:13,998 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:14,402 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:14,819 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:15,538 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:16,159 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:16,662 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:16,662 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:27,239 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:08:27,239 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:08:27,239 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:08:27,602 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:27,987 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:29,042 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:29,461 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:30,085 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:30,495 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:30,498 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:30,501 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:08:30,501 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:08:30,501 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:08:30,870 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:30,870 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:08:31,412 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:08:31,412 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:08:32,432 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:08:32,432 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:08:32,432 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:08:32,436 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:08:32,446 - main - INFO - Adding 26 exchange features
2025-04-16 19:08:32,446 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:08:32,447 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:08:32,450 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:08:32,454 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:08:32,457 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:08:32,458 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:08:32,458 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 19:08:32,459 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:08:32,459 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.47, Alignment: 0.67
2025-04-16 19:08:32,459 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:08:32,459 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:08:32,459 - market_regime - INFO - Regime metrics - Trend strength: 0.47, Volatility: 0.30%, Alignment: 0.67
2025-04-16 19:08:32,459 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:08:32,459 - main - INFO - Regime adjustments: {'leverage_factor': 0.7330074252703409, 'position_size_factor': 0.815037849027177, 'stop_loss_factor': 1.1033688784540343, 'take_profit_factor': 0.9166016295891628, 'entry_confidence': 0.6696540154701811}
2025-04-16 19:08:32,459 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:08:32,460 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.002203126389719611, 'orderbook_score': 0.0, 'volume_score': -0.012899966786691608, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.015103093176411219, 'confidence': 49.52802833823715, 'alignment': 50.0}
2025-04-16 19:08:32,460 - main - INFO - Signal-based decision: WAIT with confidence 49.53% and alignment 50.00%
2025-04-16 19:08:32,460 - main - INFO - Calculated signal scores: {'macd_score': -0.002203126389719611, 'orderbook_score': 0.0, 'volume_score': -0.012899966786691608, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.015103093176411219, 'confidence': 49.52802833823715, 'alignment': 50.0}
2025-04-16 19:08:32,461 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:08:32,461 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:08:32,461 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:08:32,461 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:08:32,461 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:08:32,461 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:08:32,462 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:08:32,462 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 19:08:32,462 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 19:08:33,474 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:33,890 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:34,373 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:34,764 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:35,357 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:35,768 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:35,768 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:39,090 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:39,539 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:39,910 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:40,318 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:40,897 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:41,347 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:41,347 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:44,827 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:45,331 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:46,457 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:46,895 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:47,500 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:47,898 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:47,901 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:49,025 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:08:49,417 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:08:49,837 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:08:50,354 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:08:50,942 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:08:51,360 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:08:51,361 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:08:59,655 - data.exchange - INFO - Fetched 99 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:00,093 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:00,490 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:01,002 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:01,597 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:02,133 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:02,136 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:03,977 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:04,388 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:04,785 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:05,294 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:05,924 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:06,429 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:06,429 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:09,086 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:09,709 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:10,070 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:10,519 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:11,150 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:11,565 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:11,581 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:19,128 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:19,556 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:20,046 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:20,559 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:21,873 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:22,310 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:22,314 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:23,004 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:09:23,004 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:09:23,004 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:09:23,396 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:23,802 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:24,157 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:24,555 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:25,132 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:26,134 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:26,142 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:26,147 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:09:26,147 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:26,147 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:09:26,527 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:26,527 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:09:26,899 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:09:26,899 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:09:27,321 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:09:27,321 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:09:27,321 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:27,326 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:09:27,335 - main - INFO - Adding 26 exchange features
2025-04-16 19:09:27,335 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:27,335 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:09:27,340 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:09:27,344 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:09:27,348 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:09:27,349 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:09:27,349 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 19:09:27,350 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:09:27,350 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.47, Alignment: 0.67
2025-04-16 19:09:27,350 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:09:27,350 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:09:27,350 - market_regime - INFO - Regime metrics - Trend strength: 0.47, Volatility: 0.30%, Alignment: 0.67
2025-04-16 19:09:27,350 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:09:27,350 - main - INFO - Regime adjustments: {'leverage_factor': 0.7331229624665516, 'position_size_factor': 0.8151022855022851, 'stop_loss_factor': 1.1031643575541021, 'take_profit_factor': 0.9165220871272158, 'entry_confidence': 0.6696028557413175}
2025-04-16 19:09:27,350 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:09:27,350 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.004061935867721104, 'orderbook_score': 0.0, 'volume_score': -0.07497891719417028, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07904085306189139, 'confidence': 47.5299733418159, 'alignment': 62.5}
2025-04-16 19:09:27,352 - main - INFO - Signal-based decision: WAIT with confidence 47.53% and alignment 62.50%
2025-04-16 19:09:27,352 - main - INFO - Calculated signal scores: {'macd_score': -0.004061935867721104, 'orderbook_score': 0.0, 'volume_score': -0.07497891719417028, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07904085306189139, 'confidence': 47.5299733418159, 'alignment': 62.5}
2025-04-16 19:09:27,353 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:09:27,353 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:09:27,353 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:09:27,353 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:09:27,353 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:09:27,353 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:09:27,354 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:09:27,354 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 19:09:27,354 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 19:09:27,421 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:09:27,421 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:09:27,421 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:09:27,809 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:28,234 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:28,234 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:29,156 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:29,463 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:29,517 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:29,874 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:29,959 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:30,700 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:30,700 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:31,109 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:31,113 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:31,120 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:09:31,121 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:31,121 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:09:31,130 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:31,132 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:31,516 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:31,516 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:09:31,885 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:09:31,885 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:09:32,263 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:09:32,263 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:09:32,263 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:32,271 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:09:32,288 - main - INFO - Adding 26 exchange features
2025-04-16 19:09:32,288 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:32,290 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:09:32,294 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:09:32,298 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:09:32,302 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:09:32,303 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:09:32,303 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 19:09:32,304 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:09:32,304 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.47, Alignment: 0.67
2025-04-16 19:09:32,304 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:09:32,305 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:09:32,305 - market_regime - INFO - Regime metrics - Trend strength: 0.47, Volatility: 0.30%, Alignment: 0.67
2025-04-16 19:09:32,305 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:09:32,305 - main - INFO - Regime adjustments: {'leverage_factor': 0.7331229624665516, 'position_size_factor': 0.8151022855022851, 'stop_loss_factor': 1.1031643575541021, 'take_profit_factor': 0.9165220871272158, 'entry_confidence': 0.6696028557413175}
2025-04-16 19:09:32,305 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:09:32,305 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.004061935867721104, 'orderbook_score': 0.0, 'volume_score': -0.07497891719417028, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07904085306189139, 'confidence': 47.5299733418159, 'alignment': 62.5}
2025-04-16 19:09:32,305 - main - INFO - Signal-based decision: WAIT with confidence 47.53% and alignment 62.50%
2025-04-16 19:09:32,305 - main - INFO - Calculated signal scores: {'macd_score': -0.004061935867721104, 'orderbook_score': 0.0, 'volume_score': -0.07497891719417028, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.07904085306189139, 'confidence': 47.5299733418159, 'alignment': 62.5}
2025-04-16 19:09:32,308 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:09:32,308 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:09:32,310 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:09:32,310 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:09:32,310 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:09:32,310 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:09:32,310 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:09:32,311 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 19:09:32,311 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 19:09:32,719 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:33,138 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:33,492 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:33,890 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:34,470 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:34,794 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:34,879 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:34,882 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:35,207 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:35,572 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:35,974 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:37,269 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:37,701 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:37,704 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:39,006 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:39,416 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:39,785 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:39,790 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:40,195 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:40,196 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:40,969 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:41,102 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:41,501 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:41,517 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:41,883 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:42,469 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:42,885 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:42,889 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:44,801 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:45,210 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:45,571 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:45,967 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:46,588 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:47,002 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:47,002 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:54,323 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:09:54,323 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:09:54,325 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:09:54,691 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:55,526 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:09:56,869 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:09:57,276 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:09:57,867 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:09:59,015 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:09:59,018 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:09:59,022 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:09:59,022 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:09:59,022 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:09:59,387 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:09:59,387 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:10:01,021 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:10:01,021 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:10:01,392 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:10:01,392 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:10:01,392 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:10:01,398 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:10:01,405 - main - INFO - Adding 26 exchange features
2025-04-16 19:10:01,405 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:10:01,405 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:10:01,413 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:10:01,418 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:10:01,420 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:10:01,420 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:10:01,420 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 19:10:01,420 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:10:01,425 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.47, Alignment: 0.67
2025-04-16 19:10:01,425 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:10:01,425 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:10:01,425 - market_regime - INFO - Regime metrics - Trend strength: 0.47, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:10:01,425 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:10:01,425 - main - INFO - Regime adjustments: {'leverage_factor': 0.7340649598267446, 'position_size_factor': 0.8156276486878474, 'stop_loss_factor': 1.101496858651943, 'take_profit_factor': 0.915873561876174, 'entry_confidence': 0.6691857404648686}
2025-04-16 19:10:01,426 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:10:01,426 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.00373190249180522, 'orderbook_score': 0.0, 'volume_score': -0.06411839793123261, 'price_action_score': 0.060098753153902035, 'trend_score': 0.0, 'total_score': -0.0077515472691357945, 'confidence': 49.75776414783951, 'alignment': 50.0}
2025-04-16 19:10:01,427 - main - INFO - Signal-based decision: WAIT with confidence 49.76% and alignment 50.00%
2025-04-16 19:10:01,427 - main - INFO - Calculated signal scores: {'macd_score': -0.00373190249180522, 'orderbook_score': 0.0, 'volume_score': -0.06411839793123261, 'price_action_score': 0.060098753153902035, 'trend_score': 0.0, 'total_score': -0.0077515472691357945, 'confidence': 49.75776414783951, 'alignment': 50.0}
2025-04-16 19:10:01,428 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:10:01,429 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:10:01,429 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:10:01,429 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:10:01,429 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:10:01,429 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:10:01,429 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:10:01,430 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.12%, TP: 0.14% (ATR: 0.07%)
2025-04-16 19:10:01,431 - main - INFO - Using adaptive take profit: 0.14%
2025-04-16 19:10:01,431 - main - INFO - Using adaptive stop loss: 0.12%
2025-04-16 19:10:01,431 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:10:01,432 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:10:01,432 - main - INFO - Decision for DOGE/USDT:USDT: SHORT
2025-04-16 19:10:01,432 - main - INFO - Explanation: Multiple bearish signals detected: MACD shows bearish momentum. Order book shows selling pressure. Recent trades show selling activity. These factors suggest downward price movement.
2025-04-16 19:10:01,432 - main - INFO - Take Profit: 0.13866948967973527%
2025-04-16 19:10:01,433 - main - INFO - Stop Loss: 0.12018022438910508%
2025-04-16 19:10:05,134 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:07,433 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:07,805 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:08,201 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:10:09,118 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:10:09,560 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:10:09,561 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:10:14,792 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:15,214 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:15,587 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:16,004 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:10:16,591 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:10:17,012 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:10:17,020 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:10:24,781 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:25,188 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:25,555 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:25,938 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:10:26,533 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:10:26,969 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:10:26,970 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:10:29,814 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:30,213 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:30,575 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:30,985 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:10:31,568 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:10:31,995 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:10:31,998 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:10:39,804 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:40,212 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:41,184 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:41,571 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:10:42,170 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:10:42,589 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:10:42,600 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:10:51,660 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:10:51,660 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:10:51,665 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:10:52,670 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:53,054 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:53,720 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:54,128 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:10:55,033 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:10:55,453 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:10:55,453 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:10:55,453 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:10:55,453 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:10:55,453 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:10:55,823 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:55,823 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:10:56,193 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:10:56,193 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:10:56,882 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:10:56,882 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:10:56,882 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:10:56,884 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:10:56,896 - main - INFO - Adding 26 exchange features
2025-04-16 19:10:56,896 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:10:56,896 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:10:56,903 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:10:56,907 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:10:56,911 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:10:56,911 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:10:56,911 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 19:10:56,911 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 19:10:56,911 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.47, Alignment: 0.67
2025-04-16 19:10:56,911 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:10:56,911 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:10:56,911 - market_regime - INFO - Regime metrics - Trend strength: 0.47, Volatility: 0.29%, Alignment: 0.67
2025-04-16 19:10:56,911 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:10:56,911 - main - INFO - Regime adjustments: {'leverage_factor': 0.7340649598267446, 'position_size_factor': 0.8156276486878474, 'stop_loss_factor': 1.101496858651943, 'take_profit_factor': 0.915873561876174, 'entry_confidence': 0.6691857404648686}
2025-04-16 19:10:56,913 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:10:56,913 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.006105524503704495, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.006105524503704495, 'confidence': 49.80920235925924, 'alignment': 50.0}
2025-04-16 19:10:56,913 - main - INFO - Signal-based decision: WAIT with confidence 49.81% and alignment 50.00%
2025-04-16 19:10:56,913 - main - INFO - Calculated signal scores: {'macd_score': -0.006105524503704495, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.006105524503704495, 'confidence': 49.80920235925924, 'alignment': 50.0}
2025-04-16 19:10:56,916 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:10:56,916 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:10:56,917 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:10:56,917 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:10:56,917 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:10:56,917 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:10:56,918 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:10:56,918 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 19:10:56,918 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 19:10:57,322 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:10:58,034 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:10:59,050 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:10:59,451 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:00,043 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:00,465 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:00,465 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:05,460 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:05,873 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:06,237 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:06,621 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:07,518 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:07,937 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:07,937 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:09,843 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:10,267 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:10,622 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:11,006 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:11,608 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:12,058 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:12,060 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:14,905 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:15,668 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:16,683 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:17,075 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:17,659 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:27,673 - data.exchange - ERROR - Error fetching trades for DOGE/USDT:USDT (future): htx GET https://api.hbdm.com/linear-swap-ex/market/history/trade?size=100&contract_code=DOGE-USDT
2025-04-16 19:11:27,674 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:30,209 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:30,866 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:31,225 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:31,612 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:32,203 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:32,596 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:32,599 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:34,928 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:35,312 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:35,667 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:36,366 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:36,950 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:37,350 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:37,350 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:39,921 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:40,300 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:40,652 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:41,030 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:41,618 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:42,017 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:42,017 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:44,905 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:45,299 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:45,648 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:46,039 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:46,650 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:47,053 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:47,053 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:51,899 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:11:51,899 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 19:11:51,899 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 19:11:52,904 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:53,315 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:11:53,681 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:11:54,063 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:11:54,658 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:11:55,062 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:11:55,067 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:11:55,067 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 19:11:55,067 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:11:55,067 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:11:55,433 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 19:11:55,433 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 19:11:55,800 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 19:11:55,800 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 19:11:56,175 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 19:11:56,175 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 19:11:56,177 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 19:11:56,185 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 19:11:56,192 - main - INFO - Adding 26 exchange features
2025-04-16 19:11:56,192 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 19:11:56,195 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 19:11:56,199 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 19:11:56,203 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 19:11:56,207 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 19:11:56,207 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:11:56,207 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 19:11:56,209 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 19:11:56,209 - main - INFO - Multi-timeframe analysis: Trend direction: strong_bullish, Strength: 0.80, Alignment: 0.67
2025-04-16 19:11:56,209 - market_regime - INFO - Initialized market regime detector
2025-04-16 19:11:56,209 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 19:11:56,209 - market_regime - INFO - Regime metrics - Trend strength: 0.80, Volatility: 0.30%, Alignment: 0.67
2025-04-16 19:11:56,209 - main - INFO - Detected market regime: low_volatility
2025-04-16 19:11:56,209 - main - INFO - Regime adjustments: {'leverage_factor': 0.779763639811945, 'position_size_factor': 0.8417967160403551, 'stop_loss_factor': 1.0819384375004037, 'take_profit_factor': 0.980520039740113, 'entry_confidence': 0.6789361425217918}
2025-04-16 19:11:56,209 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:11:56,210 - signal_scoring - INFO - Calculated signal scores: {'macd_score': -0.007992945683236415, 'orderbook_score': 0.0, 'volume_score': -0.010774371509649739, 'price_action_score': 0.01422600339635339, 'trend_score': 0.0, 'total_score': -0.004541313796532763, 'confidence': 49.858083943858354, 'alignment': 50.0}
2025-04-16 19:11:56,210 - main - INFO - Signal-based decision: WAIT with confidence 49.86% and alignment 50.00%
2025-04-16 19:11:56,210 - main - INFO - Calculated signal scores: {'macd_score': -0.007992945683236415, 'orderbook_score': 0.0, 'volume_score': -0.010774371509649739, 'price_action_score': 0.01422600339635339, 'trend_score': 0.0, 'total_score': -0.004541313796532763, 'confidence': 49.858083943858354, 'alignment': 50.0}
2025-04-16 19:11:56,211 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 19:11:56,211 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 19:11:56,211 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 19:11:56,211 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 19:11:56,211 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 19:11:56,211 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 19:11:56,211 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 19:11:56,212 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.13%, TP: 0.15% (ATR: 0.08%)
2025-04-16 19:11:56,212 - main - INFO - Using adaptive take profit: 0.15%
2025-04-16 19:11:56,212 - main - INFO - Using adaptive stop loss: 0.13%
2025-04-16 19:11:56,212 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 19:11:56,212 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 19:11:56,212 - main - INFO - Decision for DOGE/USDT:USDT: SHORT
2025-04-16 19:11:56,213 - main - INFO - Explanation: Multiple bearish signals detected: RSI indicates overbought conditions. MACD shows bearish momentum. Order book shows selling pressure. Futures market is more active while price is overbought, indicating distribution. These factors suggest downward price movement.
2025-04-16 19:11:56,213 - main - INFO - Take Profit: 0.1520342848329216%
2025-04-16 19:11:56,213 - main - INFO - Stop Loss: 0.1317630468552011%
2025-04-16 19:11:59,913 - data.exchange - INFO - Fetched 99 1m candles for DOGE/USDT (spot)
2025-04-16 19:12:00,334 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 19:12:01,001 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 19:12:01,416 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 19:12:02,006 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 19:12:04,082 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 19:12:04,082 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 19:12:04,152 - __main__ - INFO - Application closed normally
2025-04-16 19:53:05,630 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 19:53:06,198 - __main__ - INFO - Initializing components...
2025-04-16 19:53:06,724 - __main__ - INFO - Loading configuration...
2025-04-16 19:53:07,231 - __main__ - INFO - Connecting to data sources...
2025-04-16 19:53:07,745 - __main__ - INFO - Creating main window...
2025-04-16 19:53:07,747 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 19:53:07,748 - main_window - INFO - Initializing MainWindow
2025-04-16 19:53:07,748 - main_window - INFO - Creating QSettings
2025-04-16 19:53:07,748 - main_window - INFO - Creating ThemeManager
2025-04-16 19:53:07,748 - main_window - INFO - Creating NotificationManager
2025-04-16 19:53:07,750 - main_window - INFO - Calling init_ui
2025-04-16 19:53:07,750 - main_window - INFO - Setting window title and geometry
2025-04-16 19:53:07,751 - main_window - INFO - Creating menu bar
2025-04-16 19:53:07,751 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 19:53:07,751 - main_window - INFO - Creating central widget
2025-04-16 19:53:07,752 - main_window - INFO - Creating control toolbar
2025-04-16 19:53:07,752 - main_window - INFO - Adding bot buttons
2025-04-16 19:53:07,752 - main_window - INFO - Creating bot tab widget
2025-04-16 19:53:07,752 - main_window - INFO - Creating status bar
2025-04-16 19:53:07,754 - main_window - INFO - Applying theme
2025-04-16 19:53:07,765 - main_window - INFO - Restoring window geometry
2025-04-16 19:53:07,765 - main_window - INFO - Adding initial simulation bot
2025-04-16 19:53:07,765 - main_window - INFO - Adding new simulation bot
2025-04-16 19:53:07,765 - main_window - INFO - Using default bot name
2025-04-16 19:53:07,766 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 19:53:07,766 - main_window - INFO - Generating unique ID
2025-04-16 19:53:07,766 - main_window - INFO - Creating BotTabWidget with ID 653228c6-5e54-44e0-8a57-a69cd419ad94 and name Simulation Bot 1
2025-04-16 19:53:17,996 - multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:53:17,997 - market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:53:17,998 - adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:53:18,001 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-16 19:53:49,689 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:53:49,689 - trade_lifecycle - INFO - Initialized trade lifecycle manager
2025-04-16 19:53:59,948 - data.exchange - ERROR - Error connecting to htx exchange: htx GET https://api.huobi.pro/v2/reference/currencies
2025-04-16 19:54:00,062 - main_window - INFO - BotTabWidget created successfully
2025-04-16 19:54:00,062 - main_window - INFO - Adding to tab widget
2025-04-16 19:54:00,098 - main_window - INFO - Updating status bar
2025-04-16 19:54:00,098 - __main__ - INFO - MainWindow initialized successfully
2025-04-16 19:54:23,954 - main_window - INFO - Adding new live bot
2025-04-16 19:54:23,954 - main_window - INFO - Using default bot name
2025-04-16 19:54:23,954 - main_window - INFO - Bot name: Live Bot 2
2025-04-16 19:54:23,954 - main_window - INFO - Generating unique ID
2025-04-16 19:54:23,954 - main_window - INFO - Creating BotTabWidget with ID 34460d7c-8ddc-4d90-9d66-3e7ff1666b29 and name Live Bot 2
2025-04-16 19:54:23,954 - trade_lifecycle - INFO - Initialized trade lifecycle manager
2025-04-16 19:54:37,831 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:54:37,831 - dynamic_targets - INFO - Initialized dynamic target calculator with htx exchange
2025-04-16 19:54:37,831 - dynamic_targets - INFO - Fee rate: 0.0400%
2025-04-16 19:54:37,831 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:54:47,322 - data.exchange - INFO - Fetched 2000 5m candles for DOGE/USDT:USDT (spot)
2025-04-16 19:54:47,329 - dynamic_targets - INFO - Calculated dynamic targets for DOGE/USDT:USDT with 20.00x leverage:
2025-04-16 19:54:47,329 - dynamic_targets - INFO - Take profit: 3.92%, Stop loss: 0.62%
2025-04-16 19:54:47,376 - main_window - INFO - BotTabWidget created successfully
2025-04-16 19:54:47,376 - main_window - INFO - Adding to tab widget
2025-04-16 19:54:47,403 - main_window - INFO - Updating status bar
2025-04-16 19:56:44,592 - __main__ - INFO - Application closed normally
2025-04-16 19:56:47,220 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 19:56:47,722 - __main__ - INFO - Initializing components...
2025-04-16 19:56:48,237 - __main__ - INFO - Loading configuration...
2025-04-16 19:56:48,740 - __main__ - INFO - Connecting to data sources...
2025-04-16 19:56:49,245 - __main__ - INFO - Creating main window...
2025-04-16 19:56:49,247 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 19:56:49,247 - main_window - INFO - Initializing MainWindow
2025-04-16 19:56:49,247 - main_window - INFO - Creating QSettings
2025-04-16 19:56:49,247 - main_window - INFO - Creating ThemeManager
2025-04-16 19:56:49,247 - main_window - INFO - Creating NotificationManager
2025-04-16 19:56:49,249 - main_window - INFO - Calling init_ui
2025-04-16 19:56:49,249 - main_window - INFO - Setting window title and geometry
2025-04-16 19:56:49,249 - main_window - INFO - Creating menu bar
2025-04-16 19:56:49,249 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 19:56:49,249 - main_window - INFO - Creating central widget
2025-04-16 19:56:49,249 - main_window - INFO - Creating control toolbar
2025-04-16 19:56:49,249 - main_window - INFO - Adding bot buttons
2025-04-16 19:56:49,249 - main_window - INFO - Creating bot tab widget
2025-04-16 19:56:49,249 - main_window - INFO - Creating status bar
2025-04-16 19:56:49,253 - main_window - INFO - Applying theme
2025-04-16 19:56:49,265 - main_window - INFO - Restoring window geometry
2025-04-16 19:56:49,270 - main_window - INFO - Adding initial simulation bot
2025-04-16 19:56:49,270 - main_window - INFO - Adding new simulation bot
2025-04-16 19:56:49,271 - main_window - INFO - Using default bot name
2025-04-16 19:56:49,271 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 19:56:49,271 - main_window - INFO - Generating unique ID
2025-04-16 19:56:49,271 - main_window - INFO - Creating BotTabWidget with ID efdf7fea-bf5a-4d08-a8ad-58504ba35451 and name Simulation Bot 1
2025-04-16 19:56:51,398 - multi_timeframe - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:56:51,398 - market_regime - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:56:51,404 - adaptive_risk - INFO - TA-Lib not available, using pandas-ta fallback
2025-04-16 19:56:51,404 - root - WARNING - TA-Lib not available. Using numpy-based fallback for technical indicators.
2025-04-16 19:57:21,107 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:57:21,109 - trade_lifecycle - INFO - Initialized trade lifecycle manager
2025-04-16 19:57:35,075 - data.exchange - INFO - Connected to htx exchange
2025-04-16 19:57:35,075 - dynamic_targets - INFO - Initialized dynamic target calculator with htx exchange
2025-04-16 19:57:35,075 - dynamic_targets - INFO - Fee rate: 0.0400%
2025-04-16 19:57:35,075 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 19:57:40,532 - data.exchange - INFO - Fetched 2000 5m candles for DOGE/USDT:USDT (spot)
2025-04-16 19:57:40,534 - dynamic_targets - INFO - Calculated dynamic targets for DOGE/USDT:USDT with 20.00x leverage:
2025-04-16 19:57:40,534 - dynamic_targets - INFO - Take profit: 3.92%, Stop loss: 0.62%
2025-04-16 19:57:40,607 - main_window - INFO - BotTabWidget created successfully
2025-04-16 19:57:40,607 - main_window - INFO - Adding to tab widget
2025-04-16 19:57:40,623 - main_window - INFO - Updating status bar
2025-04-16 19:57:40,623 - __main__ - INFO - MainWindow initialized successfully
2025-04-16 20:00:23,254 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:00:23,254 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:00:23,254 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:00:23,916 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:00:24,826 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:00:25,235 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:00:25,615 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:00:26,518 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:00:27,586 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:00:27,590 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:00:27,592 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:00:27,592 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:00:27,592 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:00:28,696 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:00:28,696 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:00:31,449 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:00:31,449 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:00:32,149 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:00:32,149 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:00:32,149 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:00:32,156 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:00:32,168 - main - INFO - Adding 26 exchange features
2025-04-16 20:00:32,168 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:00:32,168 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:00:32,172 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:00:32,176 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:00:32,179 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:00:32,179 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bearish (score: -0.33)
2025-04-16 20:00:32,179 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:00:32,179 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:00:32,179 - main - INFO - Multi-timeframe analysis: Trend direction: strong_bullish, Strength: 0.73, Alignment: 0.67
2025-04-16 20:00:32,179 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:00:32,179 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:00:32,179 - market_regime - INFO - Regime metrics - Trend strength: 0.73, Volatility: 0.27%, Alignment: 0.67
2025-04-16 20:00:32,179 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:00:32,179 - main - INFO - Regime adjustments: {'leverage_factor': 0.7726691421335358, 'position_size_factor': 0.8375091331107205, 'stop_loss_factor': 1.0794072115091968, 'take_profit_factor': 0.9622565225919888, 'entry_confidence': 0.6748636147774137}
2025-04-16 20:00:32,179 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:00:32,181 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.00794035753476789, 'orderbook_score': 0.0, 'volume_score': -0.07498505481449357, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06704469727972567, 'confidence': 47.904853210008575, 'alignment': 62.5}
2025-04-16 20:00:32,181 - main - INFO - Signal-based decision: WAIT with confidence 47.90% and alignment 62.50%
2025-04-16 20:00:32,182 - main - INFO - Calculated signal scores: {'macd_score': 0.00794035753476789, 'orderbook_score': 0.0, 'volume_score': -0.07498505481449357, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06704469727972567, 'confidence': 47.904853210008575, 'alignment': 62.5}
2025-04-16 20:00:32,182 - llama.runner - WARNING - Model file not found at C:\Users\<USER>\Documents\dev\Epinnox_v6\config\models\Llama-4-Scout-17B-16E-Instruct.gguf
2025-04-16 20:00:32,182 - llama.runner - INFO - You'll need to download a LLaMA model and place it in the models directory.
2025-04-16 20:00:32,182 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:00:32,182 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:00:32,182 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:00:32,182 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:00:32,184 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:00:32,184 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:00:32,185 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:00:33,761 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:00:37,920 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:00:38,335 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:00:38,718 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:00:42,602 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:00:52,612 - data.exchange - ERROR - Error fetching trades for DOGE/USDT:USDT (future): htx GET https://api.hbdm.com/linear-swap-ex/market/history/trade?size=100&contract_code=DOGE-USDT
2025-04-16 20:00:52,612 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:00:55,033 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:00:58,168 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:00:58,605 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:00:59,169 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:01:01,373 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:01:02,977 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:01:02,977 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:02:00,922 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:02:00,922 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:02:00,922 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:02:01,485 - data.exchange - INFO - Fetched 99 1m candles for DOGE/USDT (spot)
2025-04-16 20:02:11,876 - data.exchange - ERROR - Error fetching OHLCV data for DOGE/USDT:USDT (future): htx GET https://api.hbdm.com/linear-swap-ex/market/history/kline?period=1min&size=100&from=1744849381&to=1744855321&contract_code=DOGE-USDT
2025-04-16 20:02:12,696 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:02:13,378 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:02:20,217 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:02:21,323 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:02:21,326 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:02:21,329 - main - INFO - Fetched 99 rows of exchange data for DOGE/USDT
2025-04-16 20:02:21,329 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:02:21,329 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:02:21,891 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:02:21,891 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:02:28,485 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:02:28,485 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:02:29,109 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:02:29,110 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:02:29,110 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:02:29,114 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:02:29,124 - main - INFO - Adding 23 exchange features
2025-04-16 20:02:29,124 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:02:29,124 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:02:29,129 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:02:29,133 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:02:29,136 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:02:29,136 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bearish (score: -0.33)
2025-04-16 20:02:29,136 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:02:29,136 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:02:29,139 - main - INFO - Multi-timeframe analysis: Trend direction: strong_bullish, Strength: 0.73, Alignment: 0.67
2025-04-16 20:02:29,139 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:02:29,139 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:02:29,139 - market_regime - INFO - Regime metrics - Trend strength: 0.73, Volatility: 0.27%, Alignment: 0.67
2025-04-16 20:02:29,139 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:02:29,140 - main - INFO - Regime adjustments: {'leverage_factor': 0.7727491988545286, 'position_size_factor': 0.8375536426335871, 'stop_loss_factor': 1.0792471987674896, 'take_profit_factor': 0.9621725385601553, 'entry_confidence': 0.6748191556046864}
2025-04-16 20:02:29,140 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:02:29,140 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.007464024931904093, 'orderbook_score': 0.0, 'volume_score': 0.06379052377314853, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.07125454870505263, 'confidence': 52.226704647032896, 'alignment': 62.5}
2025-04-16 20:02:29,140 - main - INFO - Signal-based decision: WAIT with confidence 52.23% and alignment 62.50%
2025-04-16 20:02:29,140 - main - INFO - Calculated signal scores: {'macd_score': 0.007464024931904093, 'orderbook_score': 0.0, 'volume_score': 0.06379052377314853, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.07125454870505263, 'confidence': 52.226704647032896, 'alignment': 62.5}
2025-04-16 20:02:29,141 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:02:29,142 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:02:29,142 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:02:29,142 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:02:29,142 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:02:29,142 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:02:29,142 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:02:42,160 - data.exchange - ERROR - Error fetching OHLCV data for DOGE/USDT (spot): htx GET https://api.huobi.pro/market/history/candles?period=1min&symbol=dogeusdt&size=100
2025-04-16 20:02:44,158 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:02:51,422 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:02:53,373 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:02:54,891 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:02:55,308 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:02:55,319 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:03:16,398 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:03:16,398 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:03:16,398 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:03:16,881 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:03:20,157 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:03:20,569 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:03:21,135 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:03:22,401 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:03:23,422 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:03:23,422 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:03:23,437 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:03:23,437 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:03:23,437 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:03:25,310 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:03:25,310 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:03:25,822 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:03:25,822 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:03:28,016 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:03:28,016 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:03:28,016 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:03:28,023 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:03:28,032 - main - INFO - Adding 26 exchange features
2025-04-16 20:03:28,032 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:03:28,032 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:03:28,037 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:03:28,039 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:03:28,043 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:03:28,043 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: bearish (score: -0.33)
2025-04-16 20:03:28,043 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:03:28,043 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:03:28,043 - main - INFO - Multi-timeframe analysis: Trend direction: strong_bullish, Strength: 0.73, Alignment: 0.67
2025-04-16 20:03:28,043 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:03:28,048 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:03:28,048 - market_regime - INFO - Regime metrics - Trend strength: 0.73, Volatility: 0.27%, Alignment: 0.67
2025-04-16 20:03:28,048 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:03:28,048 - main - INFO - Regime adjustments: {'leverage_factor': 0.7729487673399209, 'position_size_factor': 0.8376645976908742, 'stop_loss_factor': 1.078848312826246, 'take_profit_factor': 0.9619631799226489, 'entry_confidence': 0.6747083260621697}
2025-04-16 20:03:28,049 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:03:28,050 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.007092949026343817, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.007092949026343817, 'confidence': 50.221654657073245, 'alignment': 50.0}
2025-04-16 20:03:28,050 - main - INFO - Signal-based decision: WAIT with confidence 50.22% and alignment 50.00%
2025-04-16 20:03:28,050 - main - INFO - Calculated signal scores: {'macd_score': 0.007092949026343817, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.007092949026343817, 'confidence': 50.221654657073245, 'alignment': 50.0}
2025-04-16 20:03:28,051 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:03:28,051 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:03:28,051 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:03:28,051 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:03:28,051 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:03:28,052 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:03:28,052 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:03:31,724 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:03:32,109 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:03:32,515 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:03:33,057 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:03:34,509 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:03:35,452 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:03:35,455 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:03:38,273 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:03:40,542 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:03:40,957 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:03:41,884 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:03:42,673 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:03:43,075 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:03:43,080 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:03:58,265 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:03:58,265 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:03:58,265 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:03:58,847 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:03:59,968 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:04:00,377 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:04:00,789 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:04:02,260 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:04:03,606 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:04:03,606 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:04:03,625 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:04:03,625 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:04:03,625 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:04:04,259 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:04:04,259 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:04:04,888 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:04:04,888 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:04:11,642 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:04:11,642 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:04:11,642 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:04:11,650 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:04:11,660 - main - INFO - Adding 26 exchange features
2025-04-16 20:04:11,662 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:04:11,663 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:04:11,675 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:04:11,683 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:04:11,689 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:04:11,689 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:04:11,691 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:04:11,692 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:04:11,692 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:04:11,693 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:04:11,693 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:04:11,693 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.28%, Alignment: 0.67
2025-04-16 20:04:11,693 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:04:11,693 - main - INFO - Regime adjustments: {'leverage_factor': 0.750498723080874, 'position_size_factor': 0.82452117182298, 'stop_loss_factor': 1.0914990019610722, 'take_profit_factor': 0.9359505525996801, 'entry_confidence': 0.67172705223843}
2025-04-16 20:04:11,695 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:04:11,696 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.004799168281339535, 'orderbook_score': 0.0, 'volume_score': -0.015546518183538104, 'price_action_score': -0.06004251492608394, 'trend_score': 0.0, 'total_score': -0.0707898648282825, 'confidence': 47.78781672411617, 'alignment': 62.5}
2025-04-16 20:04:11,696 - main - INFO - Signal-based decision: WAIT with confidence 47.79% and alignment 62.50%
2025-04-16 20:04:11,697 - main - INFO - Calculated signal scores: {'macd_score': 0.004799168281339535, 'orderbook_score': 0.0, 'volume_score': -0.015546518183538104, 'price_action_score': -0.06004251492608394, 'trend_score': 0.0, 'total_score': -0.0707898648282825, 'confidence': 47.78781672411617, 'alignment': 62.5}
2025-04-16 20:04:11,698 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:04:11,698 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:04:11,698 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:04:11,700 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:04:11,700 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:04:11,704 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.11%, TP: 0.13% (ATR: 0.06%)
2025-04-16 20:04:11,774 - main - INFO - Using adaptive take profit: 0.13%
2025-04-16 20:04:11,775 - main - INFO - Using adaptive stop loss: 0.11%
2025-04-16 20:04:11,775 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 20:04:11,775 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 20:04:11,775 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 20:04:11,775 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Order book shows buying pressure. These factors suggest upward price movement.
2025-04-16 20:04:11,775 - main - INFO - Take Profit: 0.12690134085994248%
2025-04-16 20:04:11,775 - main - INFO - Stop Loss: 0.10998116207860968%
2025-04-16 20:04:16,939 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:04:17,898 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:04:18,717 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:04:19,106 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:04:20,052 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:04:20,615 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:04:20,618 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:04:26,874 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:04:27,279 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:04:27,689 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:04:28,575 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:04:29,474 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:04:29,877 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:04:29,882 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:04:31,868 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:04:35,137 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:04:35,546 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:04:36,511 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:04:37,408 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:04:38,283 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:04:38,288 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:04:58,595 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:04:58,595 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:04:58,595 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:04:59,513 - data.exchange - INFO - Fetched 99 1m candles for DOGE/USDT (spot)
2025-04-16 20:05:00,413 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:05:00,817 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:05:01,413 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:05:02,645 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:05:03,246 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:05:03,251 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:05:03,257 - main - INFO - Fetched 99 rows of exchange data for DOGE/USDT
2025-04-16 20:05:03,257 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:05:03,257 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:05:10,272 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:05:10,272 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:05:20,693 - data.exchange - ERROR - Error fetching OHLCV data for DOGE/USDT (spot): htx GET https://api.huobi.pro/market/history/candles?period=5min&symbol=dogeusdt&size=100
2025-04-16 20:05:20,693 - multi_timeframe - WARNING - Empty data returned for DOGE/USDT on 5m timeframe
2025-04-16 20:05:22,853 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:05:22,853 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:05:22,853 - main - INFO - Fetched data for 2 timeframes: ['1m', '15m']
2025-04-16 20:05:22,855 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:05:22,875 - main - INFO - Adding 26 exchange features
2025-04-16 20:05:22,875 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:05:22,875 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:05:22,879 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:05:22,885 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:05:22,885 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:05:22,886 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:05:22,886 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.43, Alignment: 0.50
2025-04-16 20:05:22,886 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:05:22,886 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:05:22,886 - market_regime - INFO - Regime metrics - Trend strength: 0.43, Volatility: 0.30%, Alignment: 0.50
2025-04-16 20:05:22,887 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:05:22,887 - main - INFO - Regime adjustments: {'leverage_factor': 0.7266691126734779, 'position_size_factor': 0.8081908611548413, 'stop_loss_factor': 1.1139487530300145, 'take_profit_factor': 0.9102773057645283, 'entry_confidence': 0.6754526280336439}
2025-04-16 20:05:22,887 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:05:22,888 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.0044842278257723875, 'orderbook_score': 0.0, 'volume_score': 0.0012620169918954437, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.005746244817667831, 'confidence': 50.17957015055211, 'alignment': 50.0}
2025-04-16 20:05:22,889 - main - INFO - Signal-based decision: WAIT with confidence 50.18% and alignment 50.00%
2025-04-16 20:05:22,889 - main - INFO - Calculated signal scores: {'macd_score': 0.0044842278257723875, 'orderbook_score': 0.0, 'volume_score': 0.0012620169918954437, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.005746244817667831, 'confidence': 50.17957015055211, 'alignment': 50.0}
2025-04-16 20:05:22,891 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:05:22,891 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:05:22,891 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:05:22,891 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:05:22,891 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:05:22,891 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:05:22,891 - main - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-04-16 20:05:23,783 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:05:25,628 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:05:26,029 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:05:26,632 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:05:27,841 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:05:34,084 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:05:34,089 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:05:37,671 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:05:38,937 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:05:40,739 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:05:41,132 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:05:42,067 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:05:43,145 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:05:43,149 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:05:55,743 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:05:55,743 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:05:55,743 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:05:56,949 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:05:57,983 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:05:58,391 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:06:00,083 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:06:01,135 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:06:01,658 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:06:01,661 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:06:01,664 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:06:01,665 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:06:01,665 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:06:02,685 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:06:02,686 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:06:04,799 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:06:04,799 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:06:06,748 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:06:06,748 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:06:06,758 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:06:06,765 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:06:06,770 - main - INFO - Adding 26 exchange features
2025-04-16 20:06:06,770 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:06:06,770 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:06:06,778 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:06:06,782 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:06:06,786 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:06:06,786 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:06:06,786 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:06:06,786 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:06:06,788 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:06:06,788 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:06:06,788 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:06:06,788 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.27%, Alignment: 0.67
2025-04-16 20:06:06,788 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:06:06,789 - main - INFO - Regime adjustments: {'leverage_factor': 0.7516430058563314, 'position_size_factor': 0.8251607372799696, 'stop_loss_factor': 1.0893761069803414, 'take_profit_factor': 0.9350027628260689, 'entry_confidence': 0.6711703220665325}
2025-04-16 20:06:06,789 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:06:06,789 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.00475205356283611, 'orderbook_score': 0.0, 'volume_score': -0.07468575366448144, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06993370010164532, 'confidence': 47.81457187182359, 'alignment': 62.5}
2025-04-16 20:06:06,789 - main - INFO - Signal-based decision: WAIT with confidence 47.81% and alignment 62.50%
2025-04-16 20:06:06,790 - main - INFO - Calculated signal scores: {'macd_score': 0.00475205356283611, 'orderbook_score': 0.0, 'volume_score': -0.07468575366448144, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06993370010164532, 'confidence': 47.81457187182359, 'alignment': 62.5}
2025-04-16 20:06:06,790 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:06:06,790 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:06:06,790 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:06:06,790 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:06:06,790 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:06:06,790 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:06:06,790 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:06:12,221 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:06:12,614 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:06:13,336 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:06:14,336 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:06:15,257 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:06:17,084 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:06:17,084 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:06:21,955 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:06:22,352 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:06:23,516 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:06:23,907 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:06:24,957 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:06:25,374 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:06:25,374 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:06:26,953 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:06:27,347 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:06:28,515 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:06:29,202 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:06:31,586 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:06:31,981 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:06:31,986 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:06:37,961 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:06:38,652 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:06:39,056 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:06:39,455 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:06:40,427 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:06:41,794 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:06:41,794 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:07:05,747 - data.exchange - ERROR - Error connecting to htx exchange: htx GET https://api.hbdm.com/linear-swap-api/v1/swap_contract_info?business_type=all
2025-04-16 20:07:05,748 - main - ERROR - Error fetching exchange data: htx GET https://api.hbdm.com/linear-swap-api/v1/swap_contract_info?business_type=all
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1368, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 317, in begin
    version, status, reason = self._read_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 278, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 1273, in recv_into
    return self.read(nbytes, buffer)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 1129, in read
    return self._sslobj.read(len, buffer)
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='api.hbdm.com', port=443): Read timed out. (read timeout=10.0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\exchange.py", line 551, in fetch
    response = self.session.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.hbdm.com', port=443): Read timed out. (read timeout=10.0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main.py", line 123, in fetch_exchange_data
    fetcher = ExchangeDataFetcher(exchange_id='htx')
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\data\exchange.py", line 43, in __init__
    self.exchange.load_markets()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\exchange.py", line 1513, in load_markets
    markets = self.fetch_markets(params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\htx.py", line 1793, in fetch_markets
    promises.append(self.fetch_markets_by_type_and_sub_type(None, 'linear', params))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\htx.py", line 1823, in fetch_markets_by_type_and_sub_type
    response = self.contractPublicGetLinearSwapApiV1SwapContractInfo(self.extend(request, params))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\exchange.py", line 4395, in request
    return self.fetch2(path, api, method, params, headers, body, config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\exchange.py", line 4389, in fetch2
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\exchange.py", line 4380, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\ccxt\base\exchange.py", line 581, in fetch
    raise RequestTimeout(details) from e
ccxt.base.errors.RequestTimeout: htx GET https://api.hbdm.com/linear-swap-api/v1/swap_contract_info?business_type=all
2025-04-16 20:07:05,773 - main - INFO - Fetching market data for DOGE/USDT:USDT (1m, 1d)
2025-04-16 20:07:43,831 - yfinance - ERROR - Failed to get ticker 'DOGE/USDT:USDT' reason: Expecting value: line 1 column 1 (char 0)
2025-04-16 20:08:00,228 - yfinance - ERROR - 502 Server Error: Bad Gateway for url: https://query2.finance.yahoo.com/v10/finance/quoteSummary/DOGE/USDT:USDT?modules=financialData%2CquoteType%2CdefaultKeyStatistics%2CassetProfile%2CsummaryDetail&corsDomain=finance.yahoo.com&formatted=false&symbol=DOGE%2FUSDT%3AUSDT&crumb=Z%2FQE.0c4k3.
2025-04-16 20:08:00,234 - yfinance - ERROR - 
1 Failed download:
2025-04-16 20:08:00,234 - yfinance - ERROR - ['DOGE/USDT:USDT']: AttributeError("'NoneType' object has no attribute 'update'")
2025-04-16 20:08:00,235 - main - ERROR - No data returned for DOGE/USDT:USDT
2025-04-16 20:08:01,539 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:04,430 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:08:04,841 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:08:05,396 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:08:06,079 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:08:07,177 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:08:07,181 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:08:22,777 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:08:22,777 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:08:22,777 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:08:23,280 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:25,222 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:08:26,372 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:08:27,064 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:08:27,855 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:08:28,813 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:08:28,817 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:08:28,820 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:08:28,821 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:08:28,821 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:08:29,418 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:29,418 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:08:30,129 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:08:30,130 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:08:31,314 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:08:31,314 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:08:31,314 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:08:31,322 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:08:31,334 - main - INFO - Adding 26 exchange features
2025-04-16 20:08:31,334 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:08:31,334 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:08:31,339 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:08:31,347 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:08:31,352 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:08:31,352 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:08:31,352 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:08:31,354 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:08:31,355 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:08:31,355 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:08:31,355 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:08:31,355 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.29%, Alignment: 0.67
2025-04-16 20:08:31,355 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:08:31,355 - main - INFO - Regime adjustments: {'leverage_factor': 0.748962452673078, 'position_size_factor': 0.8236625156354578, 'stop_loss_factor': 1.0943491197883304, 'take_profit_factor': 0.9372230189980569, 'entry_confidence': 0.6724744969317854}
2025-04-16 20:08:31,356 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:08:31,356 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.004926351697611685, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.004926351697611685, 'confidence': 50.15394849055036, 'alignment': 50.0}
2025-04-16 20:08:31,356 - main - INFO - Signal-based decision: WAIT with confidence 50.15% and alignment 50.00%
2025-04-16 20:08:31,357 - main - INFO - Calculated signal scores: {'macd_score': 0.004926351697611685, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.004926351697611685, 'confidence': 50.15394849055036, 'alignment': 50.0}
2025-04-16 20:08:31,358 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:08:31,358 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:08:31,358 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:08:31,358 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:08:31,358 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:08:31,359 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.14%, TP: 0.16% (ATR: 0.08%)
2025-04-16 20:08:31,359 - main - INFO - Using adaptive take profit: 0.16%
2025-04-16 20:08:31,360 - main - INFO - Using adaptive stop loss: 0.14%
2025-04-16 20:08:31,360 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 20:08:31,360 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 20:08:31,360 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 20:08:31,360 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Recent trades show buying activity. These factors suggest upward price movement.
2025-04-16 20:08:31,361 - main - INFO - Take Profit: 0.16491180065139016%
2025-04-16 20:08:31,361 - main - INFO - Stop Loss: 0.1429235605645346%
2025-04-16 20:08:36,870 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:37,295 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:08:37,703 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:08:38,089 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:08:39,842 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:08:42,084 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:08:42,090 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:08:50,384 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:08:50,384 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:08:50,384 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:08:50,884 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:51,298 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:08:52,033 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:08:52,429 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:08:53,219 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:08:53,635 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:08:53,635 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:08:53,644 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:08:53,644 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:08:53,644 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:08:54,114 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:54,115 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:08:54,663 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:08:54,663 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:08:55,200 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:08:55,200 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:08:55,200 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:08:55,206 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:08:55,214 - main - INFO - Adding 26 exchange features
2025-04-16 20:08:55,214 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:08:55,214 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:08:55,217 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:08:55,223 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:08:55,226 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:08:55,226 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:08:55,229 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:08:55,229 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:08:55,229 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:08:55,229 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:08:55,230 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:08:55,230 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.29%, Alignment: 0.67
2025-04-16 20:08:55,230 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:08:55,230 - main - INFO - Regime adjustments: {'leverage_factor': 0.7487648201099996, 'position_size_factor': 0.8235520543375756, 'stop_loss_factor': 1.0947157714457587, 'take_profit_factor': 0.9373867146563643, 'entry_confidence': 0.6725706514953034}
2025-04-16 20:08:55,230 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:08:55,230 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.005564543701251078, 'orderbook_score': 0.0, 'volume_score': -0.07321541163782011, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06765086793656903, 'confidence': 47.88591037698222, 'alignment': 62.5}
2025-04-16 20:08:55,231 - main - INFO - Signal-based decision: WAIT with confidence 47.89% and alignment 62.50%
2025-04-16 20:08:55,231 - main - INFO - Calculated signal scores: {'macd_score': 0.005564543701251078, 'orderbook_score': 0.0, 'volume_score': -0.07321541163782011, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.06765086793656903, 'confidence': 47.88591037698222, 'alignment': 62.5}
2025-04-16 20:08:55,232 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:08:55,232 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:08:55,232 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:08:55,232 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:08:55,232 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:08:55,232 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:08:55,232 - main - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-04-16 20:08:56,791 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:08:57,212 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:08:57,626 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:08:58,025 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:08:58,713 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:08:59,109 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:08:59,109 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:01,822 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:02,209 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:02,925 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:03,336 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:04,334 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:07,620 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:07,623 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:11,810 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:12,201 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:12,613 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:13,325 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:14,006 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:14,701 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:14,704 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:17,121 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:17,530 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:17,940 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:18,339 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:19,015 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:19,750 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:19,752 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:26,813 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:27,201 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:27,611 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:28,317 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:29,008 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:29,705 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:29,709 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:36,975 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:37,376 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:37,788 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:38,165 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:38,866 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:39,876 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:39,878 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:46,314 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:09:46,314 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:09:46,314 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:09:46,769 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:47,160 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:47,916 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:48,315 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:49,058 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:49,485 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:49,488 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:49,496 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:09:49,496 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:09:49,496 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:09:49,958 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:49,958 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:09:50,446 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:09:50,448 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:09:50,936 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:09:50,936 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:09:50,936 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:09:50,944 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:09:50,953 - main - INFO - Adding 26 exchange features
2025-04-16 20:09:50,953 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:09:50,953 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:09:50,957 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:09:50,962 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:09:50,966 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:09:50,967 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:09:50,967 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:09:50,968 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:09:50,968 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:09:50,968 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:09:50,968 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:09:50,968 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.29%, Alignment: 0.67
2025-04-16 20:09:50,968 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:09:50,968 - main - INFO - Regime adjustments: {'leverage_factor': 0.749086684792407, 'position_size_factor': 0.8237319517694934, 'stop_loss_factor': 1.094118642018127, 'take_profit_factor': 0.9371201196668951, 'entry_confidence': 0.6724140540319772}
2025-04-16 20:09:50,968 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:09:50,968 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.007504927860834865, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.007504927860834865, 'confidence': 50.23452899565108, 'alignment': 50.0}
2025-04-16 20:09:50,969 - main - INFO - Signal-based decision: WAIT with confidence 50.23% and alignment 50.00%
2025-04-16 20:09:50,969 - main - INFO - Calculated signal scores: {'macd_score': 0.007504927860834865, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.007504927860834865, 'confidence': 50.23452899565108, 'alignment': 50.0}
2025-04-16 20:09:50,970 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:09:50,970 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:09:50,970 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:09:50,971 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:09:50,971 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:09:50,971 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:09:50,971 - main - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-04-16 20:09:51,395 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:52,108 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:52,519 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:52,915 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:53,596 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:53,996 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:54,000 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:09:56,820 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:09:57,224 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:09:58,237 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:09:58,611 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:09:59,314 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:09:59,710 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:09:59,713 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:01,830 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:02,227 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:02,934 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:03,331 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:04,003 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:04,389 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:04,401 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:06,820 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:07,210 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:08,453 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:09,735 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:10,425 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:11,131 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:11,134 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:11,832 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:12,738 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:13,165 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:13,876 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:14,571 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:14,971 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:14,976 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:16,834 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:17,524 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:17,927 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:18,314 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:18,996 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:19,400 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:19,404 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:21,828 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:22,547 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:22,953 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:23,386 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:24,071 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:24,477 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:24,477 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:26,842 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:27,240 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:27,650 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:28,037 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:28,719 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:29,111 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:29,111 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:31,846 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:32,243 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:32,647 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:33,033 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:33,711 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:34,091 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:34,102 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:44,943 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:10:44,943 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:10:44,943 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:10:45,366 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:45,779 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:46,192 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:46,596 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:47,267 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:47,692 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:47,692 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:47,699 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:10:47,699 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:10:47,701 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:10:48,108 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:48,108 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:10:48,529 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:10:48,529 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:10:49,707 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:10:49,707 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:10:49,707 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:10:49,717 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:10:49,736 - main - INFO - Adding 26 exchange features
2025-04-16 20:10:49,736 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:10:49,737 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:10:49,744 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:10:49,749 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:10:49,752 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:10:49,753 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:10:49,753 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:10:49,753 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:10:49,753 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:10:49,753 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:10:49,753 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:10:49,753 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.28%, Alignment: 0.67
2025-04-16 20:10:49,753 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:10:49,753 - main - INFO - Regime adjustments: {'leverage_factor': 0.7497671856249704, 'position_size_factor': 0.8241122990361787, 'stop_loss_factor': 1.0928561640425631, 'take_profit_factor': 0.9365564725126506, 'entry_confidence': 0.6720829686100732}
2025-04-16 20:10:49,753 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:10:49,753 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.00991385577427034, 'orderbook_score': 0.0, 'volume_score': 0.05947535259897342, 'price_action_score': 0.06000759562155899, 'trend_score': 0.0, 'total_score': 0.12939680399480274, 'confidence': 54.04365012483758, 'alignment': 75.0}
2025-04-16 20:10:49,753 - main - INFO - Signal-based decision: WAIT with confidence 54.04% and alignment 75.00%
2025-04-16 20:10:49,753 - main - INFO - Calculated signal scores: {'macd_score': 0.00991385577427034, 'orderbook_score': 0.0, 'volume_score': 0.05947535259897342, 'price_action_score': 0.06000759562155899, 'trend_score': 0.0, 'total_score': 0.12939680399480274, 'confidence': 54.04365012483758, 'alignment': 75.0}
2025-04-16 20:10:49,753 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:10:49,753 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:10:49,753 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:10:49,753 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:10:49,753 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:10:49,757 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.13%, TP: 0.16% (ATR: 0.08%)
2025-04-16 20:10:49,757 - main - INFO - Using adaptive take profit: 0.16%
2025-04-16 20:10:49,757 - main - INFO - Using adaptive stop loss: 0.13%
2025-04-16 20:10:49,757 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 20:10:49,757 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 20:10:49,757 - main - INFO - Decision for DOGE/USDT:USDT: SHORT
2025-04-16 20:10:49,757 - main - INFO - Explanation: Multiple bearish signals detected: Order book shows selling pressure. Futures market has higher buy ratio than spot, suggesting potential reversal. These factors suggest downward price movement.
2025-04-16 20:10:49,757 - main - INFO - Take Profit: 0.15535326819622802%
2025-04-16 20:10:49,757 - main - INFO - Stop Loss: 0.13463949910341305%
2025-04-16 20:10:50,175 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:50,573 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:51,085 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:51,478 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:52,236 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:52,640 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:52,644 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:10:56,852 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:10:57,328 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:10:57,808 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:10:58,217 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:10:58,944 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:10:59,409 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:10:59,416 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:01,937 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:02,450 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:02,874 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:03,287 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:04,059 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:04,541 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:04,546 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:06,873 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:07,282 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:07,728 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:08,133 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:08,818 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:09,324 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:09,328 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:16,848 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:17,250 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:17,672 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:18,065 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:18,753 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:19,150 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:19,163 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:21,964 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:22,367 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:22,767 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:23,157 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:23,842 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:24,244 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:24,248 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:31,869 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:32,261 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:32,692 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:33,110 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:33,788 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:34,183 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:34,188 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:36,902 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:37,296 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:37,702 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:38,086 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:38,775 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:39,188 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:39,189 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:44,746 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:11:44,747 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:11:44,747 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:11:45,153 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:45,539 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:45,943 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:46,327 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:47,095 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:47,497 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:47,500 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:47,503 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:11:47,503 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:11:47,503 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:11:48,043 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:48,043 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:11:48,480 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:11:48,480 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:11:48,897 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:11:48,897 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:11:48,898 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:11:48,903 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:11:48,911 - main - INFO - Adding 26 exchange features
2025-04-16 20:11:48,911 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:11:48,912 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:11:48,916 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:11:48,920 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:11:48,924 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:11:48,924 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:11:48,924 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:11:48,925 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:11:48,925 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:11:48,925 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:11:48,926 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:11:48,926 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.28%, Alignment: 0.67
2025-04-16 20:11:48,926 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:11:48,927 - main - INFO - Regime adjustments: {'leverage_factor': 0.7500651094127246, 'position_size_factor': 0.8242788153619943, 'stop_loss_factor': 1.0923034502141038, 'take_profit_factor': 0.9363097073551172, 'entry_confidence': 0.6719380191577823}
2025-04-16 20:11:48,927 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:11:48,927 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.010507481139683781, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.010507481139683781, 'confidence': 50.328358785615116, 'alignment': 50.0}
2025-04-16 20:11:48,927 - main - INFO - Signal-based decision: WAIT with confidence 50.33% and alignment 50.00%
2025-04-16 20:11:48,927 - main - INFO - Calculated signal scores: {'macd_score': 0.010507481139683781, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.010507481139683781, 'confidence': 50.328358785615116, 'alignment': 50.0}
2025-04-16 20:11:48,927 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:11:48,930 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:11:48,931 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:11:48,931 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:11:48,932 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:11:48,932 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:11:48,932 - main - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-04-16 20:11:49,367 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:49,793 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:50,219 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:50,600 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:51,279 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:51,682 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:51,686 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:11:56,864 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:11:57,265 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:11:57,677 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:11:58,068 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:11:58,743 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:11:59,152 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:11:59,156 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:12:01,881 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:02,275 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:12:02,678 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:12:03,074 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:12:03,753 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:12:04,169 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:12:04,169 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:12:11,887 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:12,756 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:12:13,170 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:12:13,556 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:12:14,228 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:12:14,639 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:12:14,640 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:12:21,872 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:22,290 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:12:22,698 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:12:23,114 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:12:24,569 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:12:25,008 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:12:25,010 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:12:31,866 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:32,289 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:12:32,774 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:12:33,164 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:12:33,850 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:12:34,250 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:12:34,250 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:12:48,961 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:12:48,961 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:12:48,962 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:12:49,417 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:49,829 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:12:50,234 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:12:50,635 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:12:51,356 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:12:51,762 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:12:51,762 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:12:51,762 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:12:51,762 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:12:51,762 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:12:52,233 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:52,233 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:12:52,700 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:12:52,700 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:12:53,159 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:12:53,159 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:12:53,159 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:12:53,159 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:12:53,177 - main - INFO - Adding 26 exchange features
2025-04-16 20:12:53,177 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:12:53,177 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:12:53,180 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:12:53,184 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:12:53,188 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:12:53,188 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:12:53,188 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:12:53,188 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:12:53,188 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:12:53,188 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:12:53,188 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:12:53,189 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.28%, Alignment: 0.67
2025-04-16 20:12:53,189 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:12:53,189 - main - INFO - Regime adjustments: {'leverage_factor': 0.749841404075716, 'position_size_factor': 0.8241537814025887, 'stop_loss_factor': 1.092718472573335, 'take_profit_factor': 0.9364949986443564, 'entry_confidence': 0.6720468589597948}
2025-04-16 20:12:53,189 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:12:53,189 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.010838255829533615, 'orderbook_score': 0.0, 'volume_score': 0.0730487282873366, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.08388698411687022, 'confidence': 52.621468253652196, 'alignment': 62.5}
2025-04-16 20:12:53,189 - main - INFO - Signal-based decision: WAIT with confidence 52.62% and alignment 62.50%
2025-04-16 20:12:53,189 - main - INFO - Calculated signal scores: {'macd_score': 0.010838255829533615, 'orderbook_score': 0.0, 'volume_score': 0.0730487282873366, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.08388698411687022, 'confidence': 52.621468253652196, 'alignment': 62.5}
2025-04-16 20:12:53,190 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:12:53,190 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:12:53,190 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:12:53,190 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:12:53,190 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:12:53,190 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:12:53,190 - main - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-04-16 20:12:53,622 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:12:54,004 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:12:54,419 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:12:54,808 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:12:55,496 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:12:55,887 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:12:55,887 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:01,969 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:02,366 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:02,967 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:03,372 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:04,056 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:04,454 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:04,457 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:06,958 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:07,411 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:07,883 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:08,271 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:08,959 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:09,360 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:09,362 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:17,002 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:17,409 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:17,966 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:18,373 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:19,063 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:19,475 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:19,477 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:26,957 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:27,352 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:27,761 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:28,214 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:28,908 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:29,328 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:29,331 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:31,972 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:32,382 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:32,787 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:33,190 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:33,863 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:34,265 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:34,268 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:44,800 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:13:44,800 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:13:44,800 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:13:45,234 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:45,654 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:46,069 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:46,471 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:47,155 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:47,559 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:47,563 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:47,570 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:13:47,570 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:13:47,570 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:13:48,033 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:48,033 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:13:48,456 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:13:48,456 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:13:49,641 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:13:49,641 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:13:49,641 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:13:49,647 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:13:49,655 - main - INFO - Adding 26 exchange features
2025-04-16 20:13:49,655 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:13:49,655 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:13:49,659 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:13:49,663 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:13:49,667 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:13:49,667 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:13:49,667 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:13:49,669 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: strong_bullish (score: 1.00)
2025-04-16 20:13:49,669 - main - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.60, Alignment: 0.67
2025-04-16 20:13:49,669 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:13:49,669 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:13:49,669 - market_regime - INFO - Regime metrics - Trend strength: 0.60, Volatility: 0.28%, Alignment: 0.67
2025-04-16 20:13:49,671 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:13:49,671 - main - INFO - Regime adjustments: {'leverage_factor': 0.7501146158184965, 'position_size_factor': 0.8243064856089913, 'stop_loss_factor': 1.0922116049966615, 'take_profit_factor': 0.9362687020493261, 'entry_confidence': 0.671913932707836}
2025-04-16 20:13:49,671 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:13:49,672 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.011436762574231436, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.011436762574231436, 'confidence': 50.35739883044473, 'alignment': 50.0}
2025-04-16 20:13:49,672 - main - INFO - Signal-based decision: WAIT with confidence 50.36% and alignment 50.00%
2025-04-16 20:13:49,672 - main - INFO - Calculated signal scores: {'macd_score': 0.011436762574231436, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.011436762574231436, 'confidence': 50.35739883044473, 'alignment': 50.0}
2025-04-16 20:13:49,673 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:13:49,674 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:13:49,674 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:13:49,674 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:13:49,675 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:13:49,675 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:13:49,675 - main - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to wait for clearer market direction.
2025-04-16 20:13:50,128 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:50,532 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:51,707 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:52,100 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:52,777 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:13:53,207 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:13:53,207 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:13:56,939 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:13:58,193 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:13:58,604 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:13:59,009 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:13:59,700 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:00,089 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:00,098 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:01,960 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:02,352 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:02,757 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:03,163 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:03,886 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:04,294 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:04,298 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:11,967 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:12,367 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:12,775 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:13,160 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:13,837 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:14,705 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:14,707 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:22,136 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:22,525 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:22,929 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:23,316 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:24,045 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:24,435 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:24,442 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:27,086 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:27,481 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:27,889 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:28,267 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:28,953 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:29,494 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:29,495 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:37,174 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:37,572 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:37,987 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:38,382 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:39,052 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:39,445 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:39,445 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:46,196 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:14:46,196 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:14:46,196 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:14:46,645 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:47,050 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:47,449 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:47,852 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:48,542 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:48,955 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:48,958 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:48,962 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:14:48,962 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:14:48,962 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:14:49,490 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:49,492 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:14:49,969 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:14:49,969 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:14:50,448 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:14:50,448 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:14:50,448 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:14:50,455 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:14:50,466 - main - INFO - Adding 26 exchange features
2025-04-16 20:14:50,466 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:14:50,466 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:14:50,472 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:14:50,476 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:14:50,481 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:14:50,482 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:14:50,483 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 20:14:50,483 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 20:14:50,483 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.07, Alignment: 0.67
2025-04-16 20:14:50,484 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:14:50,484 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:14:50,484 - market_regime - INFO - Regime metrics - Trend strength: 0.07, Volatility: 0.29%, Alignment: 0.67
2025-04-16 20:14:50,484 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:14:50,484 - main - INFO - Regime adjustments: {'leverage_factor': 0.7270126008958647, 'position_size_factor': 0.8095268445037987, 'stop_loss_factor': 1.1198316590713044, 'take_profit_factor': 0.8886547327713316, 'entry_confidence': 0.6774015859277182}
2025-04-16 20:14:50,484 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:14:50,485 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.011005533397567581, 'orderbook_score': 0.0, 'volume_score': 0.051558655084510276, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.06256418848207786, 'confidence': 51.95513089006493, 'alignment': 62.5}
2025-04-16 20:14:50,485 - main - INFO - Signal-based decision: WAIT with confidence 51.96% and alignment 62.50%
2025-04-16 20:14:50,485 - main - INFO - Calculated signal scores: {'macd_score': 0.011005533397567581, 'orderbook_score': 0.0, 'volume_score': 0.051558655084510276, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.06256418848207786, 'confidence': 51.95513089006493, 'alignment': 62.5}
2025-04-16 20:14:50,486 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:14:50,487 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:14:50,487 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:14:50,488 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:14:50,488 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:14:50,488 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:14:50,489 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:14:50,925 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:51,309 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:51,722 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:52,114 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:52,807 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:53,211 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:53,212 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:14:57,040 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:14:57,428 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:14:57,843 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:14:58,721 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:14:59,423 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:14:59,897 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:14:59,899 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:07,081 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:07,485 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:07,899 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:08,308 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:08,992 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:09,385 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:09,385 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:12,022 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:12,413 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:12,816 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:13,253 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:13,946 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:14,332 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:14,339 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:17,023 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:17,419 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:17,844 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:18,233 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:18,913 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:19,379 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:19,382 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:27,033 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:27,423 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:27,836 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:28,224 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:28,916 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:29,317 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:29,319 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:37,038 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:37,431 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:37,840 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:38,244 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:38,927 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:39,332 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:39,332 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:45,598 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:15:45,598 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:15:45,599 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:15:46,793 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:47,250 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:47,712 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:48,107 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:49,652 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:50,132 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:50,132 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:50,132 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:15:50,132 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:15:50,132 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:15:50,547 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:50,547 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:15:50,966 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:15:50,966 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:15:51,404 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:15:51,404 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:15:51,404 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:15:51,409 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:15:51,420 - main - INFO - Adding 26 exchange features
2025-04-16 20:15:51,420 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:15:51,421 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:15:51,425 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:15:51,430 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:15:51,431 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:15:51,435 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:15:51,435 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bullish (score: 0.33)
2025-04-16 20:15:51,436 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 20:15:51,436 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: 0.07, Alignment: 0.67
2025-04-16 20:15:51,436 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:15:51,437 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:15:51,437 - market_regime - INFO - Regime metrics - Trend strength: 0.07, Volatility: 0.27%, Alignment: 0.67
2025-04-16 20:15:51,437 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:15:51,437 - main - INFO - Regime adjustments: {'leverage_factor': 0.7299158989244088, 'position_size_factor': 0.8111714257885286, 'stop_loss_factor': 1.1146238183363264, 'take_profit_factor': 0.887031440564721, 'entry_confidence': 0.6760563823040434}
2025-04-16 20:15:51,438 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:15:51,438 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.011468504264496136, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.011468504264496136, 'confidence': 50.3583907582655, 'alignment': 50.0}
2025-04-16 20:15:51,438 - main - INFO - Signal-based decision: WAIT with confidence 50.36% and alignment 50.00%
2025-04-16 20:15:51,438 - main - INFO - Calculated signal scores: {'macd_score': 0.011468504264496136, 'orderbook_score': 0.0, 'volume_score': -0.0, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': 0.011468504264496136, 'confidence': 50.3583907582655, 'alignment': 50.0}
2025-04-16 20:15:51,438 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:15:51,439 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:15:51,440 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:15:51,440 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:15:51,440 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:15:51,440 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:15:51,440 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:15:51,883 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:52,282 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:52,702 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:53,088 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:53,765 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:54,170 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:54,170 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:15:57,036 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:15:57,427 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:15:57,837 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:15:58,218 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:15:58,884 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:15:59,402 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:15:59,416 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:07,037 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:07,583 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:07,986 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:08,381 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:09,057 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:09,460 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:09,463 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:12,043 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:12,517 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:13,017 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:13,393 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:14,067 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:14,465 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:14,467 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:22,041 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:22,433 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:22,922 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:23,311 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:23,989 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:24,393 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:24,393 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:32,043 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:32,425 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:32,838 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:33,230 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:33,917 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:34,313 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:34,313 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:37,170 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:37,568 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:37,975 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:38,375 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:39,054 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:39,564 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:39,564 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:45,427 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:16:45,427 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:16:45,427 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:16:45,965 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:46,372 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:47,559 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:47,980 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:48,655 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:49,060 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:49,060 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:49,060 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:16:49,060 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:16:49,060 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:16:49,491 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:49,491 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:16:49,914 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:16:49,915 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:16:50,447 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:16:50,447 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:16:50,447 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:16:50,452 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:16:50,461 - main - INFO - Adding 26 exchange features
2025-04-16 20:16:50,461 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:16:50,461 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:16:50,464 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:16:50,469 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:16:50,473 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:16:50,473 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:16:50,473 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 20:16:50,473 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 20:16:50,473 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: -0.13, Alignment: 0.33
2025-04-16 20:16:50,475 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:16:50,475 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:16:50,475 - market_regime - INFO - Regime metrics - Trend strength: -0.13, Volatility: 0.27%, Alignment: 0.33
2025-04-16 20:16:50,475 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:16:50,475 - main - INFO - Regime adjustments: {'leverage_factor': 0.7207359041390661, 'position_size_factor': 0.8005489407451737, 'stop_loss_factor': 1.128038937048046, 'take_profit_factor': 0.882680973397849, 'entry_confidence': 0.6842064319179154}
2025-04-16 20:16:50,475 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:16:50,476 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.011251221149994522, 'orderbook_score': 0.0, 'volume_score': -0.018630954661703072, 'price_action_score': -0.057017679134429435, 'trend_score': 0.0, 'total_score': -0.06439741264613799, 'confidence': 47.987580854808186, 'alignment': 62.5}
2025-04-16 20:16:50,476 - main - INFO - Signal-based decision: WAIT with confidence 47.99% and alignment 62.50%
2025-04-16 20:16:50,476 - main - INFO - Calculated signal scores: {'macd_score': 0.011251221149994522, 'orderbook_score': 0.0, 'volume_score': -0.018630954661703072, 'price_action_score': -0.057017679134429435, 'trend_score': 0.0, 'total_score': -0.06439741264613799, 'confidence': 47.987580854808186, 'alignment': 62.5}
2025-04-16 20:16:50,477 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:16:50,477 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:16:50,477 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:16:50,477 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:16:50,478 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:16:50,478 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.14%, TP: 0.16% (ATR: 0.08%)
2025-04-16 20:16:50,478 - main - INFO - Using adaptive take profit: 0.16%
2025-04-16 20:16:50,478 - main - INFO - Using adaptive stop loss: 0.14%
2025-04-16 20:16:50,479 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 20:16:50,479 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 20:16:50,479 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 20:16:50,479 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Order book shows buying pressure. These factors suggest upward price movement.
2025-04-16 20:16:50,479 - main - INFO - Take Profit: 0.16110600977861572%
2025-04-16 20:16:50,479 - main - INFO - Stop Loss: 0.13962520847479193%
2025-04-16 20:16:50,930 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:51,331 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:51,738 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:52,140 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:52,847 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:53,239 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:53,239 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:16:57,055 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:16:57,451 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:16:57,856 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:16:58,239 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:16:58,942 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:16:59,335 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:16:59,340 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:02,057 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:02,454 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:02,859 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:03,251 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:03,931 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:04,323 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:04,323 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:07,096 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:07,483 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:07,887 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:08,358 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:09,117 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:09,527 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:09,527 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:17,074 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:17,468 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:17,883 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:18,258 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:18,978 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:19,370 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:19,370 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:22,056 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:22,438 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:22,851 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:23,243 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:23,929 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:24,317 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:24,320 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:27,074 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:27,471 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:28,196 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:28,576 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:29,274 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:29,677 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:29,680 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:32,092 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:32,492 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:32,897 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:33,292 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:33,974 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:34,363 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:34,363 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:37,100 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:37,486 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:37,918 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:38,330 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:39,018 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:39,408 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:39,414 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:46,910 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:17:46,910 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:17:46,911 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:17:47,318 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:47,733 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:48,149 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:48,555 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:49,234 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:49,631 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:49,634 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:49,636 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:17:49,638 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:17:49,638 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:17:50,053 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:50,053 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:17:50,483 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:17:50,483 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:17:50,901 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:17:50,901 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:17:50,901 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:17:50,918 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:17:50,932 - main - INFO - Adding 26 exchange features
2025-04-16 20:17:50,933 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:17:50,933 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:17:50,937 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:17:50,941 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:17:50,944 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:17:50,945 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:17:50,945 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 20:17:50,945 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 20:17:50,945 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: -0.13, Alignment: 0.33
2025-04-16 20:17:50,945 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:17:50,945 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:17:50,947 - market_regime - INFO - Regime metrics - Trend strength: -0.13, Volatility: 0.27%, Alignment: 0.33
2025-04-16 20:17:50,947 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:17:50,947 - main - INFO - Regime adjustments: {'leverage_factor': 0.7203578666324966, 'position_size_factor': 0.8003267382922337, 'stop_loss_factor': 1.1287125111504401, 'take_profit_factor': 0.8828782422422085, 'entry_confidence': 0.684387383915483}
2025-04-16 20:17:50,947 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:17:50,947 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.012104577317413616, 'orderbook_score': 0.0, 'volume_score': 0.05168472207314858, 'price_action_score': -0.06008357979398492, 'trend_score': 0.0, 'total_score': 0.0037057195965772838, 'confidence': 50.11580373739304, 'alignment': 50.0}
2025-04-16 20:17:50,947 - main - INFO - Signal-based decision: WAIT with confidence 50.12% and alignment 50.00%
2025-04-16 20:17:50,948 - main - INFO - Calculated signal scores: {'macd_score': 0.012104577317413616, 'orderbook_score': 0.0, 'volume_score': 0.05168472207314858, 'price_action_score': -0.06008357979398492, 'trend_score': 0.0, 'total_score': 0.0037057195965772838, 'confidence': 50.11580373739304, 'alignment': 50.0}
2025-04-16 20:17:50,948 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:17:50,948 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:17:50,948 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:17:50,949 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:17:50,949 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:17:50,950 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.12%, TP: 0.14% (ATR: 0.07%)
2025-04-16 20:17:50,950 - main - INFO - Using adaptive take profit: 0.14%
2025-04-16 20:17:50,950 - main - INFO - Using adaptive stop loss: 0.12%
2025-04-16 20:17:50,950 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 20:17:50,950 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 20:17:50,950 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 20:17:50,950 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Order book shows buying pressure. These factors suggest upward price movement.
2025-04-16 20:17:50,950 - main - INFO - Take Profit: 0.14191838312442237%
2025-04-16 20:17:50,950 - main - INFO - Stop Loss: 0.12299593204116725%
2025-04-16 20:17:51,384 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:51,793 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:52,203 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:52,913 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:53,587 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:17:53,991 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:17:53,997 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:17:57,433 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:17:57,843 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:17:58,251 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:17:58,650 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:17:59,341 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:00,065 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:00,069 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:07,138 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:07,555 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:07,967 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:08,359 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:09,035 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:09,425 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:09,425 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:12,156 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:12,872 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:13,286 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:13,688 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:14,371 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:14,771 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:14,773 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:22,917 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:23,335 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:23,741 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:24,158 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:24,842 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:25,252 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:25,259 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:27,923 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:28,340 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:29,514 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:29,920 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:30,913 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:31,640 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:31,642 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:32,777 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:33,171 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:33,574 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:34,203 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:35,596 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:36,723 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:36,726 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:37,425 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:39,641 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:40,052 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:40,522 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:43,405 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:45,032 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:45,033 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:50,820 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:18:50,820 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:18:50,824 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:18:51,233 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:51,633 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:52,049 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:52,443 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:53,116 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:53,862 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:53,866 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:18:53,869 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:18:53,869 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:18:53,869 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:18:54,279 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:54,279 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:18:55,946 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:18:55,946 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:18:56,356 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:18:56,356 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:18:56,356 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:18:56,356 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:18:56,378 - main - INFO - Adding 26 exchange features
2025-04-16 20:18:56,379 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:18:56,379 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:18:56,385 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:18:56,390 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:18:56,393 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:18:56,394 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:18:56,394 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 20:18:56,395 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 20:18:56,395 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: -0.13, Alignment: 0.33
2025-04-16 20:18:56,395 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:18:56,395 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:18:56,395 - market_regime - INFO - Regime metrics - Trend strength: -0.13, Volatility: 0.27%, Alignment: 0.33
2025-04-16 20:18:56,396 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:18:56,396 - main - INFO - Regime adjustments: {'leverage_factor': 0.7204151314100706, 'position_size_factor': 0.8003603973176555, 'stop_loss_factor': 1.1286104787581959, 'take_profit_factor': 0.882848360137131, 'entry_confidence': 0.6843599734715131}
2025-04-16 20:18:56,396 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:18:56,396 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.014517532858764632, 'orderbook_score': 0.0, 'volume_score': -0.07406115657435886, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.05954362371559423, 'confidence': 48.13926175888768, 'alignment': 62.5}
2025-04-16 20:18:56,396 - main - INFO - Signal-based decision: WAIT with confidence 48.14% and alignment 62.50%
2025-04-16 20:18:56,396 - main - INFO - Calculated signal scores: {'macd_score': 0.014517532858764632, 'orderbook_score': 0.0, 'volume_score': -0.07406115657435886, 'price_action_score': 0.0, 'trend_score': 0.0, 'total_score': -0.05954362371559423, 'confidence': 48.13926175888768, 'alignment': 62.5}
2025-04-16 20:18:56,396 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:18:56,396 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:18:56,396 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:18:56,396 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:18:56,399 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:18:56,399 - main - INFO - Decision for DOGE/USDT:USDT: WAIT
2025-04-16 20:18:56,399 - main - INFO - Explanation: Mixed or insufficient signals. No strong signals detected. Better to wait for clearer market direction.
2025-04-16 20:18:57,178 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:18:57,568 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:18:57,980 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:18:58,366 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:18:59,047 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:18:59,438 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:18:59,442 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:02,175 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:02,603 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:03,787 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:04,474 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:05,149 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:05,853 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:05,863 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:12,177 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:12,571 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:13,283 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:13,670 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:14,353 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:14,751 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:14,756 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:17,178 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:17,883 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:18,304 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:18,705 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:19,397 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:19,808 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:19,808 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:27,163 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:27,566 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:27,983 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:28,361 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:29,049 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:29,456 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:29,456 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:32,946 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:33,653 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:34,378 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:35,244 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:35,915 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:36,612 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:36,616 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:37,170 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:37,560 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:37,964 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:38,349 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:39,645 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:40,044 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:40,046 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:46,595 - data.exchange - INFO - Connected to htx exchange
2025-04-16 20:19:46,596 - main - INFO - Using futures symbol: DOGE/USDT:USDT and spot symbol: DOGE/USDT
2025-04-16 20:19:46,596 - main - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-04-16 20:19:47,015 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:47,795 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:48,518 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:48,987 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:49,668 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:50,075 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:50,079 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:50,087 - main - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-04-16 20:19:50,087 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:19:50,088 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:19:50,507 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:50,508 - multi_timeframe - INFO - Fetched 100 1m candles for DOGE/USDT
2025-04-16 20:19:51,234 - data.exchange - INFO - Fetched 100 5m candles for DOGE/USDT (spot)
2025-04-16 20:19:51,234 - multi_timeframe - INFO - Fetched 100 5m candles for DOGE/USDT
2025-04-16 20:19:51,659 - data.exchange - INFO - Fetched 100 15m candles for DOGE/USDT (spot)
2025-04-16 20:19:51,659 - multi_timeframe - INFO - Fetched 100 15m candles for DOGE/USDT
2025-04-16 20:19:51,659 - main - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-04-16 20:19:51,662 - main - INFO - Using exchange data for DOGE/USDT:USDT
2025-04-16 20:19:51,674 - main - INFO - Adding 26 exchange features
2025-04-16 20:19:51,674 - multi_timeframe - INFO - Initialized multi-timeframe analyzer with timeframes: ['1m', '5m', '15m']
2025-04-16 20:19:51,674 - multi_timeframe - INFO - Timeframe weights: {'1m': 0.2, '5m': 0.3, '15m': 0.5}
2025-04-16 20:19:51,678 - multi_timeframe - INFO - Calculated indicators for 1m timeframe
2025-04-16 20:19:51,682 - multi_timeframe - INFO - Calculated indicators for 5m timeframe
2025-04-16 20:19:51,688 - multi_timeframe - INFO - Calculated indicators for 15m timeframe
2025-04-16 20:19:51,688 - multi_timeframe - INFO - Analyzed trend for 1m timeframe: strong_bearish (score: -1.00)
2025-04-16 20:19:51,689 - multi_timeframe - INFO - Analyzed trend for 5m timeframe: bearish (score: -0.33)
2025-04-16 20:19:51,689 - multi_timeframe - INFO - Analyzed trend for 15m timeframe: bullish (score: 0.33)
2025-04-16 20:19:51,689 - main - INFO - Multi-timeframe analysis: Trend direction: neutral, Strength: -0.13, Alignment: 0.33
2025-04-16 20:19:51,689 - market_regime - INFO - Initialized market regime detector
2025-04-16 20:19:51,689 - market_regime - INFO - Detected market regime: low_volatility
2025-04-16 20:19:51,689 - market_regime - INFO - Regime metrics - Trend strength: -0.13, Volatility: 0.28%, Alignment: 0.33
2025-04-16 20:19:51,689 - main - INFO - Detected market regime: low_volatility
2025-04-16 20:19:51,689 - main - INFO - Regime adjustments: {'leverage_factor': 0.7201217534375094, 'position_size_factor': 0.8001879559487204, 'stop_loss_factor': 1.1291332094425757, 'take_profit_factor': 0.883001451649486, 'entry_confidence': 0.6845004022100768}
2025-04-16 20:19:51,689 - signal_scoring - INFO - Initialized signal scoring system with smoothing=3, longer_timeframe=True
2025-04-16 20:19:51,690 - signal_scoring - INFO - Calculated signal scores: {'macd_score': 0.0161139880133283, 'orderbook_score': 0.0, 'volume_score': 0.01331221579632698, 'price_action_score': -0.060074100830862565, 'trend_score': 0.0, 'total_score': -0.030647897021207286, 'confidence': 49.042253218087275, 'alignment': 62.5}
2025-04-16 20:19:51,690 - main - INFO - Signal-based decision: WAIT with confidence 49.04% and alignment 62.50%
2025-04-16 20:19:51,690 - main - INFO - Calculated signal scores: {'macd_score': 0.0161139880133283, 'orderbook_score': 0.0, 'volume_score': 0.01331221579632698, 'price_action_score': -0.060074100830862565, 'trend_score': 0.0, 'total_score': -0.030647897021207286, 'confidence': 49.042253218087275, 'alignment': 62.5}
2025-04-16 20:19:51,691 - llama.runner - WARNING - llama.cpp binary not found at C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-04-16 20:19:51,691 - llama.runner - INFO - You'll need to compile llama.cpp and set the correct path.
2025-04-16 20:19:51,691 - llama.runner - INFO - Using mock LLaMA implementation as configured
2025-04-16 20:19:51,692 - llama.runner - INFO - Running mock LLaMA inference
2025-04-16 20:19:51,692 - adaptive_risk - INFO - Initialized adaptive risk manager
2025-04-16 20:19:51,693 - adaptive_risk - INFO - Calculated volatility-based stops: SL: 0.14%, TP: 0.16% (ATR: 0.08%)
2025-04-16 20:19:51,694 - main - INFO - Using adaptive take profit: 0.16%
2025-04-16 20:19:51,694 - main - INFO - Using adaptive stop loss: 0.14%
2025-04-16 20:19:51,694 - adaptive_risk - INFO - Calculated position scaling: 0 stages, total size: 1.000000
2025-04-16 20:19:51,694 - main - INFO - Position scaling strategy: 0 stages
2025-04-16 20:19:51,694 - main - INFO - Decision for DOGE/USDT:USDT: LONG
2025-04-16 20:19:51,694 - main - INFO - Explanation: Multiple bullish signals detected: MACD shows bullish momentum. Order book shows buying pressure. These factors suggest upward price movement.
2025-04-16 20:19:51,695 - main - INFO - Take Profit: 0.1585041333496538%
2025-04-16 20:19:51,695 - main - INFO - Stop Loss: 0.13737024890303332%
2025-04-16 20:19:52,135 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT (spot)
2025-04-16 20:19:52,523 - data.exchange - INFO - Fetched 100 1m candles for DOGE/USDT:USDT (future)
2025-04-16 20:19:53,707 - data.exchange - INFO - Fetched order book for DOGE/USDT (spot)
2025-04-16 20:19:54,091 - data.exchange - INFO - Fetched order book for DOGE/USDT:USDT (future)
2025-04-16 20:19:54,764 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT (spot)
2025-04-16 20:19:55,162 - data.exchange - INFO - Fetched 100 trades for DOGE/USDT:USDT (future)
2025-04-16 20:19:55,165 - data.exchange - INFO - Fetched combined data for DOGE/USDT (spot) and DOGE/USDT:USDT (futures)
2025-04-16 20:19:55,258 - __main__ - INFO - Application closed normally
2025-04-16 20:25:41,260 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 20:25:41,777 - __main__ - INFO - Initializing components...
2025-04-16 20:25:42,293 - __main__ - INFO - Loading configuration...
2025-04-16 20:25:42,798 - __main__ - INFO - Connecting to data sources...
2025-04-16 20:25:43,300 - __main__ - INFO - Creating main window...
2025-04-16 20:25:43,301 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 20:25:43,301 - main_window - INFO - Initializing MainWindow
2025-04-16 20:25:43,301 - main_window - INFO - Creating QSettings
2025-04-16 20:25:43,302 - main_window - INFO - Creating ThemeManager
2025-04-16 20:25:43,302 - main_window - INFO - Creating NotificationManager
2025-04-16 20:25:43,303 - main_window - INFO - Calling init_ui
2025-04-16 20:25:43,304 - main_window - INFO - Setting window title and geometry
2025-04-16 20:25:43,304 - main_window - INFO - Creating menu bar
2025-04-16 20:25:43,304 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 20:25:43,304 - main_window - INFO - Creating central widget
2025-04-16 20:25:43,304 - main_window - INFO - Creating control toolbar
2025-04-16 20:25:43,304 - main_window - INFO - Adding bot buttons
2025-04-16 20:25:43,305 - main_window - INFO - Creating bot tab widget
2025-04-16 20:25:43,306 - main_window - INFO - Creating status bar
2025-04-16 20:25:43,306 - main_window - INFO - Applying theme
2025-04-16 20:25:43,316 - main_window - INFO - Restoring window geometry
2025-04-16 20:25:43,322 - main_window - INFO - Adding initial simulation bot
2025-04-16 20:25:43,322 - main_window - INFO - Adding new simulation bot
2025-04-16 20:25:43,322 - main_window - INFO - Using default bot name
2025-04-16 20:25:43,322 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 20:25:43,322 - main_window - INFO - Generating unique ID
2025-04-16 20:25:43,322 - main_window - INFO - Creating BotTabWidget with ID eeeeaa67-d9eb-44d3-b1cb-b1adc0b04420 and name Simulation Bot 1
2025-04-16 20:25:43,326 - main_window - ERROR - Error creating BotTabWidget: No module named 'parameter_panel'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 11, in <module>
    from parameter_panel import ParameterPanel
ModuleNotFoundError: No module named 'parameter_panel'
2025-04-16 20:25:43,326 - __main__ - ERROR - Error creating main window: No module named 'parameter_panel'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 88, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 84, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 145, in init_ui
    self.add_new_bot("simulation")
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 11, in <module>
    from parameter_panel import ParameterPanel
ModuleNotFoundError: No module named 'parameter_panel'
2025-04-16 20:25:43,328 - __main__ - ERROR - Fatal error: No module named 'parameter_panel'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 88, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 84, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 145, in init_ui
    self.add_new_bot("simulation")
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 11, in <module>
    from parameter_panel import ParameterPanel
ModuleNotFoundError: No module named 'parameter_panel'
2025-04-16 20:27:02,136 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 20:27:02,659 - __main__ - INFO - Initializing components...
2025-04-16 20:27:03,173 - __main__ - INFO - Loading configuration...
2025-04-16 20:27:03,675 - __main__ - INFO - Connecting to data sources...
2025-04-16 20:27:04,179 - __main__ - INFO - Creating main window...
2025-04-16 20:27:04,179 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 20:27:04,179 - main_window - INFO - Initializing MainWindow
2025-04-16 20:27:04,179 - main_window - INFO - Creating QSettings
2025-04-16 20:27:04,179 - main_window - INFO - Creating ThemeManager
2025-04-16 20:27:04,179 - main_window - INFO - Creating NotificationManager
2025-04-16 20:27:04,183 - main_window - INFO - Calling init_ui
2025-04-16 20:27:04,184 - main_window - INFO - Setting window title and geometry
2025-04-16 20:27:04,184 - main_window - INFO - Creating menu bar
2025-04-16 20:27:04,184 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 20:27:04,184 - main_window - INFO - Creating central widget
2025-04-16 20:27:04,184 - main_window - INFO - Creating control toolbar
2025-04-16 20:27:04,186 - main_window - INFO - Adding bot buttons
2025-04-16 20:27:04,186 - main_window - INFO - Creating bot tab widget
2025-04-16 20:27:04,186 - main_window - INFO - Creating status bar
2025-04-16 20:27:04,186 - main_window - INFO - Applying theme
2025-04-16 20:27:04,192 - main_window - INFO - Restoring window geometry
2025-04-16 20:27:04,201 - main_window - INFO - Adding initial simulation bot
2025-04-16 20:27:04,201 - main_window - INFO - Adding new simulation bot
2025-04-16 20:27:04,201 - main_window - INFO - Using default bot name
2025-04-16 20:27:04,201 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 20:27:04,201 - main_window - INFO - Generating unique ID
2025-04-16 20:27:04,201 - main_window - INFO - Creating BotTabWidget with ID a4bbe25f-c75b-4308-a76e-a5eef97b8de9 and name Simulation Bot 1
2025-04-16 20:27:05,342 - main_window - ERROR - Error creating BotTabWidget: cannot import name 'USE_MOCK_LLAMA' from 'config.config' (C:\Users\<USER>\Documents\dev\Epinnox_v6\config\config.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 18, in <module>
    from trading_system_interface import TradingSystemInterface
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\trading_system_interface.py", line 12, in <module>
    from main import run_trading_system
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main.py", line 8, in <module>
    from config.config import config
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\config\__init__.py", line 4, in <module>
    from config.config import (
ImportError: cannot import name 'USE_MOCK_LLAMA' from 'config.config' (C:\Users\<USER>\Documents\dev\Epinnox_v6\config\config.py)
2025-04-16 20:27:05,342 - __main__ - ERROR - Error creating main window: cannot import name 'USE_MOCK_LLAMA' from 'config.config' (C:\Users\<USER>\Documents\dev\Epinnox_v6\config\config.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 88, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 84, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 145, in init_ui
    self.add_new_bot("simulation")
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 18, in <module>
    from trading_system_interface import TradingSystemInterface
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\trading_system_interface.py", line 12, in <module>
    from main import run_trading_system
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main.py", line 8, in <module>
    from config.config import config
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\config\__init__.py", line 4, in <module>
    from config.config import (
ImportError: cannot import name 'USE_MOCK_LLAMA' from 'config.config' (C:\Users\<USER>\Documents\dev\Epinnox_v6\config\config.py)
2025-04-16 20:27:05,346 - __main__ - ERROR - Fatal error: cannot import name 'USE_MOCK_LLAMA' from 'config.config' (C:\Users\<USER>\Documents\dev\Epinnox_v6\config\config.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 88, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 84, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 145, in init_ui
    self.add_new_bot("simulation")
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 18, in <module>
    from trading_system_interface import TradingSystemInterface
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\trading_system_interface.py", line 12, in <module>
    from main import run_trading_system
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main.py", line 8, in <module>
    from config.config import config
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\config\__init__.py", line 4, in <module>
    from config.config import (
ImportError: cannot import name 'USE_MOCK_LLAMA' from 'config.config' (C:\Users\<USER>\Documents\dev\Epinnox_v6\config\config.py)
2025-04-16 20:51:33,344 - __main__ - INFO - Starting Epinnox Trading System
2025-04-16 20:51:33,928 - __main__ - INFO - Initializing components...
2025-04-16 20:51:34,438 - __main__ - INFO - Loading configuration...
2025-04-16 20:51:34,944 - __main__ - INFO - Connecting to data sources...
2025-04-16 20:51:35,444 - __main__ - INFO - Creating main window...
2025-04-16 20:51:35,444 - __main__ - INFO - Initializing MainWindow class...
2025-04-16 20:51:35,444 - main_window - INFO - Initializing MainWindow
2025-04-16 20:51:35,444 - main_window - INFO - Creating QSettings
2025-04-16 20:51:35,444 - main_window - INFO - Creating ThemeManager
2025-04-16 20:51:35,444 - main_window - INFO - Creating NotificationManager
2025-04-16 20:51:35,449 - main_window - INFO - Calling init_ui
2025-04-16 20:51:35,449 - main_window - INFO - Setting window title and geometry
2025-04-16 20:51:35,449 - main_window - INFO - Creating menu bar
2025-04-16 20:51:35,449 - main_window - INFO - Creating keyboard shortcuts
2025-04-16 20:51:35,451 - main_window - INFO - Creating central widget
2025-04-16 20:51:35,452 - main_window - INFO - Creating control toolbar
2025-04-16 20:51:35,452 - main_window - INFO - Adding bot buttons
2025-04-16 20:51:35,452 - main_window - INFO - Creating bot tab widget
2025-04-16 20:51:35,453 - main_window - INFO - Creating status bar
2025-04-16 20:51:35,453 - main_window - INFO - Applying theme
2025-04-16 20:51:35,462 - main_window - INFO - Restoring window geometry
2025-04-16 20:51:35,470 - main_window - INFO - Adding initial simulation bot
2025-04-16 20:51:35,470 - main_window - INFO - Adding new simulation bot
2025-04-16 20:51:35,470 - main_window - INFO - Using default bot name
2025-04-16 20:51:35,470 - main_window - INFO - Bot name: Simulation Bot 1
2025-04-16 20:51:35,470 - main_window - INFO - Generating unique ID
2025-04-16 20:51:35,470 - main_window - INFO - Creating BotTabWidget with ID e67ef20f-dbf3-4de8-bb84-11eed330b822 and name Simulation Bot 1
2025-04-16 20:51:35,473 - main_window - ERROR - Error creating BotTabWidget: No module named 'parameter_panel'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 11, in <module>
    from parameter_panel import ParameterPanel
ModuleNotFoundError: No module named 'parameter_panel'
2025-04-16 20:51:35,490 - __main__ - ERROR - Error creating main window: No module named 'parameter_panel'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 88, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 84, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 145, in init_ui
    self.add_new_bot("simulation")
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 11, in <module>
    from parameter_panel import ParameterPanel
ModuleNotFoundError: No module named 'parameter_panel'
2025-04-16 20:51:35,490 - __main__ - ERROR - Fatal error: No module named 'parameter_panel'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\app.py", line 88, in main
    main_window = MainWindow()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 84, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 145, in init_ui
    self.add_new_bot("simulation")
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\main_window.py", line 316, in add_new_bot
    from gui.bot_tab_widget import BotTabWidget
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\gui\bot_tab_widget.py", line 11, in <module>
    from parameter_panel import ParameterPanel
ModuleNotFoundError: No module named 'parameter_panel'
2025-06-15 08:12:04,904 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:12:10,377 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:12:10,377 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:12:12,090 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:12:12,596 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:12,608 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:12:12,625 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:12:12,642 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:12:12,642 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:12:12,642 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor':
                                            0.8223036861454263, 'stop_loss_factor':
                                            1.0847483474386233, 'take_profit_factor':
                                            0.9009952904175134, 'entry_confidence':
                                            0.6649930940148552}
2025-06-15 08:12:12,642 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:12:12,642 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0,
                                            'volume_score': 0.060172236685842304,
                                            'price_action_score': -0.06004632561372948,
                                            'trend_score': 0.0, 'total_score':
                                            -0.024005472579932657, 'confidence': 49.24982898187711,
                                            'alignment': 50.0}
2025-06-15 08:12:12,644 - __main__ - INFO - Decision for DOGE/USDT: WAIT
2025-06-15 08:12:12,644 - __main__ - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (2 each). Better to
                                            wait for clearer market direction.
2025-06-15 08:12:47,859 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:12:47,912 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:12:47,912 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:12:50,012 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:12:50,016 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:12:50,024 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:12:50,033 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:12:50,050 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:12:50,051 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:12:50,051 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor':
                                            0.8223036861454263, 'stop_loss_factor':
                                            1.0847483474386233, 'take_profit_factor':
                                            0.9009952904175134, 'entry_confidence':
                                            0.6649930940148552}
2025-06-15 08:12:50,052 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:12:50,052 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0,
                                            'volume_score': 0.060172236685842304,
                                            'price_action_score': -0.06004632561372948,
                                            'trend_score': 0.0, 'total_score':
                                            -0.024005472579932657, 'confidence': 49.24982898187711,
                                            'alignment': 50.0}
2025-06-15 08:12:50,055 - __main__ - INFO - Using adaptive take profit: 0.18%
2025-06-15 08:12:50,055 - __main__ - INFO - Using adaptive stop loss: 0.16%
2025-06-15 08:12:50,055 - __main__ - INFO - Position scaling strategy: 0 stages
2025-06-15 08:12:50,055 - __main__ - INFO - Decision for DOGE/USDT: SHORT
2025-06-15 08:12:50,055 - __main__ - INFO - Explanation: Multiple bearish signals detected: MACD shows bearish momentum. Recent trades show
                                            selling activity. Futures market has higher buy ratio
                                            than spot, suggesting potential reversal. These factors
                                            suggest downward price movement.
2025-06-15 08:12:50,056 - __main__ - INFO - Take Profit: 0.1815470084635197%
2025-06-15 08:12:50,056 - __main__ - INFO - Stop Loss: 0.15734074066837742%
2025-06-15 08:12:50,056 - __main__ - INFO - Trading system finished
2025-06-15 08:14:09,696 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:14:09,697 - __main__ - INFO - Running continuously with 60s delay
2025-06-15 08:14:09,749 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:14:09,749 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:14:11,551 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:14:11,556 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:14:11,564 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:14:11,573 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:14:11,590 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:14:11,591 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:14:11,591 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor':
                                            0.8223036861454263, 'stop_loss_factor':
                                            1.0847483474386233, 'take_profit_factor':
                                            0.9009952904175134, 'entry_confidence':
                                            0.6649930940148552}
2025-06-15 08:14:11,592 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:14:11,592 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0,
                                            'volume_score': 0.060172236685842304,
                                            'price_action_score': -0.06004632561372948,
                                            'trend_score': 0.0, 'total_score':
                                            -0.024005472579932657, 'confidence': 49.24982898187711,
                                            'alignment': 50.0}
2025-06-15 08:14:11,594 - __main__ - INFO - Decision for DOGE/USDT: WAIT
2025-06-15 08:14:11,594 - __main__ - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to
                                            wait for clearer market direction.
2025-06-15 08:15:11,710 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:15:11,710 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:15:13,761 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:15:13,767 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:15:13,777 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:15:13,786 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:15:13,801 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:15:13,802 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:15:13,802 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449226064409699, 'position_size_factor':
                                            0.8223036861454263, 'stop_loss_factor':
                                            1.0847483474386233, 'take_profit_factor':
                                            0.9009952904175134, 'entry_confidence':
                                            0.6649930940148552}
2025-06-15 08:15:13,803 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.25% and alignment 50.00%
2025-06-15 08:15:13,803 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.024131383652045483, 'orderbook_score': 0.0,
                                            'volume_score': 0.060172236685842304,
                                            'price_action_score': -0.06004632561372948,
                                            'trend_score': 0.0, 'total_score':
                                            -0.024005472579932657, 'confidence': 49.24982898187711,
                                            'alignment': 50.0}
2025-06-15 08:15:13,804 - __main__ - INFO - Decision for DOGE/USDT: WAIT
2025-06-15 08:15:13,806 - __main__ - INFO - Explanation: Mixed or insufficient signals. Equal bullish and bearish signals (1 each). Better to
                                            wait for clearer market direction.
2025-06-15 08:17:15,046 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:17:21,400 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:17:21,400 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:17:23,096 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:17:23,602 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:17:23,615 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:17:23,624 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:17:23,640 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:17:23,641 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:17:23,642 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449056553885, 'position_size_factor': 0.8222942780637245,
                                            'stop_loss_factor': 1.0847785358271018,
                                            'take_profit_factor': 0.9010064150614908,
                                            'entry_confidence': 0.6650006452383264}
2025-06-15 08:17:23,642 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.65% and alignment 62.50%
2025-06-15 08:17:23,642 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.023958910039551033, 'orderbook_score': 0.0,
                                            'volume_score': -0.047429296436959055,
                                            'price_action_score': 0.0600427996690159, 'trend_score':
                                            0.0, 'total_score': -0.011345406807494192, 'confidence':
                                            49.64545603726581, 'alignment': 62.5}
2025-06-15 08:17:23,646 - __main__ - INFO - Using adaptive take profit: 0.21%
2025-06-15 08:17:23,646 - __main__ - INFO - Using adaptive stop loss: 0.18%
2025-06-15 08:17:23,647 - __main__ - INFO - Position scaling strategy: 0 stages
2025-06-15 08:17:23,647 - __main__ - INFO - Decision for DOGE/USDT: SHORT
2025-06-15 08:17:23,647 - __main__ - INFO - Explanation: Multiple bearish signals detected: MACD shows bearish momentum. Order book shows
                                            selling pressure. These factors suggest downward price
                                            movement.
2025-06-15 08:17:23,647 - __main__ - INFO - Take Profit: 0.2053085530305893%
2025-06-15 08:17:23,647 - __main__ - INFO - Stop Loss: 0.17793407929317845%
2025-06-15 08:17:23,648 - __main__ - INFO - Trading system finished
2025-06-15 08:18:28,760 - __main__ - INFO - Starting trading system for DOGE/USDT
2025-06-15 08:18:28,811 - __main__ - INFO - Using spot symbol: DOGE/USDT and futures symbol: DOGE/USDT:USDT
2025-06-15 08:18:28,811 - __main__ - INFO - Using live trades data for DOGE/USDT and DOGE/USDT:USDT
2025-06-15 08:18:30,915 - __main__ - INFO - Fetched 100 rows of exchange data for DOGE/USDT
2025-06-15 08:18:30,922 - __main__ - INFO - Fetched data for 3 timeframes: ['1m', '5m', '15m']
2025-06-15 08:18:30,931 - __main__ - INFO - Using exchange data for DOGE/USDT
2025-06-15 08:18:30,941 - __main__ - INFO - Adding 26 exchange features
2025-06-15 08:18:30,959 - __main__ - INFO - Multi-timeframe analysis: Trend direction: bullish, Strength: 0.33, Alignment: 0.67
2025-06-15 08:18:30,959 - __main__ - INFO - Detected market regime: low_volatility
2025-06-15 08:18:30,959 - __main__ - INFO - Regime adjustments: {'leverage_factor': 0.7449056553885, 'position_size_factor': 0.8222942780637245,
                                            'stop_loss_factor': 1.0847785358271018,
                                            'take_profit_factor': 0.9010064150614908,
                                            'entry_confidence': 0.6650006452383264}
2025-06-15 08:18:30,961 - __main__ - INFO - Signal-based decision: WAIT with confidence 49.65% and alignment 62.50%
2025-06-15 08:18:30,962 - __main__ - INFO - Calculated signal scores: {'macd_score': -0.023958910039551033, 'orderbook_score': 0.0,
                                            'volume_score': -0.047429296436959055,
                                            'price_action_score': 0.0600427996690159, 'trend_score':
                                            0.0, 'total_score': -0.011345406807494192, 'confidence':
                                            49.64545603726581, 'alignment': 62.5}
2025-06-15 08:18:41,052 - __main__ - INFO - Using adaptive take profit: 0.21%
2025-06-15 08:18:41,052 - __main__ - INFO - Using adaptive stop loss: 0.18%
2025-06-15 08:18:41,052 - __main__ - INFO - Position scaling strategy: 0 stages
2025-06-15 08:18:41,052 - __main__ - INFO - Decision for DOGE/USDT: LONG
2025-06-15 08:18:41,052 - __main__ - INFO - Explanation: The market regime is 'low volatility', which usually suggests a more stable and
                                            predictable environment for trading strategies like long
                                            positions to succeed, as lower volatility often
                                            indicates less aggressive price swings that can be
                                            exploited by bullish trends. Furthermore, the Multi-
                                            Timeframe Analysis shows high confidence (116.67%) in an
                                            overall Bullish Trend Direction with a moderate strength
                                            of 0.33 and alignment at 0.67 indicating signal
                                            agreement is above average which further supports taking
                                            a long position. The bullish price action, along with
                                            the positive indicators like MACD trending towards
                                            neutral (which typically means no immediate bearish
                                            reversal) despite being slightly negative, suggests that
                                            it's in favor of entering or holding onto a LONG
                                            position here for potential gains as reflected by this
                                            multi-faceted analysis.
2025-06-15 08:18:41,052 - __main__ - INFO - Take Profit: 0.2053085530305893%
2025-06-15 08:18:41,052 - __main__ - INFO - Stop Loss: 0.17793407929317845%
2025-06-15 08:18:41,052 - __main__ - INFO - Trading system finished
