# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyQt
*.ui

# Logs
*.log
logs/*.log

# Cache
cache/*.json

# Config files with sensitive data
config/credentials.yaml
config/models_config.yaml

# Trading data
trades/*.csv
simulation_charts/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
temp_*
*.tmp
system_status.json
gui_data.json
trading_parameters.json
