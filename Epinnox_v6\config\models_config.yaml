api_keys:
  openai: ********************************************************************************************************************************************************************
models:
  mock:
  - name: Mock
    type: mock
    temperature: 0.7
    max_tokens: 512
  transformers:
  - name: Phi-2-GPU
    type: transformers
    model_path: models/models/phi-2
    model_type: causal_lm
    temperature: 0.7
    max_tokens: 512
    trust_remote_code: true
  - name: TinyLlama-GPU
    type: transformers
    model_path: models/models/tinyllama-1.1b
    model_type: causal_lm
    temperature: 0.7
    max_tokens: 512
    trust_remote_code: true
  chatgpt:
  - max_tokens: 512
    model: gpt-3.5-turbo
    name: GPT-4
    temperature: 0.7
    type: chatgpt
  - max_tokens: 512
    model: gpt-3.5-turbo
    name: GPT-3.5-Turbo
    temperature: 0.7
    type: chatgpt
  llama:
  - bin_path: C:/Users/<USER>/Documents/dev/llama.cpp/build/bin/Release/main.exe
    context_size: 4096
    max_tokens: 512
    model_path: C:/Users/<USER>/.lmstudio/models/lmstudio-community/Phi-3.1-mini-128k-instruct-GGUF/Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
    model_type: instruct
    name: Phi-3.1-Mini-LMStudio
    temperature: 0.1
    type: llama
  - bin_path: C:/Users/<USER>/Documents/dev/llama.cpp/build/bin/Release/main.exe
    context_size: 4096
    max_tokens: 512
    model_path: models/Llama-4-Scout-17B-16E-Instruct.gguf
    model_type: instruct
    name: LLaMA-4-Scout
    temperature: 0.7
    type: llama
  - bin_path: C:/Users/<USER>/Documents/dev/llama.cpp/build/bin/Release/main.exe
    context_size: 2048
    max_tokens: 512
    model_path: models/llama-3-8b-instruct.gguf
    model_type: instruct
    name: LLaMA-3-8B
    temperature: 0.7
    type: llama
  - bin_path: C:/Users/<USER>/Documents/dev/llama.cpp/build/bin/Release/main.exe
    context_size: 2048
    max_tokens: 512
    model_path: models/Meta-Llama-3-8B-Instruct-IQ2_XS.gguf
    model_type: instruct
    name: Meta-LLaMA-3-8B-IQ2
    temperature: 0.7
    type: llama
  - bin_path: C:/Users/<USER>/Documents/dev/llama.cpp/build/bin/Release/main.exe
    context_size: 2048
    max_tokens: 512
    model_path: models/Meta-Llama-3-8B-Instruct-Q5_K_S.gguf
    model_type: instruct
    name: Meta-LLaMA-3-8B-Q5
    temperature: 0.7
    type: llama
