"""
Timeframe Analysis Panel
Displays multi-timeframe analysis results and trend alignment
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QProgressBar, QFrame, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class TimeframeAnalysisPanel(QWidget):
    """Panel displaying multi-timeframe analysis and trend alignment"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Overall Trend Summary
        summary_group = QGroupBox("📊 Overall Trend Summary")
        summary_layout = QGridLayout(summary_group)
        
        # Overall trend indicators
        self.overall_trend_label = QLabel("NEUTRAL")
        self.overall_trend_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.overall_trend_label.setAlignment(Qt.AlignCenter)
        
        self.trend_strength_label = QLabel("Strength: 0%")
        self.trend_strength_label.setFont(QFont("Arial", 12))
        
        self.alignment_label = QLabel("Alignment: 0%")
        self.alignment_label.setFont(QFont("Arial", 12))
        
        # Alignment progress bar
        self.alignment_bar = QProgressBar()
        self.alignment_bar.setRange(0, 100)
        self.alignment_bar.setValue(0)
        self.alignment_bar.setTextVisible(True)
        
        summary_layout.addWidget(QLabel("Overall Trend:"), 0, 0)
        summary_layout.addWidget(self.overall_trend_label, 0, 1)
        summary_layout.addWidget(self.trend_strength_label, 0, 2)
        
        summary_layout.addWidget(QLabel("Timeframe Alignment:"), 1, 0)
        summary_layout.addWidget(self.alignment_bar, 1, 1, 1, 2)
        
        layout.addWidget(summary_group)
        
        # Individual Timeframe Analysis
        timeframes_group = QGroupBox("⏱️ Individual Timeframe Analysis")
        timeframes_layout = QVBoxLayout(timeframes_group)
        
        # Timeframes table
        self.timeframes_table = QTableWidget(3, 4)
        self.timeframes_table.setHorizontalHeaderLabels([
            "Timeframe", "Direction", "Strength", "Status"
        ])
        self.timeframes_table.horizontalHeader().setStretchLastSection(True)
        self.timeframes_table.setMaximumHeight(150)
        
        # Set timeframe rows
        timeframes = ['1m', '5m', '15m']
        for i, tf in enumerate(timeframes):
            self.timeframes_table.setItem(i, 0, QTableWidgetItem(tf))
            self.timeframes_table.setItem(i, 1, QTableWidgetItem("NEUTRAL"))
            self.timeframes_table.setItem(i, 2, QTableWidgetItem("0%"))
            self.timeframes_table.setItem(i, 3, QTableWidgetItem("🔵"))
        
        timeframes_layout.addWidget(self.timeframes_table)
        layout.addWidget(timeframes_group)
        
        # Trend Consensus
        consensus_group = QGroupBox("🎯 Trend Consensus")
        consensus_layout = QGridLayout(consensus_group)
        
        # Consensus indicators
        self.bullish_count = QLabel("Bullish: 0/3")
        self.bearish_count = QLabel("Bearish: 0/3")
        self.neutral_count = QLabel("Neutral: 3/3")
        
        self.consensus_strength = QLabel("Consensus Strength: Weak")
        self.consensus_strength.setFont(QFont("Arial", 12, QFont.Bold))
        
        consensus_layout.addWidget(self.bullish_count, 0, 0)
        consensus_layout.addWidget(self.bearish_count, 0, 1)
        consensus_layout.addWidget(self.neutral_count, 0, 2)
        consensus_layout.addWidget(self.consensus_strength, 1, 0, 1, 3)
        
        layout.addWidget(consensus_group)
        
        # Trend Change Detection
        change_group = QGroupBox("🔄 Trend Change Detection")
        change_layout = QVBoxLayout(change_group)
        
        self.trend_change_label = QLabel("No recent trend changes detected")
        self.trend_change_label.setWordWrap(True)
        
        self.momentum_label = QLabel("Momentum: Neutral")
        self.momentum_label.setFont(QFont("Arial", 11, QFont.Bold))
        
        change_layout.addWidget(self.trend_change_label)
        change_layout.addWidget(self.momentum_label)
        
        layout.addWidget(change_group)
    
    def update_data(self, data):
        """Update the panel with new timeframe analysis data"""
        try:
            # Update overall trend summary
            overall_trend = data.get('overall_trend', 'neutral').upper()
            self.overall_trend_label.setText(overall_trend)
            
            # Color code overall trend
            if overall_trend == 'BULLISH':
                self.overall_trend_label.setStyleSheet("color: #00ff88; background-color: rgba(0, 255, 136, 20); padding: 5px; border-radius: 3px;")
            elif overall_trend == 'BEARISH':
                self.overall_trend_label.setStyleSheet("color: #ff4444; background-color: rgba(255, 68, 68, 20); padding: 5px; border-radius: 3px;")
            else:
                self.overall_trend_label.setStyleSheet("color: #ffaa00; background-color: rgba(255, 170, 0, 20); padding: 5px; border-radius: 3px;")
            
            # Update trend strength
            trend_strength = data.get('trend_strength', 0.0) * 100
            self.trend_strength_label.setText(f"Strength: {trend_strength:.1f}%")
            
            # Update alignment
            alignment = data.get('alignment_percentage', 0.0)
            self.alignment_bar.setValue(int(alignment))
            self.alignment_label.setText(f"Alignment: {alignment:.1f}%")
            
            # Color code alignment bar
            if alignment >= 80:
                self.alignment_bar.setStyleSheet("QProgressBar::chunk { background-color: #00ff88; }")
            elif alignment >= 60:
                self.alignment_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
            else:
                self.alignment_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
            
            # Update individual timeframes
            trends = data.get('trends', {})
            self.update_timeframes_table(trends)
            
            # Update consensus
            self.update_consensus(trends)
            
            # Update trend change detection
            self.update_trend_changes(data)
            
        except Exception as e:
            print(f"Error updating timeframe analysis panel: {e}")
    
    def update_timeframes_table(self, trends):
        """Update the timeframes table"""
        try:
            timeframes = ['1m', '5m', '15m']
            
            for i, tf in enumerate(timeframes):
                if tf in trends:
                    trend_data = trends[tf]
                    direction = trend_data.get('direction', 'neutral').upper()
                    strength = trend_data.get('strength', 0.0) * 100
                    
                    # Update direction
                    direction_item = QTableWidgetItem(direction)
                    if direction == 'BULLISH':
                        direction_item.setBackground(QColor(0, 255, 136, 50))
                        status_item = QTableWidgetItem("🟢")
                    elif direction == 'BEARISH':
                        direction_item.setBackground(QColor(255, 68, 68, 50))
                        status_item = QTableWidgetItem("🔴")
                    else:
                        direction_item.setBackground(QColor(255, 170, 0, 50))
                        status_item = QTableWidgetItem("🟡")
                    
                    # Update strength
                    strength_item = QTableWidgetItem(f"{strength:.1f}%")
                    
                    self.timeframes_table.setItem(i, 1, direction_item)
                    self.timeframes_table.setItem(i, 2, strength_item)
                    self.timeframes_table.setItem(i, 3, status_item)
                    
        except Exception as e:
            print(f"Error updating timeframes table: {e}")
    
    def update_consensus(self, trends):
        """Update the consensus indicators"""
        try:
            bullish_count = 0
            bearish_count = 0
            neutral_count = 0
            
            for trend_data in trends.values():
                direction = trend_data.get('direction', 'neutral').lower()
                if direction == 'bullish':
                    bullish_count += 1
                elif direction == 'bearish':
                    bearish_count += 1
                else:
                    neutral_count += 1
            
            total = len(trends)
            
            # Update counts
            self.bullish_count.setText(f"Bullish: {bullish_count}/{total}")
            self.bullish_count.setStyleSheet("color: #00ff88;")
            
            self.bearish_count.setText(f"Bearish: {bearish_count}/{total}")
            self.bearish_count.setStyleSheet("color: #ff4444;")
            
            self.neutral_count.setText(f"Neutral: {neutral_count}/{total}")
            self.neutral_count.setStyleSheet("color: #ffaa00;")
            
            # Determine consensus strength
            max_count = max(bullish_count, bearish_count, neutral_count)
            consensus_percentage = (max_count / total) * 100 if total > 0 else 0
            
            if consensus_percentage >= 100:
                strength = "Very Strong"
                color = "#00ff88"
            elif consensus_percentage >= 67:
                strength = "Strong"
                color = "#88ff00"
            elif consensus_percentage >= 50:
                strength = "Moderate"
                color = "#ffaa00"
            else:
                strength = "Weak"
                color = "#ff4444"
            
            self.consensus_strength.setText(f"Consensus Strength: {strength} ({consensus_percentage:.0f}%)")
            self.consensus_strength.setStyleSheet(f"color: {color};")
            
        except Exception as e:
            print(f"Error updating consensus: {e}")
    
    def update_trend_changes(self, data):
        """Update trend change detection"""
        try:
            # This would be enhanced with actual trend change detection logic
            # For now, we'll show basic momentum information
            
            trend_strength = data.get('trend_strength', 0.0)
            alignment = data.get('alignment_percentage', 0.0)
            
            if alignment >= 80 and trend_strength >= 0.7:
                self.trend_change_label.setText("Strong trend confirmation detected across all timeframes")
                self.momentum_label.setText("Momentum: Very Strong")
                self.momentum_label.setStyleSheet("color: #00ff88;")
            elif alignment >= 60:
                self.trend_change_label.setText("Moderate trend alignment detected")
                self.momentum_label.setText("Momentum: Moderate")
                self.momentum_label.setStyleSheet("color: #ffaa00;")
            else:
                self.trend_change_label.setText("Mixed signals across timeframes - no clear trend")
                self.momentum_label.setText("Momentum: Weak")
                self.momentum_label.setStyleSheet("color: #ff4444;")
                
        except Exception as e:
            print(f"Error updating trend changes: {e}")
